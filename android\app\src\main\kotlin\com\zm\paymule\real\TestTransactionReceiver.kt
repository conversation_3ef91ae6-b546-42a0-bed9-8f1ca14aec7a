// TEST TRANSACTION RECEIVER - ANDROID NATIVE IMPLEMENTATION
// Handles ADB broadcast commands for test transactions

package com.zm.paymule.real

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.util.Log
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class TestTransactionReceiver(private val context: Context) : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "TestTransactionReceiver"
        private const val CHANNEL = "test_transaction_service"
        private const val ACTION_TEST_TRANSACTION = "com.zm.paymule.TEST_TRANSACTION"
        
        // Intent extra keys
        private const val EXTRA_RECEIVER = "receiver"
        private const val EXTRA_AMOUNT = "amount"
        private const val EXTRA_TYPE = "type"
        private const val EXTRA_REFERENCE = "reference"
        private const val EXTRA_METADATA = "metadata"
    }
    
    private var methodChannel: MethodChannel? = null
    private var isRegistered = false
    
    fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
        
        methodChannel?.setMethodCallHandler { call, result ->
            when (call.method) {
                "setupBroadcastReceiver" -> {
                    try {
                        val action = call.argument<String>("action") ?: ACTION_TEST_TRANSACTION
                        setupBroadcastReceiver(action)
                        result.success(true)
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to setup broadcast receiver", e)
                        result.error("SETUP_ERROR", "Failed to setup broadcast receiver: ${e.message}", null)
                    }
                }
                "unregisterReceiver" -> {
                    try {
                        unregisterReceiver()
                        result.success(true)
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to unregister receiver", e)
                        result.error("UNREGISTER_ERROR", "Failed to unregister receiver: ${e.message}", null)
                    }
                }
                "sendTestTransaction" -> {
                    try {
                        val receiver = call.argument<String>("receiver") ?: ""
                        val amount = call.argument<String>("amount") ?: "0"
                        val type = call.argument<String>("type") ?: "unknown"
                        val reference = call.argument<String>("reference")
                        
                        sendTestTransactionBroadcast(receiver, amount, type, reference)
                        result.success(true)
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to send test transaction", e)
                        result.error("SEND_ERROR", "Failed to send test transaction: ${e.message}", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
    
    private fun setupBroadcastReceiver(action: String) {
        if (isRegistered) {
            Log.w(TAG, "Broadcast receiver already registered")
            return
        }
        
        try {
            val intentFilter = IntentFilter(action)
            context.registerReceiver(this, intentFilter)
            isRegistered = true
            
            Log.i(TAG, "Broadcast receiver registered for action: $action")
            Log.i(TAG, "Ready to receive ADB test transaction commands")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to register broadcast receiver", e)
            throw e
        }
    }
    
    private fun unregisterReceiver() {
        if (!isRegistered) {
            Log.w(TAG, "Broadcast receiver not registered")
            return
        }
        
        try {
            context.unregisterReceiver(this)
            isRegistered = false
            Log.i(TAG, "Broadcast receiver unregistered")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to unregister broadcast receiver", e)
            throw e
        }
    }
    
    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent == null) {
            Log.w(TAG, "Received null intent")
            return
        }
        
        try {
            Log.i(TAG, "Received broadcast: ${intent.action}")
            
            when (intent.action) {
                ACTION_TEST_TRANSACTION -> {
                    handleTestTransactionBroadcast(intent)
                }
                else -> {
                    Log.w(TAG, "Unknown broadcast action: ${intent.action}")
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling broadcast", e)
        }
    }
    
    private fun handleTestTransactionBroadcast(intent: Intent) {
        try {
            // Extract transaction data from intent
            val receiver = intent.getStringExtra(EXTRA_RECEIVER) ?: ""
            val amount = intent.getStringExtra(EXTRA_AMOUNT) ?: "0"
            val type = intent.getStringExtra(EXTRA_TYPE) ?: "unknown"
            val reference = intent.getStringExtra(EXTRA_REFERENCE)
            val metadata = intent.getStringExtra(EXTRA_METADATA)
            
            Log.i(TAG, "Test transaction broadcast received:")
            Log.i(TAG, "  Receiver: $receiver")
            Log.i(TAG, "  Amount: $amount")
            Log.i(TAG, "  Type: $type")
            Log.i(TAG, "  Reference: $reference")
            
            // Validate required fields
            if (receiver.isEmpty()) {
                Log.e(TAG, "Missing required field: receiver")
                return
            }
            
            if (amount.isEmpty() || amount == "0") {
                Log.e(TAG, "Missing or invalid amount: $amount")
                return
            }
            
            // Create transaction data map
            val transactionData = mutableMapOf<String, Any?>()
            transactionData[EXTRA_RECEIVER] = receiver
            transactionData[EXTRA_AMOUNT] = amount
            transactionData[EXTRA_TYPE] = type
            
            if (reference != null) {
                transactionData[EXTRA_REFERENCE] = reference
            }
            
            if (metadata != null) {
                transactionData[EXTRA_METADATA] = parseMetadata(metadata)
            }
            
            // Add broadcast metadata
            transactionData["broadcastTime"] = System.currentTimeMillis()
            transactionData["source"] = "adb_broadcast"
            
            // Send to Flutter
            methodChannel?.invokeMethod("onTestTransactionReceived", transactionData)
            
            Log.i(TAG, "Test transaction data sent to Flutter")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling test transaction broadcast", e)
        }
    }
    
    private fun parseMetadata(metadataString: String): Map<String, Any>? {
        return try {
            // Simple key=value parsing (can be enhanced for JSON)
            val metadata = mutableMapOf<String, Any>()
            val pairs = metadataString.split(",")
            
            for (pair in pairs) {
                val keyValue = pair.split("=", limit = 2)
                if (keyValue.size == 2) {
                    metadata[keyValue[0].trim()] = keyValue[1].trim()
                }
            }
            
            if (metadata.isNotEmpty()) metadata else null
        } catch (e: Exception) {
            Log.w(TAG, "Failed to parse metadata: $metadataString", e)
            null
        }
    }
    
    private fun sendTestTransactionBroadcast(
        receiver: String,
        amount: String,
        type: String,
        reference: String?
    ) {
        try {
            val intent = Intent(ACTION_TEST_TRANSACTION)
            intent.putExtra(EXTRA_RECEIVER, receiver)
            intent.putExtra(EXTRA_AMOUNT, amount)
            intent.putExtra(EXTRA_TYPE, type)
            
            if (reference != null) {
                intent.putExtra(EXTRA_REFERENCE, reference)
            }
            
            // Add timestamp
            intent.putExtra("timestamp", System.currentTimeMillis())
            intent.putExtra("source", "internal")
            
            context.sendBroadcast(intent)
            
            Log.i(TAG, "Test transaction broadcast sent:")
            Log.i(TAG, "  Receiver: $receiver")
            Log.i(TAG, "  Amount: $amount")
            Log.i(TAG, "  Type: $type")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send test transaction broadcast", e)
            throw e
        }
    }
    
    fun isReceiverRegistered(): Boolean {
        return isRegistered
    }
    
    fun cleanup() {
        try {
            if (isRegistered) {
                unregisterReceiver()
            }
            methodChannel?.setMethodCallHandler(null)
            methodChannel = null
            Log.i(TAG, "TestTransactionReceiver cleaned up")
        } catch (e: Exception) {
            Log.e(TAG, "Error during cleanup", e)
        }
    }
}

// Extension functions for easier ADB command generation
object ADBTestCommands {
    
    fun generateMTNToAirtelCommand(receiver: String, amount: String): String {
        return """
            adb shell am broadcast -a com.zm.paymule.TEST_TRANSACTION \
            --es "receiver" "$receiver" \
            --es "amount" "$amount" \
            --es "type" "mtn_to_airtel"
        """.trimIndent()
    }
    
    fun generateAirtelToMTNCommand(receiver: String, amount: String): String {
        return """
            adb shell am broadcast -a com.zm.paymule.TEST_TRANSACTION \
            --es "receiver" "$receiver" \
            --es "amount" "$amount" \
            --es "type" "airtel_to_mtn"
        """.trimIndent()
    }
    
    fun generateMTNToZamtelCommand(receiver: String, amount: String): String {
        return """
            adb shell am broadcast -a com.zm.paymule.TEST_TRANSACTION \
            --es "receiver" "$receiver" \
            --es "amount" "$amount" \
            --es "type" "mtn_to_zamtel"
        """.trimIndent()
    }
    
    fun generateZamtelToMTNCommand(receiver: String, amount: String): String {
        return """
            adb shell am broadcast -a com.zm.paymule.TEST_TRANSACTION \
            --es "receiver" "$receiver" \
            --es "amount" "$amount" \
            --es "type" "zamtel_to_mtn"
        """.trimIndent()
    }
    
    fun generateAirtelToZamtelCommand(receiver: String, amount: String): String {
        return """
            adb shell am broadcast -a com.zm.paymule.TEST_TRANSACTION \
            --es "receiver" "$receiver" \
            --es "amount" "$amount" \
            --es "type" "airtel_to_zamtel"
        """.trimIndent()
    }
    
    fun generateZamtelToAirtelCommand(receiver: String, amount: String): String {
        return """
            adb shell am broadcast -a com.zm.paymule.TEST_TRANSACTION \
            --es "receiver" "$receiver" \
            --es "amount" "$amount" \
            --es "type" "zamtel_to_airtel"
        """.trimIndent()
    }
    
    fun generateSameNetworkCommand(receiver: String, amount: String): String {
        return """
            adb shell am broadcast -a com.zm.paymule.TEST_TRANSACTION \
            --es "receiver" "$receiver" \
            --es "amount" "$amount" \
            --es "type" "same_network"
        """.trimIndent()
    }
    
    fun generateCustomCommand(
        receiver: String,
        amount: String,
        type: String,
        reference: String? = null,
        metadata: String? = null
    ): String {
        var command = """
            adb shell am broadcast -a com.zm.paymule.TEST_TRANSACTION \
            --es "receiver" "$receiver" \
            --es "amount" "$amount" \
            --es "type" "$type"
        """.trimIndent()
        
        if (reference != null) {
            command += """ \
            --es "reference" "$reference""""
        }
        
        if (metadata != null) {
            command += """ \
            --es "metadata" "$metadata""""
        }
        
        return command
    }
}
