/// QR Scanner Service for Zambian Mobile Money
/// Optimized for low-light conditions and poor camera quality
/// Supports offline QR validation and fallback mechanisms

import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';

import '../core/constants/app_constants.dart';
import 'zambia_qr_format.dart';
import 'zambia_qr.dart';

/// QR Scanner Service optimized for Zambian conditions
class QRScannerService {
  static final QRScannerService _instance = QRScannerService._internal();
  factory QRScannerService() => _instance;
  QRScannerService._internal();

  final Logger _logger = Logger();
  MobileScannerController? _controller;
  StreamSubscription<BarcodeCapture>? _subscription;
  
  bool _isScanning = false;
  bool _isInitialized = false;
  
  // Scanner configuration for Zambian conditions
  static const ScannerConfig _config = ScannerConfig(
    detectionSpeed: DetectionSpeed.normal,
    detectionTimeoutMs: 5000,
    formats: [BarcodeFormat.qrCode],
    autoStart: false,
    useFlash: false,
    facing: CameraFacing.back,
  );

  /// Initialize scanner with permissions
  Future<ScannerInitResult> initialize() async {
    if (_isInitialized) {
      return ScannerInitResult(success: true, message: 'Already initialized');
    }

    try {
      // Request camera permission
      final permissionResult = await _requestCameraPermission();
      if (!permissionResult.granted) {
        return ScannerInitResult(
          success: false,
          error: 'PERMISSION_DENIED',
          message: permissionResult.message,
        );
      }

      // Initialize mobile scanner controller
      _controller = MobileScannerController(
        detectionSpeed: _config.detectionSpeed,
        detectionTimeoutMs: _config.detectionTimeoutMs,
        formats: _config.formats,
        autoStart: _config.autoStart,
        useFlash: _config.useFlash,
        facing: _config.facing,
      );

      _isInitialized = true;
      _logger.i('🔍 QR Scanner initialized successfully');
      
      return ScannerInitResult(
        success: true,
        message: 'Scanner initialized successfully',
      );
    } catch (e) {
      _logger.e('Scanner initialization failed: $e');
      return ScannerInitResult(
        success: false,
        error: 'INIT_FAILED',
        message: 'Failed to initialize scanner: ${e.toString()}',
      );
    }
  }

  /// Start QR scanning with callback
  Future<void> startScanning({
    required Function(QRScanResult) onScan,
    Function(String)? onError,
  }) async {
    if (!_isInitialized) {
      final initResult = await initialize();
      if (!initResult.success) {
        onError?.call(initResult.message);
        return;
      }
    }

    if (_isScanning) {
      _logger.w('Scanner already running');
      return;
    }

    try {
      _isScanning = true;
      
      // Start the scanner
      await _controller?.start();
      
      // Listen for barcode detections
      _subscription = _controller?.barcodes.listen(
        (BarcodeCapture capture) async {
          await _handleBarcodeCapture(capture, onScan, onError);
        },
        onError: (error) {
          _logger.e('Scanner error: $error');
          onError?.call('Scanner error: ${error.toString()}');
        },
      );

      _logger.i('🔍 QR scanning started');
    } catch (e) {
      _isScanning = false;
      _logger.e('Failed to start scanning: $e');
      onError?.call('Failed to start scanning: ${e.toString()}');
    }
  }

  /// Stop QR scanning
  Future<void> stopScanning() async {
    if (!_isScanning) return;

    try {
      _isScanning = false;
      await _subscription?.cancel();
      await _controller?.stop();
      
      _logger.i('🔍 QR scanning stopped');
    } catch (e) {
      _logger.e('Failed to stop scanning: $e');
    }
  }

  /// Toggle flash/torch
  Future<void> toggleFlash() async {
    try {
      await _controller?.toggleTorch();
    } catch (e) {
      _logger.e('Failed to toggle flash: $e');
    }
  }

  /// Switch camera (front/back)
  Future<void> switchCamera() async {
    try {
      await _controller?.switchCamera();
    } catch (e) {
      _logger.e('Failed to switch camera: $e');
    }
  }

  /// Get current torch state
  Future<bool> isTorchOn() async {
    try {
      return await _controller?.hasTorch() ?? false;
    } catch (e) {
      _logger.e('Failed to get torch state: $e');
      return false;
    }
  }

  /// Dispose scanner resources
  Future<void> dispose() async {
    try {
      await stopScanning();
      await _controller?.dispose();
      _controller = null;
      _isInitialized = false;
      
      _logger.i('🔍 QR Scanner disposed');
    } catch (e) {
      _logger.e('Failed to dispose scanner: $e');
    }
  }

  /// Handle barcode capture and validation
  Future<void> _handleBarcodeCapture(
    BarcodeCapture capture,
    Function(QRScanResult) onScan,
    Function(String)? onError,
  ) async {
    try {
      if (capture.barcodes.isEmpty) return;

      final barcode = capture.barcodes.first;
      final qrData = barcode.rawValue;
      
      if (qrData == null || qrData.isEmpty) {
        onError?.call('Empty QR code detected');
        return;
      }

      _logger.i('🔍 QR code detected: ${qrData.substring(0, 20)}...');

      // Validate QR format
      if (!ZambiaQRFormat.isValidFormat(qrData)) {
        onScan(QRScanResult(
          success: false,
          error: 'INVALID_FORMAT',
          message: 'Invalid QR code format. Please scan a valid Pay Mule QR code.',
          rawData: qrData,
        ));
        return;
      }

      // Decode QR data
      try {
        final decodedData = await ZambiaQRFormat.decode(qrData);
        
        // Validate decoded data
        if (!decodedData.isValid) {
          onScan(QRScanResult(
            success: false,
            error: 'INVALID_DATA',
            message: 'QR code contains invalid payment data.',
            rawData: qrData,
          ));
          return;
        }

        // Check if expired
        if (decodedData.isExpired) {
          onScan(QRScanResult(
            success: false,
            error: 'EXPIRED',
            message: 'QR code has expired. Please request a new one.',
            rawData: qrData,
            decodedData: decodedData,
          ));
          return;
        }

        // Successful scan
        onScan(QRScanResult(
          success: true,
          message: 'QR code scanned successfully',
          rawData: qrData,
          decodedData: decodedData,
        ));

        // Provide haptic feedback
        await _provideFeedback();
        
      } catch (e) {
        _logger.e('QR decoding failed: $e');
        onScan(QRScanResult(
          success: false,
          error: 'DECODE_FAILED',
          message: 'Failed to decode QR code: ${e.toString()}',
          rawData: qrData,
        ));
      }
    } catch (e) {
      _logger.e('Barcode capture handling failed: $e');
      onError?.call('Failed to process QR code: ${e.toString()}');
    }
  }

  /// Request camera permission
  Future<PermissionResult> _requestCameraPermission() async {
    try {
      final status = await Permission.camera.status;
      
      if (status.isGranted) {
        return PermissionResult(
          granted: true,
          message: 'Camera permission already granted',
        );
      }

      if (status.isDenied) {
        final result = await Permission.camera.request();
        
        if (result.isGranted) {
          return PermissionResult(
            granted: true,
            message: 'Camera permission granted',
          );
        } else if (result.isPermanentlyDenied) {
          return PermissionResult(
            granted: false,
            message: 'Camera permission permanently denied. Please enable in settings.',
          );
        } else {
          return PermissionResult(
            granted: false,
            message: 'Camera permission denied',
          );
        }
      }

      if (status.isPermanentlyDenied) {
        return PermissionResult(
          granted: false,
          message: 'Camera permission permanently denied. Please enable in settings.',
        );
      }

      return PermissionResult(
        granted: false,
        message: 'Camera permission not available',
      );
    } catch (e) {
      _logger.e('Permission request failed: $e');
      return PermissionResult(
        granted: false,
        message: 'Failed to request camera permission: ${e.toString()}',
      );
    }
  }

  /// Provide haptic and audio feedback
  Future<void> _provideFeedback() async {
    try {
      // Haptic feedback
      await HapticFeedback.lightImpact();
      
      // Audio feedback (optional)
      await SystemSound.play(SystemSoundType.click);
    } catch (e) {
      _logger.e('Failed to provide feedback: $e');
    }
  }

  /// Get scanner widget for UI
  Widget getScannerWidget({
    required Function(QRScanResult) onScan,
    Function(String)? onError,
    Widget? overlay,
  }) {
    if (!_isInitialized || _controller == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Stack(
      children: [
        MobileScanner(
          controller: _controller,
          onDetect: (BarcodeCapture capture) async {
            await _handleBarcodeCapture(capture, onScan, onError);
          },
        ),
        if (overlay != null) overlay,
      ],
    );
  }

  /// Check if scanner is currently active
  bool get isScanning => _isScanning;
  
  /// Check if scanner is initialized
  bool get isInitialized => _isInitialized;
}

/// Scanner configuration
class ScannerConfig {
  final DetectionSpeed detectionSpeed;
  final int detectionTimeoutMs;
  final List<BarcodeFormat> formats;
  final bool autoStart;
  final bool useFlash;
  final CameraFacing facing;

  const ScannerConfig({
    required this.detectionSpeed,
    required this.detectionTimeoutMs,
    required this.formats,
    required this.autoStart,
    required this.useFlash,
    required this.facing,
  });
}

/// Scanner initialization result
class ScannerInitResult {
  final bool success;
  final String? error;
  final String message;

  ScannerInitResult({
    required this.success,
    this.error,
    required this.message,
  });
}

/// Permission request result
class PermissionResult {
  final bool granted;
  final String message;

  PermissionResult({
    required this.granted,
    required this.message,
  });
}

/// QR scan result
class QRScanResult {
  final bool success;
  final String? error;
  final String message;
  final String rawData;
  final ZambiaQRData? decodedData;

  QRScanResult({
    required this.success,
    this.error,
    required this.message,
    required this.rawData,
    this.decodedData,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'error': error,
      'message': message,
      'rawData': rawData,
      'decodedData': decodedData?.toJson(),
    };
  }
}
