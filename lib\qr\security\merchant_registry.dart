/// Merchant Registry for Anti-Fraud Protection
/// Maintains verified merchant database for Zambian QR payments
/// Implements BoZ compliance and fraud prevention measures

import 'dart:convert';
import 'package:logger/logger.dart';
import 'package:crypto/crypto.dart';

import '../../core/constants/app_constants.dart';
import '../../core/security/encryption_service.dart';
import '../../data/database/database_helper.dart';

/// Merchant verification status
enum MerchantStatus {
  PENDING,
  VERIFIED,
  SUSPENDED,
  BLACKLISTED,
  EXPIRED,
}

/// Merchant Registry for fraud prevention
class MerchantRegistry {
  static final MerchantRegistry _instance = MerchantRegistry._internal();
  factory MerchantRegistry() => _instance;
  MerchantRegistry._internal();

  static final Logger _logger = Logger();
  static final DatabaseHelper _dbHelper = DatabaseHelper();
  static final EncryptionService _encryption = EncryptionService();

  static bool _isInitialized = false;
  static final Map<String, MerchantRecord> _cache = {};

  /// Initialize merchant registry
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _encryption.initialize();
      await _createRegistryTables();
      await _loadMerchantCache();
      
      _isInitialized = true;
      _logger.i('🏢 Merchant registry initialized');
    } catch (e) {
      _logger.e('Failed to initialize merchant registry: $e');
      rethrow;
    }
  }

  /// Check if merchant is valid and verified
  static Future<bool> isValid(String merchantId) async {
    try {
      if (!_isInitialized) await initialize();

      // Check cache first
      if (_cache.containsKey(merchantId)) {
        final merchant = _cache[merchantId]!;
        return _isMerchantValid(merchant);
      }

      // Load from database
      final merchant = await _getMerchantRecord(merchantId);
      if (merchant == null) {
        _logger.w('🚫 Merchant not found: ${merchantId.substring(0, 8)}...');
        return false;
      }

      // Cache the result
      _cache[merchantId] = merchant;
      
      final isValid = _isMerchantValid(merchant);
      
      if (!isValid) {
        _logger.w('🚫 Invalid merchant: ${merchantId.substring(0, 8)}... (${merchant.status.name})');
      }

      return isValid;
    } catch (e) {
      _logger.e('Merchant validation failed: $e');
      return false;
    }
  }

  /// Register new merchant
  static Future<MerchantRegistrationResult> registerMerchant({
    required String merchantId,
    required String businessName,
    required String phoneNumber,
    required String category,
    required String location,
    String? description,
    Map<String, dynamic>? businessDetails,
    List<String>? verificationDocuments,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      // Check if merchant already exists
      final existing = await _getMerchantRecord(merchantId);
      if (existing != null) {
        return MerchantRegistrationResult(
          success: false,
          error: 'MERCHANT_EXISTS',
          message: 'Merchant already registered',
        );
      }

      // Validate business information
      final validation = await _validateBusinessInfo(
        businessName: businessName,
        phoneNumber: phoneNumber,
        category: category,
        location: location,
      );

      if (!validation.isValid) {
        return MerchantRegistrationResult(
          success: false,
          error: validation.error,
          message: validation.message,
        );
      }

      // Create merchant record
      final merchant = MerchantRecord(
        merchantId: merchantId,
        businessName: businessName,
        phoneNumber: phoneNumber,
        category: category,
        location: location,
        description: description,
        status: MerchantStatus.PENDING,
        registeredAt: DateTime.now(),
        lastVerifiedAt: null,
        verificationLevel: 1,
        riskScore: 0.0,
        businessDetails: businessDetails ?? {},
        verificationDocuments: verificationDocuments ?? [],
      );

      // Store in database
      await _storeMerchantRecord(merchant);
      
      // Cache the merchant
      _cache[merchantId] = merchant;

      _logger.i('🏢 Merchant registered: ${businessName} (${merchantId.substring(0, 8)}...)');

      return MerchantRegistrationResult(
        success: true,
        merchantId: merchantId,
        message: 'Merchant registered successfully. Verification pending.',
      );
    } catch (e) {
      _logger.e('Merchant registration failed: $e');
      return MerchantRegistrationResult(
        success: false,
        error: 'REGISTRATION_FAILED',
        message: 'Failed to register merchant: ${e.toString()}',
      );
    }
  }

  /// Verify merchant (admin function)
  static Future<bool> verifyMerchant({
    required String merchantId,
    required int verificationLevel,
    String? verificationNotes,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      final merchant = await _getMerchantRecord(merchantId);
      if (merchant == null) return false;

      // Update merchant status
      final updatedMerchant = merchant.copyWith(
        status: MerchantStatus.VERIFIED,
        lastVerifiedAt: DateTime.now(),
        verificationLevel: verificationLevel,
        verificationNotes: verificationNotes,
      );

      await _storeMerchantRecord(updatedMerchant);
      _cache[merchantId] = updatedMerchant;

      _logger.i('✅ Merchant verified: ${merchantId.substring(0, 8)}... (Level $verificationLevel)');
      return true;
    } catch (e) {
      _logger.e('Merchant verification failed: $e');
      return false;
    }
  }

  /// Suspend merchant
  static Future<bool> suspendMerchant({
    required String merchantId,
    required String reason,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      final merchant = await _getMerchantRecord(merchantId);
      if (merchant == null) return false;

      final updatedMerchant = merchant.copyWith(
        status: MerchantStatus.SUSPENDED,
        suspensionReason: reason,
        suspendedAt: DateTime.now(),
      );

      await _storeMerchantRecord(updatedMerchant);
      _cache[merchantId] = updatedMerchant;

      _logger.w('⚠️ Merchant suspended: ${merchantId.substring(0, 8)}... ($reason)');
      return true;
    } catch (e) {
      _logger.e('Merchant suspension failed: $e');
      return false;
    }
  }

  /// Blacklist merchant
  static Future<bool> blacklistMerchant({
    required String merchantId,
    required String reason,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      final merchant = await _getMerchantRecord(merchantId);
      if (merchant == null) return false;

      final updatedMerchant = merchant.copyWith(
        status: MerchantStatus.BLACKLISTED,
        blacklistReason: reason,
        blacklistedAt: DateTime.now(),
      );

      await _storeMerchantRecord(updatedMerchant);
      _cache[merchantId] = updatedMerchant;

      _logger.e('🚫 Merchant blacklisted: ${merchantId.substring(0, 8)}... ($reason)');
      return true;
    } catch (e) {
      _logger.e('Merchant blacklisting failed: $e');
      return false;
    }
  }

  /// Get merchant information
  static Future<MerchantRecord?> getMerchantInfo(String merchantId) async {
    try {
      if (!_isInitialized) await initialize();

      // Check cache first
      if (_cache.containsKey(merchantId)) {
        return _cache[merchantId];
      }

      // Load from database
      final merchant = await _getMerchantRecord(merchantId);
      if (merchant != null) {
        _cache[merchantId] = merchant;
      }

      return merchant;
    } catch (e) {
      _logger.e('Failed to get merchant info: $e');
      return null;
    }
  }

  /// Update merchant risk score
  static Future<void> updateRiskScore(String merchantId, double riskScore) async {
    try {
      if (!_isInitialized) await initialize();

      final merchant = await _getMerchantRecord(merchantId);
      if (merchant == null) return;

      final updatedMerchant = merchant.copyWith(riskScore: riskScore);
      await _storeMerchantRecord(updatedMerchant);
      _cache[merchantId] = updatedMerchant;

      if (riskScore > 0.8) {
        _logger.w('⚠️ High risk merchant: ${merchantId.substring(0, 8)}... (Score: ${riskScore.toStringAsFixed(2)})');
      }
    } catch (e) {
      _logger.e('Failed to update risk score: $e');
    }
  }

  /// Get merchants by status
  static Future<List<MerchantRecord>> getMerchantsByStatus(MerchantStatus status) async {
    try {
      if (!_isInitialized) await initialize();

      final results = await _dbHelper.query(
        'merchant_registry',
        where: 'status = ?',
        whereArgs: [status.name],
        orderBy: 'registered_at DESC',
      );

      final merchants = <MerchantRecord>[];
      for (final result in results) {
        try {
          final merchant = await _parseMerchantRecord(result);
          merchants.add(merchant);
        } catch (e) {
          _logger.e('Failed to parse merchant record: $e');
        }
      }

      return merchants;
    } catch (e) {
      _logger.e('Failed to get merchants by status: $e');
      return [];
    }
  }

  /// Clear cache
  static void clearCache() {
    _cache.clear();
    _logger.i('🧹 Merchant registry cache cleared');
  }

  /// Check if merchant is valid
  static bool _isMerchantValid(MerchantRecord merchant) {
    // Check status
    if (merchant.status != MerchantStatus.VERIFIED) {
      return false;
    }

    // Check if verification has expired (1 year)
    if (merchant.lastVerifiedAt != null) {
      final daysSinceVerification = DateTime.now().difference(merchant.lastVerifiedAt!).inDays;
      if (daysSinceVerification > 365) {
        return false;
      }
    }

    // Check risk score
    if (merchant.riskScore > 0.9) {
      return false;
    }

    return true;
  }

  /// Create database tables
  static Future<void> _createRegistryTables() async {
    final db = await _dbHelper.database;

    await db.execute('''
      CREATE TABLE IF NOT EXISTS merchant_registry (
        merchant_id TEXT PRIMARY KEY,
        business_name TEXT NOT NULL,
        phone_number TEXT NOT NULL,
        category TEXT NOT NULL,
        location TEXT NOT NULL,
        description TEXT,
        status TEXT NOT NULL,
        registered_at INTEGER NOT NULL,
        last_verified_at INTEGER,
        verification_level INTEGER DEFAULT 1,
        risk_score REAL DEFAULT 0.0,
        business_details TEXT,
        verification_documents TEXT,
        verification_notes TEXT,
        suspension_reason TEXT,
        suspended_at INTEGER,
        blacklist_reason TEXT,
        blacklisted_at INTEGER
      )
    ''');

    // Create indexes
    await db.execute('CREATE INDEX IF NOT EXISTS idx_merchant_status ON merchant_registry(status)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_merchant_phone ON merchant_registry(phone_number)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_merchant_category ON merchant_registry(category)');
  }

  /// Load merchant cache
  static Future<void> _loadMerchantCache() async {
    try {
      // Load verified merchants into cache
      final verifiedMerchants = await getMerchantsByStatus(MerchantStatus.VERIFIED);
      for (final merchant in verifiedMerchants) {
        _cache[merchant.merchantId] = merchant;
      }

      _logger.i('📦 Loaded ${_cache.length} verified merchants into cache');
    } catch (e) {
      _logger.e('Failed to load merchant cache: $e');
    }
  }

  /// Get merchant record from database
  static Future<MerchantRecord?> _getMerchantRecord(String merchantId) async {
    try {
      final results = await _dbHelper.query(
        'merchant_registry',
        where: 'merchant_id = ?',
        whereArgs: [merchantId],
        limit: 1,
      );

      if (results.isEmpty) return null;

      return await _parseMerchantRecord(results.first);
    } catch (e) {
      _logger.e('Failed to get merchant record: $e');
      return null;
    }
  }

  /// Store merchant record in database
  static Future<void> _storeMerchantRecord(MerchantRecord merchant) async {
    final data = {
      'merchant_id': merchant.merchantId,
      'business_name': merchant.businessName,
      'phone_number': merchant.phoneNumber,
      'category': merchant.category,
      'location': merchant.location,
      'description': merchant.description,
      'status': merchant.status.name,
      'registered_at': merchant.registeredAt.millisecondsSinceEpoch,
      'last_verified_at': merchant.lastVerifiedAt?.millisecondsSinceEpoch,
      'verification_level': merchant.verificationLevel,
      'risk_score': merchant.riskScore,
      'business_details': jsonEncode(merchant.businessDetails),
      'verification_documents': jsonEncode(merchant.verificationDocuments),
      'verification_notes': merchant.verificationNotes,
      'suspension_reason': merchant.suspensionReason,
      'suspended_at': merchant.suspendedAt?.millisecondsSinceEpoch,
      'blacklist_reason': merchant.blacklistReason,
      'blacklisted_at': merchant.blacklistedAt?.millisecondsSinceEpoch,
    };

    await _dbHelper.insertOrUpdate('merchant_registry', data, 'merchant_id');
  }

  /// Parse merchant record from database result
  static Future<MerchantRecord> _parseMerchantRecord(Map<String, dynamic> data) async {
    return MerchantRecord(
      merchantId: data['merchant_id'] as String,
      businessName: data['business_name'] as String,
      phoneNumber: data['phone_number'] as String,
      category: data['category'] as String,
      location: data['location'] as String,
      description: data['description'] as String?,
      status: MerchantStatus.values.firstWhere(
        (s) => s.name == data['status'],
        orElse: () => MerchantStatus.PENDING,
      ),
      registeredAt: DateTime.fromMillisecondsSinceEpoch(data['registered_at'] as int),
      lastVerifiedAt: data['last_verified_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['last_verified_at'] as int)
          : null,
      verificationLevel: data['verification_level'] as int? ?? 1,
      riskScore: (data['risk_score'] as num?)?.toDouble() ?? 0.0,
      businessDetails: data['business_details'] != null
          ? jsonDecode(data['business_details'] as String) as Map<String, dynamic>
          : {},
      verificationDocuments: data['verification_documents'] != null
          ? List<String>.from(jsonDecode(data['verification_documents'] as String))
          : [],
      verificationNotes: data['verification_notes'] as String?,
      suspensionReason: data['suspension_reason'] as String?,
      suspendedAt: data['suspended_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['suspended_at'] as int)
          : null,
      blacklistReason: data['blacklist_reason'] as String?,
      blacklistedAt: data['blacklisted_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['blacklisted_at'] as int)
          : null,
    );
  }

  /// Validate business information
  static Future<BusinessValidation> _validateBusinessInfo({
    required String businessName,
    required String phoneNumber,
    required String category,
    required String location,
  }) async {
    // Validate business name
    if (businessName.trim().length < 3) {
      return BusinessValidation(
        isValid: false,
        error: 'INVALID_BUSINESS_NAME',
        message: 'Business name must be at least 3 characters',
      );
    }

    // Validate phone number format
    if (!_isValidZambianPhone(phoneNumber)) {
      return BusinessValidation(
        isValid: false,
        error: 'INVALID_PHONE',
        message: 'Invalid Zambian phone number format',
      );
    }

    // Check for duplicate phone number
    final existingMerchants = await _dbHelper.query(
      'merchant_registry',
      where: 'phone_number = ?',
      whereArgs: [phoneNumber],
    );

    if (existingMerchants.isNotEmpty) {
      return BusinessValidation(
        isValid: false,
        error: 'PHONE_EXISTS',
        message: 'Phone number already registered',
      );
    }

    return BusinessValidation(
      isValid: true,
      message: 'Business information is valid',
    );
  }

  /// Validate Zambian phone number
  static bool _isValidZambianPhone(String phone) {
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    // Zambian phone patterns
    if (cleanPhone.startsWith('260') && cleanPhone.length == 12) return true;
    if (cleanPhone.startsWith('0') && cleanPhone.length == 10) return true;
    if (cleanPhone.length == 9) return true;
    
    return false;
  }
}

/// Merchant record model
class MerchantRecord {
  final String merchantId;
  final String businessName;
  final String phoneNumber;
  final String category;
  final String location;
  final String? description;
  final MerchantStatus status;
  final DateTime registeredAt;
  final DateTime? lastVerifiedAt;
  final int verificationLevel;
  final double riskScore;
  final Map<String, dynamic> businessDetails;
  final List<String> verificationDocuments;
  final String? verificationNotes;
  final String? suspensionReason;
  final DateTime? suspendedAt;
  final String? blacklistReason;
  final DateTime? blacklistedAt;

  MerchantRecord({
    required this.merchantId,
    required this.businessName,
    required this.phoneNumber,
    required this.category,
    required this.location,
    this.description,
    required this.status,
    required this.registeredAt,
    this.lastVerifiedAt,
    required this.verificationLevel,
    required this.riskScore,
    required this.businessDetails,
    required this.verificationDocuments,
    this.verificationNotes,
    this.suspensionReason,
    this.suspendedAt,
    this.blacklistReason,
    this.blacklistedAt,
  });

  MerchantRecord copyWith({
    MerchantStatus? status,
    DateTime? lastVerifiedAt,
    int? verificationLevel,
    double? riskScore,
    String? verificationNotes,
    String? suspensionReason,
    DateTime? suspendedAt,
    String? blacklistReason,
    DateTime? blacklistedAt,
  }) {
    return MerchantRecord(
      merchantId: merchantId,
      businessName: businessName,
      phoneNumber: phoneNumber,
      category: category,
      location: location,
      description: description,
      status: status ?? this.status,
      registeredAt: registeredAt,
      lastVerifiedAt: lastVerifiedAt ?? this.lastVerifiedAt,
      verificationLevel: verificationLevel ?? this.verificationLevel,
      riskScore: riskScore ?? this.riskScore,
      businessDetails: businessDetails,
      verificationDocuments: verificationDocuments,
      verificationNotes: verificationNotes ?? this.verificationNotes,
      suspensionReason: suspensionReason ?? this.suspensionReason,
      suspendedAt: suspendedAt ?? this.suspendedAt,
      blacklistReason: blacklistReason ?? this.blacklistReason,
      blacklistedAt: blacklistedAt ?? this.blacklistedAt,
    );
  }
}

/// Merchant registration result
class MerchantRegistrationResult {
  final bool success;
  final String? merchantId;
  final String? error;
  final String message;

  MerchantRegistrationResult({
    required this.success,
    this.merchantId,
    this.error,
    required this.message,
  });
}

/// Business validation result
class BusinessValidation {
  final bool isValid;
  final String? error;
  final String message;

  BusinessValidation({
    required this.isValid,
    this.error,
    required this.message,
  });
}
