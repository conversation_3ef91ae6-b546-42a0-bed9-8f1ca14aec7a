/// QR Payment Security Service for Zambian Mobile Money
/// Implements fraud detection, transaction limits, and PIN verification
/// Compliant with Bank of Zambia security requirements

import 'dart:convert';
import 'package:logger/logger.dart';
import 'package:crypto/crypto.dart';

import '../../core/constants/app_constants.dart';
import '../../core/config/app_config.dart';
import '../../core/security/encryption_service.dart';
import '../../data/database/database_helper.dart';
import '../../features/auth/data/services/pin_service.dart';
import '../zambia_qr_format.dart';

/// QR Payment Security Service
class QRSecurityService {
  static final QRSecurityService _instance = QRSecurityService._internal();
  factory QRSecurityService() => _instance;
  QRSecurityService._internal();

  final Logger _logger = Logger();
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final EncryptionService _encryption = EncryptionService();
  final PINService _pinService = PINService();

  bool _isInitialized = false;

  /// Initialize security service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _encryption.initialize();
      await _createSecurityTables();
      _isInitialized = true;
      
      _logger.i('🔒 QR Security service initialized');
    } catch (e) {
      _logger.e('Failed to initialize QR security service: $e');
      rethrow;
    }
  }

  /// Verify transaction PIN for QR payment
  Future<PINVerificationResult> verifyTransactionPIN({
    required String userId,
    required String pin,
    required double amount,
    required String merchantId,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      // Log PIN verification attempt
      await _logSecurityEvent(
        userId: userId,
        eventType: 'PIN_VERIFICATION_ATTEMPT',
        details: {
          'amount': amount,
          'merchant_id': merchantId.substring(0, 8),
        },
      );

      // Check for recent failed attempts
      final recentFailures = await _getRecentFailedAttempts(userId);
      if (recentFailures >= AppConstants.maxPinAttempts) {
        await _logSecurityEvent(
          userId: userId,
          eventType: 'PIN_LOCKED',
          details: {'reason': 'Too many failed attempts'},
        );

        return PINVerificationResult(
          success: false,
          error: 'PIN_LOCKED',
          message: 'PIN locked due to too many failed attempts',
          attemptsRemaining: 0,
        );
      }

      // Verify PIN with PIN service
      final pinResult = await _pinService.verifyPIN(
        userId: userId,
        pin: pin,
      );

      if (pinResult.success) {
        // Clear failed attempts on success
        await _clearFailedAttempts(userId);
        
        await _logSecurityEvent(
          userId: userId,
          eventType: 'PIN_VERIFICATION_SUCCESS',
          details: {'amount': amount},
        );

        return PINVerificationResult(
          success: true,
          message: 'PIN verified successfully',
          attemptsRemaining: AppConstants.maxPinAttempts,
        );
      } else {
        // Record failed attempt
        await _recordFailedAttempt(userId);
        
        await _logSecurityEvent(
          userId: userId,
          eventType: 'PIN_VERIFICATION_FAILED',
          details: {'attempts_remaining': AppConstants.maxPinAttempts - recentFailures - 1},
        );

        return PINVerificationResult(
          success: false,
          error: 'INVALID_PIN',
          message: 'Invalid PIN',
          attemptsRemaining: AppConstants.maxPinAttempts - recentFailures - 1,
        );
      }
    } catch (e) {
      _logger.e('PIN verification failed: $e');
      return PINVerificationResult(
        success: false,
        error: 'VERIFICATION_ERROR',
        message: 'PIN verification failed',
        attemptsRemaining: 0,
      );
    }
  }

  /// Validate transaction limits
  Future<TransactionLimitResult> validateTransactionLimits({
    required String userId,
    required double amount,
    required String transactionType,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      // Check minimum amount
      if (amount < AppConstants.minTransactionAmount) {
        return TransactionLimitResult(
          isValid: false,
          error: 'AMOUNT_TOO_LOW',
          message: 'Amount below minimum limit of K${AppConstants.minTransactionAmount}',
        );
      }

      // Check maximum single transaction amount
      if (amount > AppConstants.maxTransactionAmount) {
        return TransactionLimitResult(
          isValid: false,
          error: 'AMOUNT_TOO_HIGH',
          message: 'Amount exceeds maximum limit of K${AppConstants.maxTransactionAmount}',
        );
      }

      // Check daily limits
      final dailyTotal = await _getDailyTransactionTotal(userId);
      if (dailyTotal + amount > AppConstants.maxDailyTransactionAmount) {
        return TransactionLimitResult(
          isValid: false,
          error: 'DAILY_LIMIT_EXCEEDED',
          message: 'Daily transaction limit exceeded',
          currentDailyTotal: dailyTotal,
          dailyLimit: AppConstants.maxDailyTransactionAmount,
        );
      }

      // Check monthly limits
      final monthlyTotal = await _getMonthlyTransactionTotal(userId);
      if (monthlyTotal + amount > AppConstants.maxMonthlyTransactionAmount) {
        return TransactionLimitResult(
          isValid: false,
          error: 'MONTHLY_LIMIT_EXCEEDED',
          message: 'Monthly transaction limit exceeded',
          currentMonthlyTotal: monthlyTotal,
          monthlyLimit: AppConstants.maxMonthlyTransactionAmount,
        );
      }

      await _logSecurityEvent(
        userId: userId,
        eventType: 'TRANSACTION_LIMIT_CHECK_PASSED',
        details: {
          'amount': amount,
          'daily_total': dailyTotal,
          'monthly_total': monthlyTotal,
        },
      );

      return TransactionLimitResult(
        isValid: true,
        message: 'Transaction limits validated',
        currentDailyTotal: dailyTotal,
        currentMonthlyTotal: monthlyTotal,
        dailyLimit: AppConstants.maxDailyTransactionAmount,
        monthlyLimit: AppConstants.maxMonthlyTransactionAmount,
      );
    } catch (e) {
      _logger.e('Transaction limit validation failed: $e');
      return TransactionLimitResult(
        isValid: false,
        error: 'VALIDATION_ERROR',
        message: 'Failed to validate transaction limits',
      );
    }
  }

  /// Detect fraudulent QR payment patterns
  Future<FraudDetectionResult> detectFraud({
    required String userId,
    required ZambiaQRData qrData,
    required String deviceInfo,
    String? locationData,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      final fraudScore = await _calculateFraudScore(
        userId: userId,
        qrData: qrData,
        deviceInfo: deviceInfo,
        locationData: locationData,
      );

      final riskLevel = _determineRiskLevel(fraudScore);
      
      await _logSecurityEvent(
        userId: userId,
        eventType: 'FRAUD_DETECTION_CHECK',
        details: {
          'fraud_score': fraudScore,
          'risk_level': riskLevel,
          'merchant_id': qrData.merchantId.substring(0, 8),
          'amount': qrData.amount,
        },
      );

      if (riskLevel == 'HIGH') {
        return FraudDetectionResult(
          isBlocked: true,
          riskLevel: riskLevel,
          fraudScore: fraudScore,
          reason: 'High fraud risk detected',
          requiresAdditionalVerification: true,
        );
      } else if (riskLevel == 'MEDIUM') {
        return FraudDetectionResult(
          isBlocked: false,
          riskLevel: riskLevel,
          fraudScore: fraudScore,
          reason: 'Medium fraud risk detected',
          requiresAdditionalVerification: true,
        );
      } else {
        return FraudDetectionResult(
          isBlocked: false,
          riskLevel: riskLevel,
          fraudScore: fraudScore,
          reason: 'Low fraud risk',
          requiresAdditionalVerification: false,
        );
      }
    } catch (e) {
      _logger.e('Fraud detection failed: $e');
      return FraudDetectionResult(
        isBlocked: true,
        riskLevel: 'HIGH',
        fraudScore: 100,
        reason: 'Fraud detection system error',
        requiresAdditionalVerification: true,
      );
    }
  }

  /// Validate QR code integrity and authenticity
  Future<QRValidationResult> validateQRCode(String qrData) async {
    try {
      if (!_isInitialized) await initialize();

      // Check QR format
      if (!ZambiaQRFormat.isValidFormat(qrData)) {
        return QRValidationResult(
          isValid: false,
          error: 'INVALID_FORMAT',
          message: 'Invalid QR code format',
        );
      }

      // Decode and validate
      final decodedData = await ZambiaQRFormat.decode(qrData);
      
      // Check expiry
      if (decodedData.isExpired) {
        return QRValidationResult(
          isValid: false,
          error: 'QR_EXPIRED',
          message: 'QR code has expired',
        );
      }

      // Check data integrity
      if (!decodedData.isValid) {
        return QRValidationResult(
          isValid: false,
          error: 'INVALID_DATA',
          message: 'QR code contains invalid data',
        );
      }

      // Check if QR has been used too many times (replay attack prevention)
      final usageCount = await _getQRUsageCount(qrData);
      if (usageCount > AppConstants.maxQRUsageCount) {
        return QRValidationResult(
          isValid: false,
          error: 'QR_OVERUSED',
          message: 'QR code has been used too many times',
        );
      }

      return QRValidationResult(
        isValid: true,
        message: 'QR code is valid',
        decodedData: decodedData,
      );
    } catch (e) {
      _logger.e('QR validation failed: $e');
      return QRValidationResult(
        isValid: false,
        error: 'VALIDATION_ERROR',
        message: 'QR code validation failed',
      );
    }
  }

  /// Record successful QR transaction for security tracking
  Future<void> recordSuccessfulTransaction({
    required String userId,
    required String transactionId,
    required ZambiaQRData qrData,
    required String deviceInfo,
    String? locationData,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      final transactionRecord = {
        'id': transactionId,
        'user_id': userId,
        'merchant_id': qrData.merchantId,
        'amount': qrData.amount,
        'currency': qrData.currency,
        'provider': qrData.provider,
        'device_info': deviceInfo,
        'location_data': locationData,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('qr_transaction_security_log', transactionRecord);

      await _logSecurityEvent(
        userId: userId,
        eventType: 'QR_TRANSACTION_SUCCESS',
        details: {
          'transaction_id': transactionId,
          'amount': qrData.amount,
          'merchant_id': qrData.merchantId.substring(0, 8),
        },
      );
    } catch (e) {
      _logger.e('Failed to record successful transaction: $e');
    }
  }

  /// Create security-related database tables
  Future<void> _createSecurityTables() async {
    final db = await _dbHelper.database;

    // PIN attempt tracking table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS qr_pin_attempts (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        attempt_type TEXT NOT NULL,
        success INTEGER NOT NULL,
        timestamp INTEGER NOT NULL,
        device_info TEXT,
        ip_address TEXT
      )
    ''');

    // Transaction security log table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS qr_transaction_security_log (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        merchant_id TEXT NOT NULL,
        amount REAL NOT NULL,
        currency TEXT NOT NULL,
        provider TEXT,
        device_info TEXT,
        location_data TEXT,
        timestamp INTEGER NOT NULL
      )
    ''');

    // Security events table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS qr_security_events (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        event_type TEXT NOT NULL,
        details TEXT,
        timestamp INTEGER NOT NULL,
        severity TEXT DEFAULT 'INFO'
      )
    ''');

    // QR usage tracking table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS qr_usage_tracking (
        id TEXT PRIMARY KEY,
        qr_hash TEXT NOT NULL UNIQUE,
        usage_count INTEGER DEFAULT 0,
        first_used_at INTEGER,
        last_used_at INTEGER
      )
    ''');
  }

  /// Calculate fraud score based on various factors
  Future<double> _calculateFraudScore({
    required String userId,
    required ZambiaQRData qrData,
    required String deviceInfo,
    String? locationData,
  }) async {
    double score = 0.0;

    // Check transaction amount patterns
    final recentTransactions = await _getRecentTransactions(userId, 24);
    if (recentTransactions.isNotEmpty) {
      final avgAmount = recentTransactions.map((t) => t['amount'] as double).reduce((a, b) => a + b) / recentTransactions.length;
      if (qrData.amount > avgAmount * 5) {
        score += 30; // Unusual amount
      }
    }

    // Check transaction frequency
    final recentCount = await _getRecentTransactionCount(userId, 1);
    if (recentCount > 10) {
      score += 25; // Too many transactions in short time
    }

    // Check merchant patterns
    final merchantTransactions = await _getMerchantTransactionHistory(userId, qrData.merchantId);
    if (merchantTransactions.isEmpty) {
      score += 15; // First time with this merchant
    }

    // Check time patterns
    final hour = DateTime.now().hour;
    if (hour < 6 || hour > 22) {
      score += 10; // Unusual time
    }

    // Check device consistency
    final lastDeviceInfo = await _getLastDeviceInfo(userId);
    if (lastDeviceInfo != null && lastDeviceInfo != deviceInfo) {
      score += 20; // Different device
    }

    return score.clamp(0.0, 100.0);
  }

  /// Determine risk level based on fraud score
  String _determineRiskLevel(double fraudScore) {
    if (fraudScore >= 70) return 'HIGH';
    if (fraudScore >= 40) return 'MEDIUM';
    return 'LOW';
  }

  /// Get recent failed PIN attempts
  Future<int> _getRecentFailedAttempts(String userId) async {
    final attempts = await _dbHelper.query(
      'qr_pin_attempts',
      where: 'user_id = ? AND success = 0 AND timestamp > ?',
      whereArgs: [
        userId,
        DateTime.now().subtract(const Duration(hours: 1)).millisecondsSinceEpoch,
      ],
    );
    return attempts.length;
  }

  /// Record failed PIN attempt
  Future<void> _recordFailedAttempt(String userId) async {
    final attempt = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'user_id': userId,
      'attempt_type': 'QR_PAYMENT',
      'success': 0,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    await _dbHelper.insert('qr_pin_attempts', attempt);
  }

  /// Clear failed attempts
  Future<void> _clearFailedAttempts(String userId) async {
    await _dbHelper.delete(
      'qr_pin_attempts',
      where: 'user_id = ? AND success = 0',
      whereArgs: [userId],
    );
  }

  /// Get daily transaction total
  Future<double> _getDailyTransactionTotal(String userId) async {
    final startOfDay = DateTime.now().copyWith(hour: 0, minute: 0, second: 0, millisecond: 0);
    
    final transactions = await _dbHelper.query(
      'qr_transaction_security_log',
      where: 'user_id = ? AND timestamp >= ?',
      whereArgs: [userId, startOfDay.millisecondsSinceEpoch],
    );

    return transactions.fold(0.0, (sum, t) => sum + (t['amount'] as double));
  }

  /// Get monthly transaction total
  Future<double> _getMonthlyTransactionTotal(String userId) async {
    final startOfMonth = DateTime.now().copyWith(day: 1, hour: 0, minute: 0, second: 0, millisecond: 0);
    
    final transactions = await _dbHelper.query(
      'qr_transaction_security_log',
      where: 'user_id = ? AND timestamp >= ?',
      whereArgs: [userId, startOfMonth.millisecondsSinceEpoch],
    );

    return transactions.fold(0.0, (sum, t) => sum + (t['amount'] as double));
  }

  /// Get QR usage count
  Future<int> _getQRUsageCount(String qrData) async {
    final qrHash = sha256.convert(utf8.encode(qrData)).toString();
    
    final usage = await _dbHelper.query(
      'qr_usage_tracking',
      where: 'qr_hash = ?',
      whereArgs: [qrHash],
      limit: 1,
    );

    if (usage.isNotEmpty) {
      return usage.first['usage_count'] as int;
    }
    return 0;
  }

  /// Get recent transactions
  Future<List<Map<String, dynamic>>> _getRecentTransactions(String userId, int hours) async {
    final since = DateTime.now().subtract(Duration(hours: hours)).millisecondsSinceEpoch;
    
    return await _dbHelper.query(
      'qr_transaction_security_log',
      where: 'user_id = ? AND timestamp >= ?',
      whereArgs: [userId, since],
      orderBy: 'timestamp DESC',
    );
  }

  /// Get recent transaction count
  Future<int> _getRecentTransactionCount(String userId, int hours) async {
    final transactions = await _getRecentTransactions(userId, hours);
    return transactions.length;
  }

  /// Get merchant transaction history
  Future<List<Map<String, dynamic>>> _getMerchantTransactionHistory(String userId, String merchantId) async {
    return await _dbHelper.query(
      'qr_transaction_security_log',
      where: 'user_id = ? AND merchant_id = ?',
      whereArgs: [userId, merchantId],
      limit: 10,
    );
  }

  /// Get last device info
  Future<String?> _getLastDeviceInfo(String userId) async {
    final transactions = await _dbHelper.query(
      'qr_transaction_security_log',
      where: 'user_id = ? AND device_info IS NOT NULL',
      whereArgs: [userId],
      orderBy: 'timestamp DESC',
      limit: 1,
    );

    if (transactions.isNotEmpty) {
      return transactions.first['device_info'] as String?;
    }
    return null;
  }

  /// Log security event
  Future<void> _logSecurityEvent({
    required String userId,
    required String eventType,
    Map<String, dynamic>? details,
    String severity = 'INFO',
  }) async {
    final event = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'user_id': userId,
      'event_type': eventType,
      'details': details != null ? jsonEncode(details) : null,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'severity': severity,
    };

    await _dbHelper.insert('qr_security_events', event);
  }
}

/// PIN verification result
class PINVerificationResult {
  final bool success;
  final String? error;
  final String message;
  final int attemptsRemaining;

  PINVerificationResult({
    required this.success,
    this.error,
    required this.message,
    required this.attemptsRemaining,
  });
}

/// Transaction limit validation result
class TransactionLimitResult {
  final bool isValid;
  final String? error;
  final String message;
  final double? currentDailyTotal;
  final double? currentMonthlyTotal;
  final double? dailyLimit;
  final double? monthlyLimit;

  TransactionLimitResult({
    required this.isValid,
    this.error,
    required this.message,
    this.currentDailyTotal,
    this.currentMonthlyTotal,
    this.dailyLimit,
    this.monthlyLimit,
  });
}

/// Fraud detection result
class FraudDetectionResult {
  final bool isBlocked;
  final String riskLevel;
  final double fraudScore;
  final String reason;
  final bool requiresAdditionalVerification;

  FraudDetectionResult({
    required this.isBlocked,
    required this.riskLevel,
    required this.fraudScore,
    required this.reason,
    required this.requiresAdditionalVerification,
  });
}

/// QR validation result
class QRValidationResult {
  final bool isValid;
  final String? error;
  final String message;
  final ZambiaQRData? decodedData;

  QRValidationResult({
    required this.isValid,
    this.error,
    required this.message,
    this.decodedData,
  });
}
