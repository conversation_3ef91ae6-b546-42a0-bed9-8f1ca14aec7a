// WALLET SERVICE - AUTOMATIC ACTIVATION
// Activates the dominant mobile money provider and manages wallet operations

import 'dart:async';
import 'package:flutter/services.dart';
import 'mobile_money_detector.dart';

enum WalletStatus {
  inactive,
  activating,
  active,
  error,
  suspended
}

class WalletActivationResult {
  final bool success;
  final String? message;
  final MobileMoneyProvider? activatedProvider;
  final WalletStatus status;
  final Map<String, dynamic>? walletData;

  WalletActivationResult({
    required this.success,
    this.message,
    this.activatedProvider,
    required this.status,
    this.walletData,
  });

  Map<String, dynamic> toJson() => {
    'success': success,
    'message': message,
    'activatedProvider': activatedProvider?.toString(),
    'status': status.toString(),
    'walletData': walletData,
  };
}

class ActiveWallet {
  final MobileMoneyProvider provider;
  final String displayName;
  final String serviceCode;
  final String apiEndpoint;
  final String? phoneNumber;
  final WalletStatus status;
  final DateTime activatedAt;
  final Map<String, dynamic> configuration;

  ActiveWallet({
    required this.provider,
    required this.displayName,
    required this.serviceCode,
    required this.apiEndpoint,
    this.phoneNumber,
    required this.status,
    required this.activatedAt,
    required this.configuration,
  });

  Map<String, dynamic> toJson() => {
    'provider': provider.toString(),
    'displayName': displayName,
    'serviceCode': serviceCode,
    'apiEndpoint': apiEndpoint,
    'phoneNumber': phoneNumber,
    'status': status.toString(),
    'activatedAt': activatedAt.toIso8601String(),
    'configuration': configuration,
  };
}

class WalletService {
  static const MethodChannel _channel = MethodChannel('wallet_service');
  
  static ActiveWallet? _activeWallet;
  static final StreamController<WalletStatus> _statusController = StreamController<WalletStatus>.broadcast();
  
  // Wallet configurations
  static const Map<MobileMoneyProvider, Map<String, dynamic>> _walletConfigs = {
    MobileMoneyProvider.mtnMobileMoney: {
      'displayName': 'MTN Mobile Money',
      'serviceCode': '*303#',
      'apiEndpoint': 'https://api.mtn.com/v1',
      'apiVersion': 'v1',
      'timeout': 30000,
      'retryAttempts': 3,
      'features': ['send', 'receive', 'balance', 'history', 'airtime'],
      'limits': {
        'dailyLimit': 50000.0,
        'transactionLimit': 10000.0,
        'minimumAmount': 1.0,
      },
    },
    MobileMoneyProvider.airtelMoney: {
      'displayName': 'Airtel Money',
      'serviceCode': '*432#',
      'apiEndpoint': 'https://api.airtel.africa/v1',
      'apiVersion': 'v1',
      'timeout': 30000,
      'retryAttempts': 3,
      'features': ['send', 'receive', 'balance', 'history', 'airtime', 'bills'],
      'limits': {
        'dailyLimit': 75000.0,
        'transactionLimit': 15000.0,
        'minimumAmount': 1.0,
      },
    },
    MobileMoneyProvider.zamtelKwacha: {
      'displayName': 'Zamtel Kwacha',
      'serviceCode': '*327#',
      'apiEndpoint': 'https://api.zamtel.zm/kwacha/v1',
      'apiVersion': 'v1',
      'timeout': 30000,
      'retryAttempts': 3,
      'features': ['send', 'receive', 'balance', 'history'],
      'limits': {
        'dailyLimit': 30000.0,
        'transactionLimit': 5000.0,
        'minimumAmount': 1.0,
      },
    },
  };

  /// Activate the dominant mobile money provider
  static Future<WalletActivationResult> activate(MobileMoneyProvider provider) async {
    try {
      print('🔄 Activating wallet: ${_getProviderDisplayName(provider)}');
      
      _updateStatus(WalletStatus.activating);

      // Validate provider
      if (provider == MobileMoneyProvider.unknown) {
        return WalletActivationResult(
          success: false,
          message: 'Cannot activate unknown provider',
          status: WalletStatus.error,
        );
      }

      // Get wallet configuration
      final config = _walletConfigs[provider];
      if (config == null) {
        return WalletActivationResult(
          success: false,
          message: 'Provider configuration not found',
          status: WalletStatus.error,
        );
      }

      // Initialize wallet connection
      final initResult = await _initializeWalletConnection(provider, config);
      if (!initResult.success) {
        _updateStatus(WalletStatus.error);
        return initResult;
      }

      // Verify wallet registration
      final verifyResult = await _verifyWalletRegistration(provider);
      if (!verifyResult.success) {
        _updateStatus(WalletStatus.error);
        return verifyResult;
      }

      // Setup wallet services
      final setupResult = await _setupWalletServices(provider, config);
      if (!setupResult.success) {
        _updateStatus(WalletStatus.error);
        return setupResult;
      }

      // Create active wallet instance
      _activeWallet = ActiveWallet(
        provider: provider,
        displayName: config['displayName'],
        serviceCode: config['serviceCode'],
        apiEndpoint: config['apiEndpoint'],
        phoneNumber: await _getWalletPhoneNumber(provider),
        status: WalletStatus.active,
        activatedAt: DateTime.now(),
        configuration: Map<String, dynamic>.from(config),
      );

      _updateStatus(WalletStatus.active);

      print('✅ Wallet activated successfully: ${config['displayName']}');
      print('📱 Service Code: ${config['serviceCode']}');
      print('🌐 API Endpoint: ${config['apiEndpoint']}');

      return WalletActivationResult(
        success: true,
        message: 'Wallet activated successfully',
        activatedProvider: provider,
        status: WalletStatus.active,
        walletData: _activeWallet?.toJson(),
      );

    } catch (e) {
      print('❌ Wallet activation failed: $e');
      _updateStatus(WalletStatus.error);
      
      return WalletActivationResult(
        success: false,
        message: 'Wallet activation failed: ${e.toString()}',
        status: WalletStatus.error,
      );
    }
  }

  /// Initialize wallet connection
  static Future<WalletActivationResult> _initializeWalletConnection(
    MobileMoneyProvider provider,
    Map<String, dynamic> config,
  ) async {
    try {
      print('🔗 Initializing wallet connection...');

      // Test API connectivity
      final connectivityResult = await _testAPIConnectivity(config['apiEndpoint']);
      if (!connectivityResult) {
        return WalletActivationResult(
          success: false,
          message: 'Unable to connect to ${config['displayName']} API',
          status: WalletStatus.error,
        );
      }

      // Initialize provider-specific connection
      switch (provider) {
        case MobileMoneyProvider.mtnMobileMoney:
          return await _initializeMTNConnection(config);
        case MobileMoneyProvider.airtelMoney:
          return await _initializeAirtelConnection(config);
        case MobileMoneyProvider.zamtelKwacha:
          return await _initializeZamtelConnection(config);
        default:
          return WalletActivationResult(
            success: false,
            message: 'Unsupported provider',
            status: WalletStatus.error,
          );
      }

    } catch (e) {
      return WalletActivationResult(
        success: false,
        message: 'Connection initialization failed: ${e.toString()}',
        status: WalletStatus.error,
      );
    }
  }

  /// Initialize MTN Mobile Money connection
  static Future<WalletActivationResult> _initializeMTNConnection(Map<String, dynamic> config) async {
    try {
      print('📱 Initializing MTN Mobile Money connection...');

      // MTN-specific initialization
      final mtnResult = await _channel.invokeMethod('initializeMTN', {
        'apiEndpoint': config['apiEndpoint'],
        'timeout': config['timeout'],
        'retryAttempts': config['retryAttempts'],
      });

      if (mtnResult['success'] == true) {
        print('✅ MTN connection initialized');
        return WalletActivationResult(
          success: true,
          message: 'MTN connection established',
          status: WalletStatus.activating,
        );
      } else {
        return WalletActivationResult(
          success: false,
          message: mtnResult['message'] ?? 'MTN initialization failed',
          status: WalletStatus.error,
        );
      }

    } catch (e) {
      return WalletActivationResult(
        success: false,
        message: 'MTN initialization error: ${e.toString()}',
        status: WalletStatus.error,
      );
    }
  }

  /// Initialize Airtel Money connection
  static Future<WalletActivationResult> _initializeAirtelConnection(Map<String, dynamic> config) async {
    try {
      print('📱 Initializing Airtel Money connection...');

      final airtelResult = await _channel.invokeMethod('initializeAirtel', {
        'apiEndpoint': config['apiEndpoint'],
        'timeout': config['timeout'],
        'retryAttempts': config['retryAttempts'],
      });

      if (airtelResult['success'] == true) {
        print('✅ Airtel connection initialized');
        return WalletActivationResult(
          success: true,
          message: 'Airtel connection established',
          status: WalletStatus.activating,
        );
      } else {
        return WalletActivationResult(
          success: false,
          message: airtelResult['message'] ?? 'Airtel initialization failed',
          status: WalletStatus.error,
        );
      }

    } catch (e) {
      return WalletActivationResult(
        success: false,
        message: 'Airtel initialization error: ${e.toString()}',
        status: WalletStatus.error,
      );
    }
  }

  /// Initialize Zamtel Kwacha connection
  static Future<WalletActivationResult> _initializeZamtelConnection(Map<String, dynamic> config) async {
    try {
      print('📱 Initializing Zamtel Kwacha connection...');

      final zamtelResult = await _channel.invokeMethod('initializeZamtel', {
        'apiEndpoint': config['apiEndpoint'],
        'timeout': config['timeout'],
        'retryAttempts': config['retryAttempts'],
      });

      if (zamtelResult['success'] == true) {
        print('✅ Zamtel connection initialized');
        return WalletActivationResult(
          success: true,
          message: 'Zamtel connection established',
          status: WalletStatus.activating,
        );
      } else {
        return WalletActivationResult(
          success: false,
          message: zamtelResult['message'] ?? 'Zamtel initialization failed',
          status: WalletStatus.error,
        );
      }

    } catch (e) {
      return WalletActivationResult(
        success: false,
        message: 'Zamtel initialization error: ${e.toString()}',
        status: WalletStatus.error,
      );
    }
  }

  /// Verify wallet registration
  static Future<WalletActivationResult> _verifyWalletRegistration(MobileMoneyProvider provider) async {
    try {
      print('✅ Verifying wallet registration...');

      // Check if user is registered with the provider
      final registrationResult = await _channel.invokeMethod('verifyRegistration', {
        'provider': provider.toString(),
      });

      if (registrationResult['isRegistered'] == true) {
        print('✅ Wallet registration verified');
        return WalletActivationResult(
          success: true,
          message: 'Wallet registration verified',
          status: WalletStatus.activating,
        );
      } else {
        return WalletActivationResult(
          success: false,
          message: 'Wallet not registered. Please register with ${_getProviderDisplayName(provider)} first.',
          status: WalletStatus.error,
        );
      }

    } catch (e) {
      // Assume registration is valid if verification fails
      print('⚠️ Registration verification failed, proceeding: $e');
      return WalletActivationResult(
        success: true,
        message: 'Registration verification skipped',
        status: WalletStatus.activating,
      );
    }
  }

  /// Setup wallet services
  static Future<WalletActivationResult> _setupWalletServices(
    MobileMoneyProvider provider,
    Map<String, dynamic> config,
  ) async {
    try {
      print('⚙️ Setting up wallet services...');

      // Setup service features
      final features = List<String>.from(config['features']);
      for (final feature in features) {
        await _enableWalletFeature(provider, feature);
      }

      // Configure transaction limits
      final limits = Map<String, dynamic>.from(config['limits']);
      await _configureTransactionLimits(provider, limits);

      // Setup monitoring and notifications
      await _setupWalletMonitoring(provider);

      print('✅ Wallet services configured');
      return WalletActivationResult(
        success: true,
        message: 'Wallet services configured',
        status: WalletStatus.activating,
      );

    } catch (e) {
      return WalletActivationResult(
        success: false,
        message: 'Service setup failed: ${e.toString()}',
        status: WalletStatus.error,
      );
    }
  }

  /// Test API connectivity
  static Future<bool> _testAPIConnectivity(String apiEndpoint) async {
    try {
      final result = await _channel.invokeMethod('testConnectivity', {
        'endpoint': apiEndpoint,
        'timeout': 10000,
      });
      return result['connected'] == true;
    } catch (e) {
      return false;
    }
  }

  /// Enable wallet feature
  static Future<void> _enableWalletFeature(MobileMoneyProvider provider, String feature) async {
    try {
      await _channel.invokeMethod('enableFeature', {
        'provider': provider.toString(),
        'feature': feature,
      });
      print('   ✅ $feature enabled');
    } catch (e) {
      print('   ⚠️ Failed to enable $feature: $e');
    }
  }

  /// Configure transaction limits
  static Future<void> _configureTransactionLimits(
    MobileMoneyProvider provider,
    Map<String, dynamic> limits,
  ) async {
    try {
      await _channel.invokeMethod('configureLimits', {
        'provider': provider.toString(),
        'limits': limits,
      });
      print('   ✅ Transaction limits configured');
    } catch (e) {
      print('   ⚠️ Failed to configure limits: $e');
    }
  }

  /// Setup wallet monitoring
  static Future<void> _setupWalletMonitoring(MobileMoneyProvider provider) async {
    try {
      await _channel.invokeMethod('setupMonitoring', {
        'provider': provider.toString(),
      });
      print('   ✅ Wallet monitoring enabled');
    } catch (e) {
      print('   ⚠️ Failed to setup monitoring: $e');
    }
  }

  /// Get wallet phone number
  static Future<String?> _getWalletPhoneNumber(MobileMoneyProvider provider) async {
    try {
      final result = await _channel.invokeMethod('getWalletPhoneNumber', {
        'provider': provider.toString(),
      });
      return result['phoneNumber'];
    } catch (e) {
      return null;
    }
  }

  /// Update wallet status
  static void _updateStatus(WalletStatus status) {
    _statusController.add(status);
  }

  /// Get active wallet
  static ActiveWallet? getActiveWallet() {
    return _activeWallet;
  }

  /// Check if wallet is active
  static bool isWalletActive() {
    return _activeWallet?.status == WalletStatus.active;
  }

  /// Get wallet status stream
  static Stream<WalletStatus> get statusStream => _statusController.stream;

  /// Deactivate current wallet
  static Future<bool> deactivate() async {
    try {
      if (_activeWallet != null) {
        _updateStatus(WalletStatus.inactive);
        _activeWallet = null;
        print('✅ Wallet deactivated');
        return true;
      }
      return false;
    } catch (e) {
      print('❌ Wallet deactivation failed: $e');
      return false;
    }
  }

  /// Switch to different provider
  static Future<WalletActivationResult> switchProvider(MobileMoneyProvider newProvider) async {
    try {
      // Deactivate current wallet
      await deactivate();
      
      // Activate new provider
      return await activate(newProvider);
    } catch (e) {
      return WalletActivationResult(
        success: false,
        message: 'Provider switch failed: ${e.toString()}',
        status: WalletStatus.error,
      );
    }
  }

  /// Get provider display name
  static String _getProviderDisplayName(MobileMoneyProvider provider) {
    return _walletConfigs[provider]?['displayName'] ?? 'Unknown Provider';
  }

  /// Get wallet configuration
  static Map<String, dynamic>? getWalletConfiguration(MobileMoneyProvider provider) {
    return _walletConfigs[provider];
  }

  /// Get supported features for provider
  static List<String> getSupportedFeatures(MobileMoneyProvider provider) {
    final config = _walletConfigs[provider];
    return config != null ? List<String>.from(config['features']) : [];
  }

  /// Get transaction limits for provider
  static Map<String, dynamic>? getTransactionLimits(MobileMoneyProvider provider) {
    final config = _walletConfigs[provider];
    return config?['limits'];
  }
}
