/// QR Generator Widget for Zambian Merchants
/// Generates QR codes for payments with offline support
/// Optimized for small businesses and rural merchants

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:typed_data';

import '../../core/constants/app_constants.dart';
import '../zambia_qr.dart';
import '../zambia_qr_format.dart';
import '../merchant_qr_service.dart';
import '../offline_qr_service.dart';

/// QR Generator Widget for merchants
class QRGeneratorWidget extends StatefulWidget {
  final String merchantId;
  final double? fixedAmount;
  final String? description;
  final bool allowCustomAmount;
  final bool enableOfflineMode;
  final Function(Uint8List qrImage, String qrData)? onQRGenerated;
  final Function(String error)? onError;

  const QRGeneratorWidget({
    Key? key,
    required this.merchantId,
    this.fixedAmount,
    this.description,
    this.allowCustomAmount = true,
    this.enableOfflineMode = true,
    this.onQRGenerated,
    this.onError,
  }) : super(key: key);

  @override
  State<QRGeneratorWidget> createState() => _QRGeneratorWidgetState();
}

class _QRGeneratorWidgetState extends State<QRGeneratorWidget> {
  final MerchantQRService _merchantService = MerchantQRService();
  final OfflineQRService _offlineService = OfflineQRService();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  Uint8List? _qrImage;
  String? _qrData;
  bool _isGenerating = false;
  bool _isOfflineMode = false;
  double? _currentAmount;
  String? _currentDescription;

  @override
  void initState() {
    super.initState();
    _initializeWidget();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _initializeWidget() {
    if (widget.fixedAmount != null) {
      _amountController.text = widget.fixedAmount!.toStringAsFixed(2);
      _currentAmount = widget.fixedAmount;
    }
    
    if (widget.description != null) {
      _descriptionController.text = widget.description!;
      _currentDescription = widget.description;
    }

    // Generate initial QR if fixed amount is provided
    if (widget.fixedAmount != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _generateQR();
      });
    }
  }

  Future<void> _generateQR() async {
    if (_isGenerating) return;

    setState(() {
      _isGenerating = true;
    });

    try {
      final amount = _currentAmount ?? 0.0;
      final description = _currentDescription?.trim();

      if (_isOfflineMode && widget.enableOfflineMode) {
        await _generateOfflineQR(amount, description);
      } else {
        await _generateOnlineQR(amount, description);
      }
    } catch (e) {
      widget.onError?.call('Failed to generate QR: ${e.toString()}');
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  Future<void> _generateOnlineQR(double amount, String? description) async {
    try {
      final result = await _merchantService.generateMerchantQR(
        merchantId: widget.merchantId,
        amount: amount > 0 ? amount : null,
        description: description,
        expiryDuration: const Duration(hours: 24),
      );

      if (result.success && result.qrData != null) {
        setState(() {
          _qrImage = result.qrData;
          _qrData = 'QR_${result.qrId}'; // Simplified for display
        });

        widget.onQRGenerated?.call(_qrImage!, _qrData!);
      } else {
        widget.onError?.call(result.message);
      }
    } catch (e) {
      widget.onError?.call('Online QR generation failed: ${e.toString()}');
    }
  }

  Future<void> _generateOfflineQR(double amount, String? description) async {
    try {
      final result = await _offlineService.generateOfflineQR(
        merchantId: widget.merchantId,
        amount: amount > 0 ? amount : null,
        description: description,
        validityDuration: const Duration(hours: 48), // Longer for offline
      );

      if (result.success && result.qrImage != null) {
        setState(() {
          _qrImage = result.qrImage;
          _qrData = result.qrPayload ?? 'OFFLINE_QR';
        });

        widget.onQRGenerated?.call(_qrImage!, _qrData!);
      } else {
        widget.onError?.call(result.message);
      }
    } catch (e) {
      widget.onError?.call('Offline QR generation failed: ${e.toString()}');
    }
  }

  void _onAmountChanged(String value) {
    final amount = ZambiaQRFormat.parseAmountFromString(value);
    if (amount != _currentAmount) {
      setState(() {
        _currentAmount = amount;
      });

      // Validate amount and show feedback
      if (amount != null) {
        final validation = ZambiaQRFormat.validateAmountDetailed(amount);
        if (!validation.isValid) {
          // Show validation error
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(validation.message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    }
  }

  void _onDescriptionChanged(String value) {
    if (value != _currentDescription) {
      setState(() {
        _currentDescription = value.isEmpty ? null : value;
      });
    }
  }

  void _toggleOfflineMode() {
    if (!widget.enableOfflineMode) return;
    
    setState(() {
      _isOfflineMode = !_isOfflineMode;
    });
    
    // Regenerate QR with new mode
    if (_qrImage != null) {
      _generateQR();
    }
  }

  Future<void> _shareQR() async {
    if (_qrImage == null) return;

    try {
      // Copy QR data to clipboard
      await Clipboard.setData(ClipboardData(text: _qrData ?? ''));
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('QR code data copied to clipboard'),
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      widget.onError?.call('Failed to share QR: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            if (widget.allowCustomAmount) _buildAmountInput(),
            const SizedBox(height: 12),
            _buildDescriptionInput(),
            const SizedBox(height: 16),
            _buildModeToggle(),
            const SizedBox(height: 16),
            _buildQRDisplay(),
            const SizedBox(height: 16),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(
          Icons.qr_code,
          color: Color(0xFF2E7D32),
          size: 24,
        ),
        const SizedBox(width: 8),
        const Text(
          'Generate Payment QR',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        if (_isOfflineMode)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.orange.shade100,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              'OFFLINE',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildAmountInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _amountController,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          decoration: InputDecoration(
            labelText: 'Amount (ZMW)',
            prefixText: 'K ',
            border: const OutlineInputBorder(),
            helperText: 'Enter 0 for open amount (customer enters)',
            errorText: _getAmountErrorText(),
          ),
          onChanged: _onAmountChanged,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
          ],
        ),
        const SizedBox(height: 8),
        _buildAmountValidationIndicator(),
        const SizedBox(height: 8),
        _buildSuggestedAmounts(),
      ],
    );
  }

  String? _getAmountErrorText() {
    if (_currentAmount == null) return null;

    final validation = ZambiaQRFormat.validateAmountDetailed(_currentAmount!);
    return validation.isValid ? null : validation.message;
  }

  Widget _buildAmountValidationIndicator() {
    if (_currentAmount == null) return const SizedBox.shrink();

    final validation = ZambiaQRFormat.validateAmountDetailed(_currentAmount!);

    Color color;
    IconData icon;
    String message;

    if (!validation.isValid) {
      color = Colors.red;
      icon = Icons.error;
      message = validation.message;
    } else if (validation.isOpenAmount) {
      color = Colors.blue;
      icon = Icons.edit;
      message = 'Open amount - customer will enter amount';
    } else {
      color = Colors.green;
      icon = Icons.check_circle;
      message = ZambiaQRFormat.formatAmountForDisplay(_currentAmount!);
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              message,
              style: TextStyle(color: color, fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestedAmounts() {
    // Get suggested amounts for retail category (default)
    final suggestedAmounts = ZambiaQRFormat.getSuggestedAmounts('RETAIL');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick amounts:',
          style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 4),
        Wrap(
          spacing: 6,
          runSpacing: 4,
          children: suggestedAmounts.map((amount) {
            return ActionChip(
              label: Text(
                ZambiaQRFormat.formatAmountForDisplay(amount, includeSymbol: false),
                style: const TextStyle(fontSize: 11),
              ),
              onPressed: () {
                _amountController.text = amount.toString();
                _onAmountChanged(amount.toString());
              },
              backgroundColor: Colors.green.shade50,
              labelStyle: TextStyle(color: Colors.green.shade700),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              visualDensity: VisualDensity.compact,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDescriptionInput() {
    return TextFormField(
      controller: _descriptionController,
      decoration: const InputDecoration(
        labelText: 'Description (Optional)',
        border: OutlineInputBorder(),
        helperText: 'Brief description of the payment',
      ),
      onChanged: _onDescriptionChanged,
      maxLength: 50,
    );
  }

  Widget _buildModeToggle() {
    if (!widget.enableOfflineMode) return const SizedBox.shrink();

    return Row(
      children: [
        const Text('Mode: '),
        const SizedBox(width: 8),
        ChoiceChip(
          label: const Text('Online'),
          selected: !_isOfflineMode,
          onSelected: (selected) {
            if (selected) _toggleOfflineMode();
          },
        ),
        const SizedBox(width: 8),
        ChoiceChip(
          label: const Text('Offline'),
          selected: _isOfflineMode,
          onSelected: (selected) {
            if (selected) _toggleOfflineMode();
          },
        ),
      ],
    );
  }

  Widget _buildQRDisplay() {
    return Container(
      height: 250,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: _isGenerating
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Generating QR code...'),
                ],
              ),
            )
          : _qrImage != null
              ? Center(
                  child: Image.memory(
                    _qrImage!,
                    width: 200,
                    height: 200,
                    fit: BoxFit.contain,
                  ),
                )
              : const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.qr_code_2,
                        size: 64,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'QR code will appear here',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isGenerating ? null : _generateQR,
            icon: const Icon(Icons.refresh),
            label: const Text('Generate QR'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7D32),
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _qrImage != null ? _shareQR : null,
            icon: const Icon(Icons.share),
            label: const Text('Share'),
          ),
        ),
      ],
    );
  }
}
