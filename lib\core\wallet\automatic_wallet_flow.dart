// AUTOMATIC WALLET DETECTION AND ACTIVATION FLOW
// Implements: final dominantWallet = MobileMoney.detectDominantProvider(); WalletService.activate(dominantWallet);

import 'dart:async';
import 'package:flutter/material.dart';
import 'mobile_money_detector.dart';
import 'wallet_service.dart';

class AutomaticWalletResult {
  final bool success;
  final String? message;
  final MobileMoneyProvider? detectedProvider;
  final WalletActivationResult? activationResult;
  final WalletDetectionResult? detectionResult;

  AutomaticWalletResult({
    required this.success,
    this.message,
    this.detectedProvider,
    this.activationResult,
    this.detectionResult,
  });

  Map<String, dynamic> toJson() => {
    'success': success,
    'message': message,
    'detectedProvider': detectedProvider?.toString(),
    'activationResult': activationResult?.toJson(),
    'detectionResult': detectionResult?.toJson(),
  };
}

class AutomaticWalletFlow {
  static bool _isRunning = false;
  static final StreamController<String> _progressController = StreamController<String>.broadcast();

  /// Execute automatic wallet detection and activation
  static Future<AutomaticWalletResult> execute() async {
    if (_isRunning) {
      return AutomaticWalletResult(
        success: false,
        message: 'Automatic wallet flow already running',
      );
    }

    _isRunning = true;
    
    try {
      print('🤖 Starting automatic wallet detection and activation...');
      _updateProgress('Starting automatic wallet detection...');

      // Step 1: Detect dominant provider
      _updateProgress('Detecting mobile money providers...');
      final dominantWallet = await MobileMoney.detectDominantProvider();
      
      if (dominantWallet == MobileMoneyProvider.unknown) {
        _isRunning = false;
        return AutomaticWalletResult(
          success: false,
          message: 'No mobile money provider detected',
          detectedProvider: dominantWallet,
        );
      }

      print('🎯 Dominant provider detected: ${_getProviderDisplayName(dominantWallet)}');
      _updateProgress('Detected: ${_getProviderDisplayName(dominantWallet)}');

      // Step 2: Get detailed detection results
      _updateProgress('Analyzing provider details...');
      final detectionResult = await MobileMoney.detectAllProviders();

      // Step 3: Activate the dominant wallet
      _updateProgress('Activating ${_getProviderDisplayName(dominantWallet)}...');
      final activationResult = await WalletService.activate(dominantWallet);

      _isRunning = false;

      if (activationResult.success) {
        print('✅ Automatic wallet activation completed successfully');
        _updateProgress('Wallet activated successfully!');
        
        return AutomaticWalletResult(
          success: true,
          message: 'Wallet activated successfully',
          detectedProvider: dominantWallet,
          activationResult: activationResult,
          detectionResult: detectionResult,
        );
      } else {
        print('❌ Wallet activation failed: ${activationResult.message}');
        _updateProgress('Activation failed: ${activationResult.message}');
        
        return AutomaticWalletResult(
          success: false,
          message: activationResult.message,
          detectedProvider: dominantWallet,
          activationResult: activationResult,
          detectionResult: detectionResult,
        );
      }

    } catch (e) {
      _isRunning = false;
      print('❌ Automatic wallet flow failed: $e');
      _updateProgress('Error: ${e.toString()}');
      
      return AutomaticWalletResult(
        success: false,
        message: 'Automatic wallet flow failed: ${e.toString()}',
      );
    }
  }

  /// Execute with detailed progress tracking
  static Future<AutomaticWalletResult> executeWithProgress({
    Function(String)? onProgress,
    Function(WalletDetectionResult)? onDetectionComplete,
    Function(WalletActivationResult)? onActivationComplete,
  }) async {
    if (_isRunning) {
      return AutomaticWalletResult(
        success: false,
        message: 'Automatic wallet flow already running',
      );
    }

    _isRunning = true;
    
    try {
      print('🤖 Starting detailed automatic wallet flow...');

      // Step 1: Comprehensive provider detection
      onProgress?.call('Scanning for mobile money providers...');
      _updateProgress('Scanning for mobile money providers...');
      
      final detectionResult = await MobileMoney.detectAllProviders();
      onDetectionComplete?.call(detectionResult);

      if (detectionResult.dominantProvider == MobileMoneyProvider.unknown) {
        _isRunning = false;
        onProgress?.call('No mobile money provider found');
        return AutomaticWalletResult(
          success: false,
          message: 'No mobile money provider detected',
          detectionResult: detectionResult,
        );
      }

      final dominantProvider = detectionResult.dominantProvider;
      final confidence = (detectionResult.confidence * 100).toStringAsFixed(1);
      
      print('🎯 Detection complete:');
      print('   Dominant: ${_getProviderDisplayName(dominantProvider)}');
      print('   Confidence: $confidence%');
      print('   Available: ${detectionResult.availableProviders.length} providers');

      onProgress?.call('Found: ${_getProviderDisplayName(dominantProvider)} ($confidence% confidence)');
      _updateProgress('Found: ${_getProviderDisplayName(dominantProvider)} ($confidence% confidence)');

      // Step 2: Activate the dominant provider
      onProgress?.call('Activating ${_getProviderDisplayName(dominantProvider)}...');
      _updateProgress('Activating ${_getProviderDisplayName(dominantProvider)}...');
      
      final activationResult = await WalletService.activate(dominantProvider);
      onActivationComplete?.call(activationResult);

      _isRunning = false;

      if (activationResult.success) {
        final successMessage = '${_getProviderDisplayName(dominantProvider)} activated successfully!';
        onProgress?.call(successMessage);
        _updateProgress(successMessage);
        
        return AutomaticWalletResult(
          success: true,
          message: successMessage,
          detectedProvider: dominantProvider,
          activationResult: activationResult,
          detectionResult: detectionResult,
        );
      } else {
        final errorMessage = 'Activation failed: ${activationResult.message}';
        onProgress?.call(errorMessage);
        _updateProgress(errorMessage);
        
        return AutomaticWalletResult(
          success: false,
          message: errorMessage,
          detectedProvider: dominantProvider,
          activationResult: activationResult,
          detectionResult: detectionResult,
        );
      }

    } catch (e) {
      _isRunning = false;
      final errorMessage = 'Automatic wallet flow failed: ${e.toString()}';
      onProgress?.call(errorMessage);
      _updateProgress(errorMessage);
      
      return AutomaticWalletResult(
        success: false,
        message: errorMessage,
      );
    }
  }

  /// Quick activation for returning users
  static Future<AutomaticWalletResult> quickActivation() async {
    try {
      print('⚡ Quick wallet activation...');
      _updateProgress('Quick activation in progress...');

      // Check if there's a previously detected provider
      final lastProvider = await _getLastDetectedProvider();
      
      if (lastProvider != null && lastProvider != MobileMoneyProvider.unknown) {
        print('📱 Using last detected provider: ${_getProviderDisplayName(lastProvider)}');
        _updateProgress('Activating ${_getProviderDisplayName(lastProvider)}...');
        
        final activationResult = await WalletService.activate(lastProvider);
        
        if (activationResult.success) {
          _updateProgress('Quick activation successful!');
          return AutomaticWalletResult(
            success: true,
            message: 'Quick activation successful',
            detectedProvider: lastProvider,
            activationResult: activationResult,
          );
        }
      }

      // Fallback to full detection
      print('🔄 Falling back to full detection...');
      return await execute();

    } catch (e) {
      print('❌ Quick activation failed: $e');
      return await execute();
    }
  }

  /// Detect and activate all available providers
  static Future<Map<MobileMoneyProvider, WalletActivationResult>> activateAllProviders() async {
    final results = <MobileMoneyProvider, WalletActivationResult>{};
    
    try {
      print('🔄 Detecting and activating all available providers...');
      _updateProgress('Detecting all providers...');

      final detectionResult = await MobileMoney.detectAllProviders();
      
      for (final provider in detectionResult.availableProviders) {
        if (provider == MobileMoneyProvider.unknown) continue;
        
        print('🔄 Activating ${_getProviderDisplayName(provider)}...');
        _updateProgress('Activating ${_getProviderDisplayName(provider)}...');
        
        final activationResult = await WalletService.activate(provider);
        results[provider] = activationResult;
        
        if (activationResult.success) {
          print('✅ ${_getProviderDisplayName(provider)} activated');
        } else {
          print('❌ ${_getProviderDisplayName(provider)} activation failed');
        }
      }

      _updateProgress('All providers processed');
      return results;

    } catch (e) {
      print('❌ Multi-provider activation failed: $e');
      return results;
    }
  }

  /// Switch to a different provider
  static Future<AutomaticWalletResult> switchProvider(MobileMoneyProvider newProvider) async {
    try {
      print('🔄 Switching to ${_getProviderDisplayName(newProvider)}...');
      _updateProgress('Switching to ${_getProviderDisplayName(newProvider)}...');

      final activationResult = await WalletService.switchProvider(newProvider);
      
      if (activationResult.success) {
        _updateProgress('Provider switched successfully!');
        await _saveLastDetectedProvider(newProvider);
        
        return AutomaticWalletResult(
          success: true,
          message: 'Provider switched successfully',
          detectedProvider: newProvider,
          activationResult: activationResult,
        );
      } else {
        _updateProgress('Provider switch failed');
        return AutomaticWalletResult(
          success: false,
          message: activationResult.message,
          detectedProvider: newProvider,
          activationResult: activationResult,
        );
      }

    } catch (e) {
      _updateProgress('Switch failed: ${e.toString()}');
      return AutomaticWalletResult(
        success: false,
        message: 'Provider switch failed: ${e.toString()}',
      );
    }
  }

  /// Get wallet status summary
  static Future<Map<String, dynamic>> getWalletStatusSummary() async {
    try {
      final activeWallet = WalletService.getActiveWallet();
      final detectionResult = await MobileMoney.detectAllProviders();
      
      return {
        'hasActiveWallet': activeWallet != null,
        'activeProvider': activeWallet?.provider.toString(),
        'activeProviderName': activeWallet?.displayName,
        'walletStatus': activeWallet?.status.toString(),
        'availableProviders': detectionResult.availableProviders.length,
        'dominantProvider': detectionResult.dominantProvider.toString(),
        'confidence': detectionResult.confidence,
        'lastActivated': activeWallet?.activatedAt.toIso8601String(),
      };
    } catch (e) {
      return {
        'hasActiveWallet': false,
        'error': e.toString(),
      };
    }
  }

  /// Update progress message
  static void _updateProgress(String message) {
    _progressController.add(message);
  }

  /// Get progress stream
  static Stream<String> get progressStream => _progressController.stream;

  /// Check if flow is running
  static bool get isRunning => _isRunning;

  /// Get provider display name
  static String _getProviderDisplayName(MobileMoneyProvider provider) {
    switch (provider) {
      case MobileMoneyProvider.mtnMobileMoney:
        return 'MTN Mobile Money';
      case MobileMoneyProvider.airtelMoney:
        return 'Airtel Money';
      case MobileMoneyProvider.zamtelKwacha:
        return 'Zamtel Kwacha';
      case MobileMoneyProvider.unknown:
        return 'Unknown Provider';
    }
  }

  /// Save last detected provider (placeholder implementation)
  static Future<void> _saveLastDetectedProvider(MobileMoneyProvider provider) async {
    // Implementation would save to secure storage
    print('💾 Saving last detected provider: ${_getProviderDisplayName(provider)}');
  }

  /// Get last detected provider (placeholder implementation)
  static Future<MobileMoneyProvider?> _getLastDetectedProvider() async {
    // Implementation would load from secure storage
    return null; // Placeholder
  }

  /// Dispose resources
  static void dispose() {
    _progressController.close();
  }
}
