/// Merchant QR Integration Service for Zambian Businesses
/// Supports small traders, shops, and informal market vendors
/// Optimized for offline operations and rural connectivity

import 'dart:convert';
import 'dart:typed_data';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

import '../core/constants/app_constants.dart';
import '../core/config/app_config.dart';
import '../core/security/encryption_service.dart';
import '../data/database/database_helper.dart';
import '../features/mobile_money/data/services/mobile_money_service.dart';
import '../features/offline_sync/data/offline_sync_manager.dart';
import 'zambia_qr.dart';
import 'zambia_qr_format.dart';

/// Merchant QR Service for business integration
class MerchantQRService {
  static final MerchantQRService _instance = MerchantQRService._internal();
  factory MerchantQRService() => _instance;
  MerchantQRService._internal();

  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final EncryptionService _encryption = EncryptionService();
  final MobileMoneyService _mobileMoneyService = MobileMoneyService();
  final OfflineSyncManager _syncManager = OfflineSyncManager();

  bool _isInitialized = false;

  /// Initialize merchant service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _encryption.initialize();
      await _createMerchantTables();
      _isInitialized = true;
      
      _logger.i('🏪 Merchant QR service initialized');
    } catch (e) {
      _logger.e('Failed to initialize merchant service: $e');
      rethrow;
    }
  }

  /// Register new merchant
  Future<MerchantRegistrationResult> registerMerchant({
    required String userId,
    required String businessName,
    required String phoneNumber,
    required String category,
    String? location,
    String? description,
    List<double>? typicalAmounts,
    Map<String, dynamic>? businessDetails,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      // Validate input
      final validation = _validateMerchantData(
        businessName: businessName,
        phoneNumber: phoneNumber,
        category: category,
      );

      if (!validation.isValid) {
        return MerchantRegistrationResult(
          success: false,
          error: validation.error,
          message: validation.message,
        );
      }

      // Check if merchant already exists
      final existingMerchant = await _getMerchantByPhone(phoneNumber);
      if (existingMerchant != null) {
        return MerchantRegistrationResult(
          success: false,
          error: 'MERCHANT_EXISTS',
          message: 'Merchant with this phone number already exists',
        );
      }

      // Generate merchant ID
      final merchantId = _uuid.v4();
      
      // Detect mobile money provider
      final provider = _mobileMoneyService.detectProvider(phoneNumber);

      // Create merchant record
      final merchant = {
        'id': merchantId,
        'user_id': userId,
        'business_name': businessName,
        'phone_number': phoneNumber,
        'category': category,
        'location': location ?? '',
        'description': description ?? '',
        'provider': provider,
        'typical_amounts': jsonEncode(typicalAmounts ?? []),
        'business_details': jsonEncode(businessDetails ?? {}),
        'status': 'ACTIVE',
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('merchants', merchant);

      // Generate default QR codes
      await _generateDefaultQRCodes(merchantId);

      _logger.i('🏪 Merchant registered: $businessName ($merchantId)');

      return MerchantRegistrationResult(
        success: true,
        merchantId: merchantId,
        message: 'Merchant registered successfully',
      );
    } catch (e) {
      _logger.e('Merchant registration failed: $e');
      return MerchantRegistrationResult(
        success: false,
        error: 'REGISTRATION_FAILED',
        message: 'Failed to register merchant: ${e.toString()}',
      );
    }
  }

  /// Generate QR code for merchant
  Future<QRGenerationResult> generateMerchantQR({
    required String merchantId,
    double? amount,
    String? description,
    Duration? expiryDuration,
    QRType qrType = QRType.dynamic,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      // Get merchant info
      final merchant = await _getMerchantById(merchantId);
      if (merchant == null) {
        return QRGenerationResult(
          success: false,
          error: 'MERCHANT_NOT_FOUND',
          message: 'Merchant not found',
        );
      }

      // Generate QR code
      final qrData = await PayMuleQR.generateQR(
        merchantId: merchantId,
        amount: amount ?? 0.0,
        description: description,
        currency: 'ZMW',
        expiryDuration: expiryDuration,
      );

      // Create QR record
      final qrId = _uuid.v4();
      final qrRecord = {
        'id': qrId,
        'merchant_id': merchantId,
        'qr_type': qrType.name,
        'amount': amount,
        'description': description,
        'expiry_duration_seconds': expiryDuration?.inSeconds,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'scan_count': 0,
        'last_scanned_at': null,
      };

      await _dbHelper.insert('merchant_qr_codes', qrRecord);

      _logger.i('🏪 QR generated for merchant: $merchantId');

      return QRGenerationResult(
        success: true,
        qrId: qrId,
        qrData: qrData,
        message: 'QR code generated successfully',
      );
    } catch (e) {
      _logger.e('QR generation failed: $e');
      return QRGenerationResult(
        success: false,
        error: 'GENERATION_FAILED',
        message: 'Failed to generate QR code: ${e.toString()}',
      );
    }
  }

  /// Process payment to merchant
  Future<MerchantPaymentResult> processPayment({
    required String merchantId,
    required String payerUserId,
    required double amount,
    required String pin,
    String? description,
    String? qrId,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      // Get merchant info
      final merchant = await _getMerchantById(merchantId);
      if (merchant == null) {
        return MerchantPaymentResult(
          success: false,
          error: 'MERCHANT_NOT_FOUND',
          message: 'Merchant not found',
        );
      }

      // Validate payment
      final validation = await _validatePayment(
        merchantId: merchantId,
        amount: amount,
        payerUserId: payerUserId,
      );

      if (!validation.isValid) {
        return MerchantPaymentResult(
          success: false,
          error: validation.error,
          message: validation.message,
        );
      }

      // Create transaction
      final transactionId = _uuid.v4();
      final transaction = {
        'id': transactionId,
        'merchant_id': merchantId,
        'payer_user_id': payerUserId,
        'amount': amount,
        'description': description ?? 'Payment to ${merchant['business_name']}',
        'qr_id': qrId,
        'status': 'PENDING',
        'created_at': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('merchant_transactions', transaction);

      // Process payment based on connectivity
      final isOnline = await _syncManager.isConnected();
      
      if (isOnline) {
        // Process online
        final result = await _processOnlinePayment(
          transactionId: transactionId,
          merchantPhone: merchant['phone_number'] as String,
          payerUserId: payerUserId,
          amount: amount,
          description: description,
        );

        // Update transaction status
        await _dbHelper.update(
          'merchant_transactions',
          {'status': result.success ? 'COMPLETED' : 'FAILED'},
          where: 'id = ?',
          whereArgs: [transactionId],
        );

        // Update QR scan count if applicable
        if (qrId != null) {
          await _updateQRScanCount(qrId);
        }

        return MerchantPaymentResult(
          success: result.success,
          transactionId: transactionId,
          amount: amount,
          merchantId: merchantId,
          message: result.message,
          error: result.success ? null : 'PAYMENT_FAILED',
        );
      } else {
        // Queue for offline processing
        await _syncManager.queueTransaction(
          userId: payerUserId,
          transactionData: {
            'transaction_id': transactionId,
            'merchant_id': merchantId,
            'amount': amount,
            'description': description,
          },
          transactionType: 'MERCHANT_PAYMENT',
        );

        await _dbHelper.update(
          'merchant_transactions',
          {'status': 'QUEUED'},
          where: 'id = ?',
          whereArgs: [transactionId],
        );

        return MerchantPaymentResult(
          success: true,
          transactionId: transactionId,
          amount: amount,
          merchantId: merchantId,
          message: 'Payment queued for processing when online',
          isOffline: true,
        );
      }
    } catch (e) {
      _logger.e('Merchant payment failed: $e');
      return MerchantPaymentResult(
        success: false,
        error: 'PAYMENT_FAILED',
        message: 'Payment processing failed: ${e.toString()}',
      );
    }
  }

  /// Get merchant information
  Future<Map<String, dynamic>?> getMerchantInfo(String merchantId) async {
    return await _getMerchantById(merchantId);
  }

  /// Get merchant transaction history
  Future<List<Map<String, dynamic>>> getMerchantTransactions(
    String merchantId, {
    int? limit,
    int? offset,
  }) async {
    try {
      return await _dbHelper.query(
        'merchant_transactions',
        where: 'merchant_id = ?',
        whereArgs: [merchantId],
        orderBy: 'created_at DESC',
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      _logger.e('Failed to get merchant transactions: $e');
      return [];
    }
  }

  /// Get merchant QR codes
  Future<List<Map<String, dynamic>>> getMerchantQRCodes(String merchantId) async {
    try {
      return await _dbHelper.query(
        'merchant_qr_codes',
        where: 'merchant_id = ?',
        whereArgs: [merchantId],
        orderBy: 'created_at DESC',
      );
    } catch (e) {
      _logger.e('Failed to get merchant QR codes: $e');
      return [];
    }
  }

  /// Update merchant information
  Future<bool> updateMerchant({
    required String merchantId,
    String? businessName,
    String? location,
    String? description,
    String? category,
    List<double>? typicalAmounts,
  }) async {
    try {
      final updates = <String, dynamic>{
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      };

      if (businessName != null) updates['business_name'] = businessName;
      if (location != null) updates['location'] = location;
      if (description != null) updates['description'] = description;
      if (category != null) updates['category'] = category;
      if (typicalAmounts != null) {
        updates['typical_amounts'] = jsonEncode(typicalAmounts);
      }

      final rowsAffected = await _dbHelper.update(
        'merchants',
        updates,
        where: 'id = ?',
        whereArgs: [merchantId],
      );

      return rowsAffected > 0;
    } catch (e) {
      _logger.e('Failed to update merchant: $e');
      return false;
    }
  }

  /// Create database tables for merchants
  Future<void> _createMerchantTables() async {
    final db = await _dbHelper.database;

    // Merchants table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS merchants (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        business_name TEXT NOT NULL,
        phone_number TEXT NOT NULL UNIQUE,
        category TEXT NOT NULL,
        location TEXT,
        description TEXT,
        provider TEXT,
        typical_amounts TEXT,
        business_details TEXT,
        status TEXT DEFAULT 'ACTIVE',
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Merchant QR codes table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS merchant_qr_codes (
        id TEXT PRIMARY KEY,
        merchant_id TEXT NOT NULL,
        qr_type TEXT NOT NULL,
        amount REAL,
        description TEXT,
        expiry_duration_seconds INTEGER,
        created_at INTEGER NOT NULL,
        scan_count INTEGER DEFAULT 0,
        last_scanned_at INTEGER,
        FOREIGN KEY (merchant_id) REFERENCES merchants (id)
      )
    ''');

    // Merchant transactions table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS merchant_transactions (
        id TEXT PRIMARY KEY,
        merchant_id TEXT NOT NULL,
        payer_user_id TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT,
        qr_id TEXT,
        status TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (merchant_id) REFERENCES merchants (id),
        FOREIGN KEY (qr_id) REFERENCES merchant_qr_codes (id)
      )
    ''');
  }

  /// Validate merchant registration data
  MerchantValidation _validateMerchantData({
    required String businessName,
    required String phoneNumber,
    required String category,
  }) {
    if (businessName.trim().isEmpty) {
      return MerchantValidation(
        isValid: false,
        error: 'INVALID_BUSINESS_NAME',
        message: 'Business name is required',
      );
    }

    if (phoneNumber.trim().isEmpty) {
      return MerchantValidation(
        isValid: false,
        error: 'INVALID_PHONE',
        message: 'Phone number is required',
      );
    }

    // Validate Zambian phone number format
    final cleanPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    if (!_isValidZambianPhone(cleanPhone)) {
      return MerchantValidation(
        isValid: false,
        error: 'INVALID_PHONE_FORMAT',
        message: 'Invalid Zambian phone number format',
      );
    }

    if (category.trim().isEmpty) {
      return MerchantValidation(
        isValid: false,
        error: 'INVALID_CATEGORY',
        message: 'Business category is required',
      );
    }

    return MerchantValidation(
      isValid: true,
      message: 'Validation successful',
    );
  }

  /// Validate Zambian phone number
  bool _isValidZambianPhone(String phone) {
    // Zambian phone numbers: +260XXXXXXXXX or 0XXXXXXXXX or XXXXXXXXX
    if (phone.startsWith('260') && phone.length == 12) return true;
    if (phone.startsWith('0') && phone.length == 10) return true;
    if (phone.length == 9) return true;
    return false;
  }

  /// Get merchant by ID
  Future<Map<String, dynamic>?> _getMerchantById(String merchantId) async {
    try {
      final merchants = await _dbHelper.query(
        'merchants',
        where: 'id = ?',
        whereArgs: [merchantId],
        limit: 1,
      );
      return merchants.isNotEmpty ? merchants.first : null;
    } catch (e) {
      _logger.e('Failed to get merchant by ID: $e');
      return null;
    }
  }

  /// Get merchant by phone number
  Future<Map<String, dynamic>?> _getMerchantByPhone(String phoneNumber) async {
    try {
      final merchants = await _dbHelper.query(
        'merchants',
        where: 'phone_number = ?',
        whereArgs: [phoneNumber],
        limit: 1,
      );
      return merchants.isNotEmpty ? merchants.first : null;
    } catch (e) {
      _logger.e('Failed to get merchant by phone: $e');
      return null;
    }
  }

  /// Generate default QR codes for new merchant
  Future<void> _generateDefaultQRCodes(String merchantId) async {
    try {
      // Generate static QR (no amount)
      await generateMerchantQR(
        merchantId: merchantId,
        qrType: QRType.static,
        description: 'Pay to merchant',
      );

      // Generate common amount QRs
      final commonAmounts = [10.0, 20.0, 50.0, 100.0];
      for (final amount in commonAmounts) {
        await generateMerchantQR(
          merchantId: merchantId,
          amount: amount,
          qrType: QRType.dynamic,
          description: 'K${amount.toStringAsFixed(0)} payment',
        );
      }
    } catch (e) {
      _logger.e('Failed to generate default QR codes: $e');
    }
  }

  /// Validate payment
  Future<PaymentValidation> _validatePayment({
    required String merchantId,
    required double amount,
    required String payerUserId,
  }) async {
    if (amount <= 0) {
      return PaymentValidation(
        isValid: false,
        error: 'INVALID_AMOUNT',
        message: 'Amount must be greater than zero',
      );
    }

    if (amount < AppConstants.minTransactionAmount) {
      return PaymentValidation(
        isValid: false,
        error: 'AMOUNT_TOO_LOW',
        message: 'Amount below minimum transaction limit',
      );
    }

    if (amount > AppConstants.maxTransactionAmount) {
      return PaymentValidation(
        isValid: false,
        error: 'AMOUNT_TOO_HIGH',
        message: 'Amount exceeds maximum transaction limit',
      );
    }

    return PaymentValidation(
      isValid: true,
      message: 'Payment validation successful',
    );
  }

  /// Process online payment
  Future<PaymentProcessResult> _processOnlinePayment({
    required String transactionId,
    required String merchantPhone,
    required String payerUserId,
    required double amount,
    String? description,
  }) async {
    try {
      final result = await _mobileMoneyService.sendMoney(
        senderPhone: payerUserId,
        receiverPhone: merchantPhone,
        amount: amount,
        externalId: transactionId,
        message: description ?? 'Merchant payment',
      );

      return PaymentProcessResult(
        success: result.success,
        message: result.message ?? (result.success ? 'Payment successful' : 'Payment failed'),
      );
    } catch (e) {
      _logger.e('Online payment processing failed: $e');
      return PaymentProcessResult(
        success: false,
        message: 'Payment processing failed: ${e.toString()}',
      );
    }
  }

  /// Update QR scan count
  Future<void> _updateQRScanCount(String qrId) async {
    try {
      await _dbHelper.rawUpdate(
        'UPDATE merchant_qr_codes SET scan_count = scan_count + 1, last_scanned_at = ? WHERE id = ?',
        [DateTime.now().millisecondsSinceEpoch, qrId],
      );
    } catch (e) {
      _logger.e('Failed to update QR scan count: $e');
    }
  }
}

/// QR Type enumeration
enum QRType {
  static,  // No amount specified
  dynamic, // Amount specified
}

/// Merchant registration result
class MerchantRegistrationResult {
  final bool success;
  final String? merchantId;
  final String? error;
  final String message;

  MerchantRegistrationResult({
    required this.success,
    this.merchantId,
    this.error,
    required this.message,
  });
}

/// QR generation result
class QRGenerationResult {
  final bool success;
  final String? qrId;
  final Uint8List? qrData;
  final String? error;
  final String message;

  QRGenerationResult({
    required this.success,
    this.qrId,
    this.qrData,
    this.error,
    required this.message,
  });
}

/// Merchant payment result
class MerchantPaymentResult {
  final bool success;
  final String? transactionId;
  final double? amount;
  final String? merchantId;
  final String? error;
  final String message;
  final bool isOffline;

  MerchantPaymentResult({
    required this.success,
    this.transactionId,
    this.amount,
    this.merchantId,
    this.error,
    required this.message,
    this.isOffline = false,
  });
}

/// Merchant validation result
class MerchantValidation {
  final bool isValid;
  final String? error;
  final String message;

  MerchantValidation({
    required this.isValid,
    this.error,
    required this.message,
  });
}

/// Payment validation result
class PaymentValidation {
  final bool isValid;
  final String? error;
  final String message;

  PaymentValidation({
    required this.isValid,
    this.error,
    required this.message,
  });
}

/// Payment process result
class PaymentProcessResult {
  final bool success;
  final String message;

  PaymentProcessResult({
    required this.success,
    required this.message,
  });
}
