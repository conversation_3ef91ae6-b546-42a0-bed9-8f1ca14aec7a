// MOBILE MONEY DETECTOR - ANDROID NATIVE IMPLEMENTATION
// Enhanced detection for MTN, Airtel, and Zamtel mobile money services

package com.zm.paymule.real

import android.Manifest
import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.provider.Telephony
import android.telephony.TelephonyManager
import android.telephony.SignalStrength
import androidx.core.app.ActivityCompat
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.net.HttpURLConnection
import java.net.URL

class MobileMoneyDetector(private val context: Context) {
    
    companion object {
        private const val CHANNEL = "mobile_money_detector"
        
        // Mobile money app package patterns
        private val MOBILE_MONEY_PACKAGES = mapOf(
            "mtn" to listOf("mtn", "mobile.money", "mtnmoney"),
            "airtel" to listOf("airtel", "money", "airtelmoney"),
            "zamtel" to listOf("zamtel", "kwacha", "zamtelkwacha")
        )
        
        // SMS sender patterns for mobile money
        private val SMS_PATTERNS = mapOf(
            "mtn" to listOf("MTN", "MTNMM", "303", "mtn money"),
            "airtel" to listOf("AIRTEL", "AIRTELMONEY", "432", "airtel money"),
            "zamtel" to listOf("ZAMTEL", "KWACHA", "327", "zamtel kwacha")
        )
    }
    
    fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "getDualSIMInfo" -> {
                    try {
                        val simInfo = getDualSIMInfo()
                        result.success(simInfo)
                    } catch (e: Exception) {
                        result.error("SIM_ERROR", "Failed to get dual SIM info: ${e.message}", null)
                    }
                }
                "getInstalledApps" -> {
                    try {
                        val apps = getInstalledMobileMoneyApps()
                        result.success(apps)
                    } catch (e: Exception) {
                        result.error("APP_ERROR", "Failed to get installed apps: ${e.message}", null)
                    }
                }
                "getSMSHistory" -> {
                    try {
                        val smsHistory = getMobileMoneySMS()
                        result.success(smsHistory)
                    } catch (e: Exception) {
                        result.error("SMS_ERROR", "Failed to get SMS history: ${e.message}", null)
                    }
                }
                "getSignalStrength" -> {
                    try {
                        val signalInfo = getSignalStrength()
                        result.success(signalInfo)
                    } catch (e: Exception) {
                        result.error("SIGNAL_ERROR", "Failed to get signal strength: ${e.message}", null)
                    }
                }
                "testConnectivity" -> {
                    val endpoint = call.argument<String>("endpoint")
                    val timeout = call.argument<Int>("timeout") ?: 10000
                    
                    if (endpoint != null) {
                        Thread {
                            try {
                                val connected = testAPIConnectivity(endpoint, timeout)
                                result.success(mapOf("connected" to connected))
                            } catch (e: Exception) {
                                result.success(mapOf("connected" to false, "error" to e.message))
                            }
                        }.start()
                    } else {
                        result.error("INVALID_ARGS", "Endpoint is required", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
    
    private fun getDualSIMInfo(): List<Map<String, Any?>> {
        val simList = mutableListOf<Map<String, Any?>>()
        
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP_MR1) {
                val subscriptionManager = context.getSystemService(Context.TELEPHONY_SUBSCRIPTION_SERVICE) as android.telephony.SubscriptionManager
                
                if (hasPhonePermission()) {
                    val activeSubscriptions = subscriptionManager.activeSubscriptionInfoList
                    
                    activeSubscriptions?.forEach { subscription ->
                        val simInfo = mutableMapOf<String, Any?>()
                        simInfo["subscriptionId"] = subscription.subscriptionId
                        simInfo["displayName"] = subscription.displayName?.toString()
                        simInfo["carrierName"] = subscription.carrierName?.toString()
                        simInfo["countryIso"] = subscription.countryIso
                        simInfo["phoneNumber"] = subscription.number
                        simInfo["simSlotIndex"] = subscription.simSlotIndex
                        
                        // Detect mobile money provider
                        val provider = detectProviderFromCarrier(subscription.carrierName?.toString())
                        simInfo["mobileMoneyProvider"] = provider
                        simInfo["supportsMobileMoney"] = provider != "unknown"
                        
                        simList.add(simInfo)
                    }
                }
            }
        } catch (e: Exception) {
            // Return empty list if dual SIM detection fails
        }
        
        return simList
    }
    
    private fun getInstalledMobileMoneyApps(): List<Map<String, Any?>> {
        val mobileMoneyApps = mutableListOf<Map<String, Any?>>()
        
        try {
            val packageManager = context.packageManager
            val installedApps = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
            
            for (app in installedApps) {
                val appInfo = mutableMapOf<String, Any?>()
                val packageName = app.packageName.lowercase()
                val appName = packageManager.getApplicationLabel(app).toString().lowercase()
                
                // Check if it's a mobile money app
                val provider = detectProviderFromApp(packageName, appName)
                if (provider != "unknown") {
                    appInfo["packageName"] = app.packageName
                    appInfo["appName"] = packageManager.getApplicationLabel(app).toString()
                    appInfo["mobileMoneyProvider"] = provider
                    appInfo["isSystemApp"] = (app.flags and ApplicationInfo.FLAG_SYSTEM) != 0
                    appInfo["isEnabled"] = app.enabled
                    
                    try {
                        val packageInfo = packageManager.getPackageInfo(app.packageName, 0)
                        appInfo["versionName"] = packageInfo.versionName
                        appInfo["versionCode"] = packageInfo.versionCode
                        appInfo["installTime"] = packageInfo.firstInstallTime
                        appInfo["lastUpdateTime"] = packageInfo.lastUpdateTime
                    } catch (e: Exception) {
                        // Ignore package info errors
                    }
                    
                    mobileMoneyApps.add(appInfo)
                }
            }
        } catch (e: Exception) {
            // Return empty list if app detection fails
        }
        
        return mobileMoneyApps
    }
    
    private fun getMobileMoneySMS(): List<Map<String, Any?>> {
        val mobileMoneyMessages = mutableListOf<Map<String, Any?>>()
        
        try {
            if (!hasSMSPermission()) {
                return mobileMoneyMessages
            }
            
            val uri = Uri.parse("content://sms/inbox")
            val projection = arrayOf("address", "body", "date", "type")
            val sortOrder = "date DESC LIMIT 100" // Last 100 messages
            
            val cursor: Cursor? = context.contentResolver.query(uri, projection, null, null, sortOrder)
            
            cursor?.use {
                val addressIndex = it.getColumnIndex("address")
                val bodyIndex = it.getColumnIndex("body")
                val dateIndex = it.getColumnIndex("date")
                val typeIndex = it.getColumnIndex("type")
                
                while (it.moveToNext()) {
                    val sender = it.getString(addressIndex) ?: ""
                    val content = it.getString(bodyIndex) ?: ""
                    val date = it.getLong(dateIndex)
                    val type = it.getInt(typeIndex)
                    
                    // Check if it's a mobile money SMS
                    val provider = detectProviderFromSMS(sender, content)
                    if (provider != "unknown") {
                        val smsInfo = mutableMapOf<String, Any?>()
                        smsInfo["sender"] = sender
                        smsInfo["content"] = content
                        smsInfo["date"] = date
                        smsInfo["type"] = type
                        smsInfo["mobileMoneyProvider"] = provider
                        smsInfo["isIncoming"] = type == Telephony.Sms.MESSAGE_TYPE_INBOX
                        
                        mobileMoneyMessages.add(smsInfo)
                    }
                }
            }
        } catch (e: Exception) {
            // Return empty list if SMS reading fails
        }
        
        return mobileMoneyMessages
    }
    
    private fun getSignalStrength(): List<Map<String, Any?>> {
        val signalInfo = mutableListOf<Map<String, Any?>>()
        
        try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            
            if (hasPhonePermission()) {
                // Get current network operator
                val operatorName = telephonyManager.networkOperatorName
                val operatorCode = telephonyManager.networkOperator
                
                val networkInfo = mutableMapOf<String, Any?>()
                networkInfo["operatorName"] = operatorName
                networkInfo["operatorCode"] = operatorCode
                networkInfo["networkType"] = getNetworkTypeString(telephonyManager.networkType)
                networkInfo["isRoaming"] = telephonyManager.isNetworkRoaming
                
                // Estimate signal strength (simplified)
                val signalStrength = estimateSignalStrength(telephonyManager)
                networkInfo["signalStrength"] = signalStrength
                
                // Detect mobile money provider
                val provider = detectProviderFromCarrier(operatorName)
                networkInfo["mobileMoneyProvider"] = provider
                networkInfo["supportsMobileMoney"] = provider != "unknown"
                
                signalInfo.add(networkInfo)
            }
        } catch (e: Exception) {
            // Return empty list if signal detection fails
        }
        
        return signalInfo
    }
    
    private fun detectProviderFromCarrier(carrierName: String?): String {
        if (carrierName == null) return "unknown"
        
        val lowerName = carrierName.lowercase()
        return when {
            lowerName.contains("mtn") -> "mtn"
            lowerName.contains("airtel") -> "airtel"
            lowerName.contains("zamtel") -> "zamtel"
            else -> "unknown"
        }
    }
    
    private fun detectProviderFromApp(packageName: String, appName: String): String {
        for ((provider, patterns) in MOBILE_MONEY_PACKAGES) {
            for (pattern in patterns) {
                if (packageName.contains(pattern) || appName.contains(pattern)) {
                    return provider
                }
            }
        }
        return "unknown"
    }
    
    private fun detectProviderFromSMS(sender: String, content: String): String {
        val lowerSender = sender.lowercase()
        val lowerContent = content.lowercase()
        
        for ((provider, patterns) in SMS_PATTERNS) {
            for (pattern in patterns) {
                val lowerPattern = pattern.lowercase()
                if (lowerSender.contains(lowerPattern) || lowerContent.contains(lowerPattern)) {
                    return provider
                }
            }
        }
        return "unknown"
    }
    
    private fun estimateSignalStrength(telephonyManager: TelephonyManager): Int {
        // Simplified signal strength estimation
        // In a real implementation, you would use SignalStrength callbacks
        return when (telephonyManager.networkType) {
            TelephonyManager.NETWORK_TYPE_LTE -> 85 // Good LTE signal
            TelephonyManager.NETWORK_TYPE_HSPA,
            TelephonyManager.NETWORK_TYPE_HSDPA,
            TelephonyManager.NETWORK_TYPE_HSUPA -> 75 // Good 3G signal
            TelephonyManager.NETWORK_TYPE_UMTS -> 65 // Fair 3G signal
            TelephonyManager.NETWORK_TYPE_EDGE -> 45 // Poor 2G signal
            TelephonyManager.NETWORK_TYPE_GPRS -> 35 // Very poor 2G signal
            else -> 50 // Unknown, assume fair
        }
    }
    
    private fun getNetworkTypeString(networkType: Int): String {
        return when (networkType) {
            TelephonyManager.NETWORK_TYPE_GPRS -> "GPRS"
            TelephonyManager.NETWORK_TYPE_EDGE -> "EDGE"
            TelephonyManager.NETWORK_TYPE_UMTS -> "UMTS"
            TelephonyManager.NETWORK_TYPE_HSDPA -> "HSDPA"
            TelephonyManager.NETWORK_TYPE_HSUPA -> "HSUPA"
            TelephonyManager.NETWORK_TYPE_HSPA -> "HSPA"
            TelephonyManager.NETWORK_TYPE_LTE -> "LTE"
            TelephonyManager.NETWORK_TYPE_NR -> "5G"
            else -> "Unknown"
        }
    }
    
    private fun testAPIConnectivity(endpoint: String, timeout: Int): Boolean {
        return try {
            val url = URL(endpoint)
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "HEAD"
            connection.connectTimeout = timeout
            connection.readTimeout = timeout
            connection.connect()
            
            val responseCode = connection.responseCode
            connection.disconnect()
            
            responseCode in 200..299 || responseCode == 404 // 404 is OK for API endpoint test
        } catch (e: Exception) {
            false
        }
    }
    
    private fun hasPhonePermission(): Boolean {
        return ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.READ_PHONE_STATE
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    private fun hasSMSPermission(): Boolean {
        return ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.READ_SMS
        ) == PackageManager.PERMISSION_GRANTED
    }
}
