// ZAMBIA SIM VERIFICATION SERVICE
// Auto-detects MTN/Airtel/Zamtel networks and validates Zambian phone numbers

import 'dart:async';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';

enum ZambianNetwork {
  mtn,
  airtel,
  zamtel,
  unknown
}

class ZambianSIMInfo {
  final String? phoneNumber;
  final String? networkOperator;
  final String? countryCode;
  final ZambianNetwork network;
  final bool isZambianSIM;
  final String? simSerialNumber;

  ZambianSIMInfo({
    this.phoneNumber,
    this.networkOperator,
    this.countryCode,
    required this.network,
    required this.isZambianSIM,
    this.simSerialNumber,
  });

  Map<String, dynamic> toJson() => {
    'phoneNumber': phoneNumber,
    'networkOperator': networkOperator,
    'countryCode': countryCode,
    'network': network.toString(),
    'isZambianSIM': isZambianSIM,
    'simSerialNumber': simSerialNumber,
  };
}

class ZambiaSIM {
  static const MethodChannel _channel = MethodChannel('zambia_sim_detector');
  
  // Zambian network operator codes
  static const Map<String, ZambianNetwork> _zambianOperators = {
    '64501': ZambianNetwork.airtel,  // Airtel Zambia
    '64502': ZambianNetwork.mtn,     // MTN Zambia
    '64503': ZambianNetwork.zamtel,  // Zamtel
    '64504': ZambianNetwork.zamtel,  // Zamtel (alternative)
  };

  // Zambian phone number prefixes
  static const Map<String, ZambianNetwork> _zambianPrefixes = {
    '96': ZambianNetwork.mtn,     // MTN: +260 96
    '97': ZambianNetwork.airtel,  // Airtel: +260 97
    '95': ZambianNetwork.zamtel,  // Zamtel: +260 95
  };

  /// Auto-detects MTN/Airtel/Zamtel SIM cards
  static Future<ZambianSIMInfo> verify() async {
    try {
      // Request phone permission
      final phonePermission = await Permission.phone.request();
      if (!phonePermission.isGranted) {
        throw Exception('Phone permission required for SIM verification');
      }

      // Get SIM information
      final simInfo = await _getSIMInfo();
      
      // Detect Zambian network
      final network = _detectZambianNetwork(simInfo);
      
      // Validate if it's a Zambian SIM
      final isZambianSIM = _isZambianSIM(simInfo, network);
      
      // Extract phone number if available
      final phoneNumber = await _extractPhoneNumber(simInfo);
      
      final zambianSIMInfo = ZambianSIMInfo(
        phoneNumber: phoneNumber,
        networkOperator: simInfo['networkOperator'],
        countryCode: simInfo['countryCode'],
        network: network,
        isZambianSIM: isZambianSIM,
        simSerialNumber: simInfo['simSerialNumber'],
      );

      // Log verification result
      print('🇿🇲 Zambian SIM Verification Result:');
      print('Network: ${network.toString()}');
      print('Is Zambian SIM: $isZambianSIM');
      print('Phone Number: ${phoneNumber ?? 'Not available'}');

      return zambianSIMInfo;
      
    } catch (e) {
      print('❌ SIM verification failed: $e');
      
      // Return unknown SIM info on failure
      return ZambianSIMInfo(
        network: ZambianNetwork.unknown,
        isZambianSIM: false,
      );
    }
  }

  /// Get SIM card information from device
  static Future<Map<String, dynamic>> _getSIMInfo() async {
    try {
      if (Platform.isAndroid) {
        final result = await _channel.invokeMethod('getSIMInfo');
        return Map<String, dynamic>.from(result);
      } else {
        // iOS doesn't provide SIM info, use alternative detection
        return await _getIOSNetworkInfo();
      }
    } catch (e) {
      print('Failed to get SIM info: $e');
      return {};
    }
  }

  /// iOS network detection (limited)
  static Future<Map<String, dynamic>> _getIOSNetworkInfo() async {
    try {
      final result = await _channel.invokeMethod('getCarrierInfo');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      return {};
    }
  }

  /// Detect Zambian network from SIM info
  static ZambianNetwork _detectZambianNetwork(Map<String, dynamic> simInfo) {
    // Check by operator code (MCC+MNC)
    final operatorCode = simInfo['networkOperator'] as String?;
    if (operatorCode != null && _zambianOperators.containsKey(operatorCode)) {
      return _zambianOperators[operatorCode]!;
    }

    // Check by carrier name
    final carrierName = (simInfo['carrierName'] as String?)?.toLowerCase();
    if (carrierName != null) {
      if (carrierName.contains('mtn')) return ZambianNetwork.mtn;
      if (carrierName.contains('airtel')) return ZambianNetwork.airtel;
      if (carrierName.contains('zamtel')) return ZambianNetwork.zamtel;
    }

    // Check by country code
    final countryCode = simInfo['countryCode'] as String?;
    if (countryCode == 'ZM' || countryCode == '260') {
      // Default to MTN if we know it's Zambian but can't determine operator
      return ZambianNetwork.mtn;
    }

    return ZambianNetwork.unknown;
  }

  /// Check if SIM is from Zambia
  static bool _isZambianSIM(Map<String, dynamic> simInfo, ZambianNetwork network) {
    // Check if network is known Zambian operator
    if (network != ZambianNetwork.unknown) {
      return true;
    }

    // Check country code
    final countryCode = simInfo['countryCode'] as String?;
    if (countryCode == 'ZM' || countryCode == '260') {
      return true;
    }

    // Check operator code starts with 645 (Zambia MCC)
    final operatorCode = simInfo['networkOperator'] as String?;
    if (operatorCode != null && operatorCode.startsWith('645')) {
      return true;
    }

    return false;
  }

  /// Extract phone number from SIM
  static Future<String?> _extractPhoneNumber(Map<String, dynamic> simInfo) async {
    try {
      // Try to get phone number from SIM
      String? phoneNumber = simInfo['phoneNumber'] as String?;
      
      // Clean and format phone number
      if (phoneNumber != null) {
        phoneNumber = _formatZambianPhoneNumber(phoneNumber);
      }

      return phoneNumber;
    } catch (e) {
      print('Failed to extract phone number: $e');
      return null;
    }
  }

  /// Format Zambian phone number to standard format
  static String _formatZambianPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Handle different formats
    if (cleaned.startsWith('260')) {
      // Already has country code
      return '+$cleaned';
    } else if (cleaned.startsWith('0')) {
      // Remove leading 0 and add country code
      return '+260${cleaned.substring(1)}';
    } else if (cleaned.length == 9) {
      // 9 digits without country code
      return '+260$cleaned';
    } else {
      // Return as is with + prefix
      return '+$cleaned';
    }
  }

  /// Validate Zambian phone number format
  static bool isValidZambianNumber(String phoneNumber) {
    final cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Check if it's a valid Zambian number
    if (cleaned.startsWith('260')) {
      final localNumber = cleaned.substring(3);
      return _isValidLocalNumber(localNumber);
    } else if (cleaned.startsWith('0') && cleaned.length == 10) {
      final localNumber = cleaned.substring(1);
      return _isValidLocalNumber(localNumber);
    } else if (cleaned.length == 9) {
      return _isValidLocalNumber(cleaned);
    }
    
    return false;
  }

  /// Check if local number matches Zambian prefixes
  static bool _isValidLocalNumber(String localNumber) {
    if (localNumber.length != 9) return false;
    
    final prefix = localNumber.substring(0, 2);
    return _zambianPrefixes.containsKey(prefix);
  }

  /// Get network name for display
  static String getNetworkDisplayName(ZambianNetwork network) {
    switch (network) {
      case ZambianNetwork.mtn:
        return 'MTN Zambia';
      case ZambianNetwork.airtel:
        return 'Airtel Zambia';
      case ZambianNetwork.zamtel:
        return 'Zamtel';
      case ZambianNetwork.unknown:
        return 'Unknown Network';
    }
  }

  /// Get network color for UI
  static String getNetworkColor(ZambianNetwork network) {
    switch (network) {
      case ZambianNetwork.mtn:
        return '#FFCC00'; // MTN Yellow
      case ZambianNetwork.airtel:
        return '#FF0000'; // Airtel Red
      case ZambianNetwork.zamtel:
        return '#00AA00'; // Zamtel Green
      case ZambianNetwork.unknown:
        return '#808080'; // Gray
    }
  }

  /// Check if network supports mobile money
  static bool supportsMobileMoney(ZambianNetwork network) {
    return network == ZambianNetwork.mtn || 
           network == ZambianNetwork.airtel || 
           network == ZambianNetwork.zamtel;
  }

  /// Get mobile money service name
  static String getMobileMoneyServiceName(ZambianNetwork network) {
    switch (network) {
      case ZambianNetwork.mtn:
        return 'MTN Mobile Money';
      case ZambianNetwork.airtel:
        return 'Airtel Money';
      case ZambianNetwork.zamtel:
        return 'Zamtel Kwacha';
      case ZambianNetwork.unknown:
        return 'Unknown Service';
    }
  }

  /// Detect network from phone number
  static ZambianNetwork detectNetworkFromNumber(String phoneNumber) {
    final cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    String localNumber;
    
    if (cleaned.startsWith('260')) {
      localNumber = cleaned.substring(3);
    } else if (cleaned.startsWith('0')) {
      localNumber = cleaned.substring(1);
    } else {
      localNumber = cleaned;
    }
    
    if (localNumber.length >= 2) {
      final prefix = localNumber.substring(0, 2);
      return _zambianPrefixes[prefix] ?? ZambianNetwork.unknown;
    }
    
    return ZambianNetwork.unknown;
  }
}
