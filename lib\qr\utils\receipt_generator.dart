/// Receipt Generator for Zambian QR Payments
/// Creates visual receipts for offline transactions
/// Supports SMS receipts and printable formats

import 'dart:convert';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';
import 'package:intl/intl.dart';

import '../../core/constants/app_constants.dart';
import '../../core/security/encryption_service.dart';
import '../../data/database/database_helper.dart';
import '../zambia_qr_format.dart';

/// Receipt Generator for offline transactions
class ReceiptGenerator {
  static final ReceiptGenerator _instance = ReceiptGenerator._internal();
  factory ReceiptGenerator() => _instance;
  ReceiptGenerator._internal();

  static final Logger _logger = Logger();
  static final Uuid _uuid = Uuid();
  static final DatabaseHelper _dbHelper = DatabaseHelper();
  static final EncryptionService _encryption = EncryptionService();

  static bool _isInitialized = false;

  /// Initialize receipt generator
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _encryption.initialize();
      await _createReceiptTables();
      
      _isInitialized = true;
      _logger.i('🧾 Receipt generator initialized');
    } catch (e) {
      _logger.e('Failed to initialize receipt generator: $e');
      rethrow;
    }
  }

  /// Save offline receipt for transaction
  static Future<String> saveOfflineReceipt(
    ZambiaQRData data, {
    String? transactionId,
    String? payerName,
    String? merchantName,
    bool isOffline = true,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      final receiptId = _uuid.v4();
      final now = DateTime.now();

      // Generate receipt content
      final receiptContent = await _generateReceiptContent(
        data: data,
        transactionId: transactionId ?? receiptId,
        payerName: payerName,
        merchantName: merchantName,
        timestamp: now,
        isOffline: isOffline,
      );

      // Create visual receipt
      final visualReceipt = await _createVisualReceipt(receiptContent);

      // Generate SMS receipt
      final smsReceipt = _generateSMSReceipt(receiptContent);

      // Encrypt and store receipt
      final encryptedContent = await _encryption.encryptData(
        jsonEncode(receiptContent.toJson()),
      );

      final receiptRecord = {
        'id': receiptId,
        'transaction_id': transactionId ?? receiptId,
        'merchant_id': data.merchantId,
        'amount': data.amount,
        'currency': data.currency,
        'receipt_content': encryptedContent,
        'visual_receipt': visualReceipt,
        'sms_receipt': smsReceipt,
        'is_offline': isOffline ? 1 : 0,
        'created_at': now.millisecondsSinceEpoch,
      };

      await _dbHelper.insert('offline_receipts', receiptRecord);

      _logger.i('🧾 Offline receipt saved: $receiptId');
      _logger.d('   Transaction: ${transactionId ?? receiptId}');
      _logger.d('   Amount: K${data.amount}');
      _logger.d('   Merchant: ${data.merchantId.substring(0, 8)}...');

      return receiptId;
    } catch (e) {
      _logger.e('Failed to save offline receipt: $e');
      rethrow;
    }
  }

  /// Get receipt by ID
  static Future<ReceiptData?> getReceipt(String receiptId) async {
    try {
      if (!_isInitialized) await initialize();

      final receipts = await _dbHelper.query(
        'offline_receipts',
        where: 'id = ?',
        whereArgs: [receiptId],
        limit: 1,
      );

      if (receipts.isEmpty) return null;

      final receipt = receipts.first;
      
      // Decrypt receipt content
      final encryptedContent = receipt['receipt_content'] as String;
      final decryptedContent = await _encryption.decryptData(encryptedContent);
      final contentJson = jsonDecode(decryptedContent) as Map<String, dynamic>;
      final receiptContent = ReceiptContent.fromJson(contentJson);

      return ReceiptData(
        id: receipt['id'] as String,
        transactionId: receipt['transaction_id'] as String,
        content: receiptContent,
        visualReceipt: receipt['visual_receipt'] as String?,
        smsReceipt: receipt['sms_receipt'] as String,
        isOffline: (receipt['is_offline'] as int) == 1,
        createdAt: DateTime.fromMillisecondsSinceEpoch(receipt['created_at'] as int),
      );
    } catch (e) {
      _logger.e('Failed to get receipt: $e');
      return null;
    }
  }

  /// Get receipts for transaction
  static Future<List<ReceiptData>> getReceiptsForTransaction(String transactionId) async {
    try {
      if (!_isInitialized) await initialize();

      final receipts = await _dbHelper.query(
        'offline_receipts',
        where: 'transaction_id = ?',
        whereArgs: [transactionId],
        orderBy: 'created_at DESC',
      );

      final receiptList = <ReceiptData>[];
      for (final receipt in receipts) {
        try {
          final receiptData = await getReceipt(receipt['id'] as String);
          if (receiptData != null) {
            receiptList.add(receiptData);
          }
        } catch (e) {
          _logger.e('Failed to parse receipt ${receipt['id']}: $e');
        }
      }

      return receiptList;
    } catch (e) {
      _logger.e('Failed to get receipts for transaction: $e');
      return [];
    }
  }

  /// Generate printable receipt widget
  static Widget buildPrintableReceipt(ReceiptContent content) {
    return Container(
      width: 300,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          const Text(
            'PAY MULE ZAMBIA',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2E7D32),
            ),
          ),
          const Text(
            'Mobile Money Receipt',
            style: TextStyle(fontSize: 12, color: Colors.grey),
          ),
          const SizedBox(height: 16),

          // Transaction details
          _buildReceiptRow('Transaction ID:', content.transactionId),
          _buildReceiptRow('Date:', DateFormat('dd/MM/yyyy HH:mm').format(content.timestamp)),
          _buildReceiptRow('Amount:', 'K${content.amount.toStringAsFixed(2)} ${content.currency}'),
          
          if (content.merchantName != null)
            _buildReceiptRow('Merchant:', content.merchantName!),
          
          if (content.payerName != null)
            _buildReceiptRow('Payer:', content.payerName!),
          
          _buildReceiptRow('Provider:', content.provider),
          
          if (content.description != null)
            _buildReceiptRow('Description:', content.description!),

          const SizedBox(height: 16),

          // Status
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: content.isOffline ? Colors.orange.shade100 : Colors.green.shade100,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              content.isOffline ? 'OFFLINE TRANSACTION' : 'COMPLETED',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: content.isOffline ? Colors.orange.shade800 : Colors.green.shade800,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Footer
          const Text(
            'Keep this receipt for your records',
            style: TextStyle(fontSize: 10, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          
          if (content.isOffline)
            const Text(
              'Transaction will be processed when online',
              style: TextStyle(fontSize: 10, color: Colors.orange),
              textAlign: TextAlign.center,
            ),

          const SizedBox(height: 8),
          
          Text(
            'Receipt ID: ${content.receiptId}',
            style: const TextStyle(fontSize: 8, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// Share receipt via SMS or other methods
  static Future<bool> shareReceipt(String receiptId, {String? phoneNumber}) async {
    try {
      final receiptData = await getReceipt(receiptId);
      if (receiptData == null) return false;

      // For SMS sharing
      if (phoneNumber != null) {
        return await _sendSMSReceipt(phoneNumber, receiptData.smsReceipt);
      }

      // For other sharing methods (clipboard, etc.)
      await Clipboard.setData(ClipboardData(text: receiptData.smsReceipt));
      return true;
    } catch (e) {
      _logger.e('Failed to share receipt: $e');
      return false;
    }
  }

  /// Generate receipt content
  static Future<ReceiptContent> _generateReceiptContent({
    required ZambiaQRData data,
    required String transactionId,
    String? payerName,
    String? merchantName,
    required DateTime timestamp,
    required bool isOffline,
  }) async {
    return ReceiptContent(
      receiptId: _uuid.v4(),
      transactionId: transactionId,
      amount: data.amount,
      currency: data.currency,
      merchantId: data.merchantId,
      merchantName: merchantName,
      payerName: payerName,
      provider: data.provider,
      description: data.description,
      timestamp: timestamp,
      isOffline: isOffline,
    );
  }

  /// Create visual receipt as base64 image
  static Future<String?> _createVisualReceipt(ReceiptContent content) async {
    try {
      // This would generate a visual receipt image
      // For now, return null as it requires complex rendering
      return null;
    } catch (e) {
      _logger.e('Failed to create visual receipt: $e');
      return null;
    }
  }

  /// Generate SMS receipt text
  static String _generateSMSReceipt(ReceiptContent content) {
    final buffer = StringBuffer();
    
    buffer.writeln('PAY MULE RECEIPT');
    buffer.writeln('================');
    buffer.writeln('ID: ${content.transactionId.substring(0, 8)}');
    buffer.writeln('Amount: K${content.amount.toStringAsFixed(2)}');
    
    if (content.merchantName != null) {
      buffer.writeln('To: ${content.merchantName}');
    }
    
    buffer.writeln('Date: ${DateFormat('dd/MM/yy HH:mm').format(content.timestamp)}');
    buffer.writeln('Provider: ${content.provider}');
    
    if (content.isOffline) {
      buffer.writeln('Status: OFFLINE - Will process when online');
    } else {
      buffer.writeln('Status: COMPLETED');
    }
    
    buffer.writeln('================');
    buffer.writeln('Keep this SMS for your records');

    return buffer.toString();
  }

  /// Send SMS receipt
  static Future<bool> _sendSMSReceipt(String phoneNumber, String receiptText) async {
    try {
      // This would integrate with SMS service
      // For now, just log the action
      _logger.i('📱 SMS receipt sent to $phoneNumber');
      return true;
    } catch (e) {
      _logger.e('Failed to send SMS receipt: $e');
      return false;
    }
  }

  /// Create database tables for receipts
  static Future<void> _createReceiptTables() async {
    final db = await _dbHelper.database;

    await db.execute('''
      CREATE TABLE IF NOT EXISTS offline_receipts (
        id TEXT PRIMARY KEY,
        transaction_id TEXT NOT NULL,
        merchant_id TEXT NOT NULL,
        amount REAL NOT NULL,
        currency TEXT NOT NULL,
        receipt_content TEXT NOT NULL,
        visual_receipt TEXT,
        sms_receipt TEXT NOT NULL,
        is_offline INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL
      )
    ''');

    // Create indexes
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_offline_receipts_transaction 
      ON offline_receipts(transaction_id)
    ''');

    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_offline_receipts_merchant 
      ON offline_receipts(merchant_id)
    ''');
  }

  /// Build receipt row widget
  static Widget _buildReceiptRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          Flexible(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}

/// Receipt content model
class ReceiptContent {
  final String receiptId;
  final String transactionId;
  final double amount;
  final String currency;
  final String merchantId;
  final String? merchantName;
  final String? payerName;
  final String provider;
  final String? description;
  final DateTime timestamp;
  final bool isOffline;

  ReceiptContent({
    required this.receiptId,
    required this.transactionId,
    required this.amount,
    required this.currency,
    required this.merchantId,
    this.merchantName,
    this.payerName,
    required this.provider,
    this.description,
    required this.timestamp,
    required this.isOffline,
  });

  Map<String, dynamic> toJson() {
    return {
      'receiptId': receiptId,
      'transactionId': transactionId,
      'amount': amount,
      'currency': currency,
      'merchantId': merchantId,
      'merchantName': merchantName,
      'payerName': payerName,
      'provider': provider,
      'description': description,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'isOffline': isOffline,
    };
  }

  factory ReceiptContent.fromJson(Map<String, dynamic> json) {
    return ReceiptContent(
      receiptId: json['receiptId'] as String,
      transactionId: json['transactionId'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      merchantId: json['merchantId'] as String,
      merchantName: json['merchantName'] as String?,
      payerName: json['payerName'] as String?,
      provider: json['provider'] as String,
      description: json['description'] as String?,
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int),
      isOffline: json['isOffline'] as bool,
    );
  }
}

/// Receipt data model
class ReceiptData {
  final String id;
  final String transactionId;
  final ReceiptContent content;
  final String? visualReceipt;
  final String smsReceipt;
  final bool isOffline;
  final DateTime createdAt;

  ReceiptData({
    required this.id,
    required this.transactionId,
    required this.content,
    this.visualReceipt,
    required this.smsReceipt,
    required this.isOffline,
    required this.createdAt,
  });
}
