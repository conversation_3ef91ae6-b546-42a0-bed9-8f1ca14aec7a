// DEMO MODE VALIDATOR
// Validates that demo mode is properly disabled in production

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'environment_validator.dart';

class DemoMode {
  static bool _isInitialized = false;
  static bool _demoModeActive = false;
  static Map<String, dynamic> _demoConfig = {};
  static List<String> _demoDataSources = [];

  /// Initialize demo mode detection
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🔍 Initializing demo mode detection...');

      // Detect demo mode from multiple sources
      _demoModeActive = await _detectDemoMode();
      _demoConfig = await _loadDemoConfig();
      _demoDataSources = await _findDemoDataSources();
      _isInitialized = true;

      print('✅ Demo mode detection complete');
      print('🎭 Demo mode active: $_demoModeActive');
      print('📋 Demo data sources: ${_demoDataSources.length}');

    } catch (e) {
      print('❌ Demo mode detection failed: $e');
      _demoModeActive = false;
    }
  }

  /// Check if demo mode is currently active
  static bool get active {
    _ensureInitialized();
    return _demoModeActive;
  }

  /// Check if demo mode is disabled (inverse of active)
  static bool get disabled {
    _ensureInitialized();
    return !_demoModeActive;
  }

  /// Get demo configuration
  static Map<String, dynamic> get config {
    _ensureInitialized();
    return Map.from(_demoConfig);
  }

  /// Get demo data sources
  static List<String> get dataSources {
    _ensureInitialized();
    return List.from(_demoDataSources);
  }

  /// Detect demo mode from multiple sources
  static Future<bool> _detectDemoMode() async {
    try {
      final detectionResults = <String, bool>{};

      // Check environment variables
      detectionResults['env_var'] = _checkEnvironmentVariable();

      // Check dart defines
      detectionResults['dart_define'] = _checkDartDefines();

      // Check configuration files
      detectionResults['config_file'] = await _checkConfigurationFiles();

      // Check demo data presence
      detectionResults['demo_data'] = await _checkDemoDataPresence();

      // Check debug flags
      detectionResults['debug_flags'] = _checkDebugFlags();

      // Check build configuration
      detectionResults['build_config'] = _checkBuildConfiguration();

      // Log detection results
      print('🔍 Demo mode detection results:');
      detectionResults.forEach((source, result) {
        print('   $source: $result');
      });

      // Demo mode is active if ANY source indicates it
      final isDemoActive = detectionResults.values.any((result) => result);

      return isDemoActive;

    } catch (e) {
      print('❌ Demo mode detection error: $e');
      return false;
    }
  }

  /// Check environment variable for demo mode
  static bool _checkEnvironmentVariable() {
    try {
      final demoEnv = Platform.environment['DEMO_MODE'];
      return demoEnv?.toLowerCase() == 'true' || demoEnv == '1';
    } catch (e) {
      return false;
    }
  }

  /// Check dart defines for demo mode
  static bool _checkDartDefines() {
    try {
      const demoMode = String.fromEnvironment('DEMO_MODE', defaultValue: 'false');
      const testMode = String.fromEnvironment('TEST_MODE', defaultValue: 'false');
      
      return demoMode.toLowerCase() == 'true' || testMode.toLowerCase() == 'true';
    } catch (e) {
      return false;
    }
  }

  /// Check configuration files for demo settings
  static Future<bool> _checkConfigurationFiles() async {
    try {
      // Check if demo configuration exists
      final demoConfigExists = await _fileExists('assets/config/demo_config.json');
      final testConfigExists = await _fileExists('assets/config/test_config.json');
      
      return demoConfigExists || testConfigExists;
    } catch (e) {
      return false;
    }
  }

  /// Check for demo data presence
  static Future<bool> _checkDemoDataPresence() async {
    try {
      final demoDataChecks = <Future<bool>>[
        _fileExists('assets/data/demo_users.json'),
        _fileExists('assets/data/demo_transactions.json'),
        _fileExists('assets/data/test_accounts.json'),
        _directoryExists('assets/demo'),
        _directoryExists('assets/test'),
      ];

      final results = await Future.wait(demoDataChecks);
      return results.any((exists) => exists);
    } catch (e) {
      return false;
    }
  }

  /// Check debug flags
  static bool _checkDebugFlags() {
    try {
      // Demo mode is often enabled in debug builds
      if (kDebugMode) {
        // Check for specific debug flags that might indicate demo mode
        const debugDemo = bool.fromEnvironment('DEBUG_DEMO', defaultValue: false);
        return debugDemo;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Check build configuration
  static bool _checkBuildConfiguration() {
    try {
      // Check if this is a debug or profile build (often has demo mode)
      if (kDebugMode || kProfileMode) {
        // Additional checks for demo indicators in build
        const flavor = String.fromEnvironment('FLAVOR', defaultValue: '');
        return flavor.contains('demo') || flavor.contains('test');
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Load demo configuration
  static Future<Map<String, dynamic>> _loadDemoConfig() async {
    try {
      if (!_demoModeActive) {
        return {};
      }

      // Load demo-specific configuration
      return {
        'demoUsers': await _loadDemoUsers(),
        'demoTransactions': await _loadDemoTransactions(),
        'demoAccounts': await _loadDemoAccounts(),
        'demoSettings': await _loadDemoSettings(),
      };

    } catch (e) {
      print('❌ Failed to load demo config: $e');
      return {};
    }
  }

  /// Find demo data sources
  static Future<List<String>> _findDemoDataSources() async {
    try {
      final sources = <String>[];

      // Check for various demo data files and directories
      final demoSources = [
        'assets/data/demo_users.json',
        'assets/data/demo_transactions.json',
        'assets/data/test_accounts.json',
        'assets/demo/',
        'assets/test/',
        'lib/demo/',
        'lib/test_data/',
      ];

      for (final source in demoSources) {
        if (await _fileExists(source) || await _directoryExists(source)) {
          sources.add(source);
        }
      }

      return sources;

    } catch (e) {
      print('❌ Failed to find demo data sources: $e');
      return [];
    }
  }

  /// Load demo users
  static Future<List<Map<String, dynamic>>> _loadDemoUsers() async {
    try {
      // This would load from actual demo data file
      return [
        {
          'id': 'demo_user_1',
          'name': 'Demo User 1',
          'phone': '+************',
          'provider': 'mtn',
        },
        {
          'id': 'demo_user_2',
          'name': 'Demo User 2',
          'phone': '+************',
          'provider': 'airtel',
        },
      ];
    } catch (e) {
      return [];
    }
  }

  /// Load demo transactions
  static Future<List<Map<String, dynamic>>> _loadDemoTransactions() async {
    try {
      // This would load from actual demo data file
      return [
        {
          'id': 'demo_tx_1',
          'amount': 100.0,
          'type': 'send',
          'status': 'completed',
        },
        {
          'id': 'demo_tx_2',
          'amount': 50.0,
          'type': 'receive',
          'status': 'completed',
        },
      ];
    } catch (e) {
      return [];
    }
  }

  /// Load demo accounts
  static Future<List<Map<String, dynamic>>> _loadDemoAccounts() async {
    try {
      // This would load from actual demo data file
      return [
        {
          'id': 'demo_account_1',
          'provider': 'mtn',
          'balance': 1000.0,
          'phone': '+************',
        },
        {
          'id': 'demo_account_2',
          'provider': 'airtel',
          'balance': 500.0,
          'phone': '+************',
        },
      ];
    } catch (e) {
      return [];
    }
  }

  /// Load demo settings
  static Future<Map<String, dynamic>> _loadDemoSettings() async {
    try {
      return {
        'enableDemoTransactions': true,
        'enableDemoNotifications': true,
        'demoTransactionDelay': 2000,
        'demoSuccessRate': 0.95,
        'enableDemoErrors': true,
      };
    } catch (e) {
      return {};
    }
  }

  /// Check if file exists
  static Future<bool> _fileExists(String path) async {
    try {
      final file = File(path);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// Check if directory exists
  static Future<bool> _directoryExists(String path) async {
    try {
      final directory = Directory(path);
      return await directory.exists();
    } catch (e) {
      return false;
    }
  }

  /// Ensure demo mode detection is initialized
  static void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('Demo mode not initialized. Call DemoMode.initialize() first.');
    }
  }

  /// Force set demo mode (for testing)
  static void setDemoMode(bool active, {Map<String, dynamic>? config}) {
    _demoModeActive = active;
    if (config != null) {
      _demoConfig = config;
    }
    _isInitialized = true;
  }

  /// Get demo mode summary
  static Map<String, dynamic> getSummary() {
    _ensureInitialized();
    return {
      'active': _demoModeActive,
      'disabled': disabled,
      'dataSources': _demoDataSources,
      'configKeys': _demoConfig.keys.toList(),
      'environment': Environment.isProduction ? 'production' : 'non-production',
      'buildMode': kReleaseMode ? 'release' : (kProfileMode ? 'profile' : 'debug'),
    };
  }

  /// Validate demo mode requirements
  static bool validateRequirements({
    bool requireDisabled = false,
    bool requireEnabled = false,
  }) {
    _ensureInitialized();

    if (requireDisabled && active) return false;
    if (requireEnabled && !active) return false;

    return true;
  }

  /// Clear demo data (for production cleanup)
  static Future<void> clearDemoData() async {
    try {
      print('🧹 Clearing demo data...');

      // This would remove demo data files and reset demo state
      _demoModeActive = false;
      _demoConfig.clear();
      _demoDataSources.clear();

      print('✅ Demo data cleared');
    } catch (e) {
      print('❌ Failed to clear demo data: $e');
    }
  }
}
