// ZAMBIAN AUTHENTICATION FLOW
// Complete authentication flow: SIM verification → SMS OTP → Security PIN

import 'dart:async';
import 'package:flutter/material.dart';
import 'zambia_sim_service.dart';
import 'sms_otp_service.dart';
import 'security_pin_service.dart';

enum AuthStep {
  simVerification,
  smsOTP,
  securityPIN,
  completed
}

class ZambianAuthResult {
  final bool success;
  final String? message;
  final AuthStep currentStep;
  final ZambianSIMInfo? simInfo;
  final String? phoneNumber;

  ZambianAuthResult({
    required this.success,
    this.message,
    required this.currentStep,
    this.simInfo,
    this.phoneNumber,
  });
}

class ZambianAuthFlow {
  static AuthStep _currentStep = AuthStep.simVerification;
  static ZambianSIMInfo? _simInfo;
  static String? _verifiedPhoneNumber;

  /// Complete Zambian authentication flow
  static Future<ZambianAuthResult> authenticate() async {
    try {
      print('🇿🇲 Starting Zambian authentication flow...');

      // Step 1: SIM Verification
      final simResult = await _performSIMVerification();
      if (!simResult.success) {
        return simResult;
      }

      // Step 2: SMS OTP (if phone number available)
      if (_simInfo?.phoneNumber != null) {
        final otpResult = await _performSMSOTP(_simInfo!.phoneNumber!);
        if (!otpResult.success) {
          return otpResult;
        }
      }

      // Step 3: Security PIN Setup
      final pinResult = await _performSecurityPINSetup();
      if (!pinResult.success) {
        return pinResult;
      }

      print('✅ Zambian authentication flow completed successfully');
      _currentStep = AuthStep.completed;

      return ZambianAuthResult(
        success: true,
        message: 'Authentication completed successfully',
        currentStep: AuthStep.completed,
        simInfo: _simInfo,
        phoneNumber: _verifiedPhoneNumber,
      );

    } catch (e) {
      print('❌ Authentication flow failed: $e');
      return ZambianAuthResult(
        success: false,
        message: 'Authentication failed: ${e.toString()}',
        currentStep: _currentStep,
      );
    }
  }

  /// Step 1: SIM Verification
  static Future<ZambianAuthResult> _performSIMVerification() async {
    try {
      print('📱 Step 1: SIM Verification');
      _currentStep = AuthStep.simVerification;

      // Auto-detect MTN/Airtel/Zamtel
      _simInfo = await ZambiaSIM.verify();

      if (_simInfo == null) {
        return ZambianAuthResult(
          success: false,
          message: 'Failed to detect SIM card',
          currentStep: AuthStep.simVerification,
        );
      }

      if (!_simInfo!.isZambianSIM) {
        return ZambianAuthResult(
          success: false,
          message: 'Please insert a Zambian SIM card (MTN, Airtel, or Zamtel)',
          currentStep: AuthStep.simVerification,
        );
      }

      final networkName = ZambiaSIM.getNetworkDisplayName(_simInfo!.network);
      print('✅ Zambian SIM detected: $networkName');

      return ZambianAuthResult(
        success: true,
        message: 'Zambian SIM verified: $networkName',
        currentStep: AuthStep.simVerification,
        simInfo: _simInfo,
      );

    } catch (e) {
      return ZambianAuthResult(
        success: false,
        message: 'SIM verification failed: ${e.toString()}',
        currentStep: AuthStep.simVerification,
      );
    }
  }

  /// Step 2: SMS OTP Verification
  static Future<ZambianAuthResult> _performSMSOTP(String phoneNumber) async {
    try {
      print('📱 Step 2: SMS OTP Verification');
      _currentStep = AuthStep.smsOTP;

      // Request SMS OTP - Real SMS sent to your number
      final otpResult = await SMSOTP.request(
        phoneNumber: phoneNumber,
        network: _simInfo?.network,
      );

      if (!otpResult.success) {
        return ZambianAuthResult(
          success: false,
          message: otpResult.message ?? 'Failed to send SMS OTP',
          currentStep: AuthStep.smsOTP,
        );
      }

      print('✅ SMS OTP sent successfully');
      _verifiedPhoneNumber = phoneNumber;

      return ZambianAuthResult(
        success: true,
        message: 'SMS OTP sent to $phoneNumber',
        currentStep: AuthStep.smsOTP,
        phoneNumber: phoneNumber,
      );

    } catch (e) {
      return ZambianAuthResult(
        success: false,
        message: 'SMS OTP failed: ${e.toString()}',
        currentStep: AuthStep.smsOTP,
      );
    }
  }

  /// Step 3: Security PIN Setup
  static Future<ZambianAuthResult> _performSecurityPINSetup() async {
    try {
      print('🔐 Step 3: Security PIN Setup');
      _currentStep = AuthStep.securityPIN;

      // Check if PIN is already setup
      final isPINSetup = await SecurityPINService.isPINSetup();
      if (isPINSetup) {
        print('✅ Security PIN already configured');
        return ZambianAuthResult(
          success: true,
          message: 'Security PIN already configured',
          currentStep: AuthStep.securityPIN,
        );
      }

      // PIN setup will be handled by UI
      return ZambianAuthResult(
        success: true,
        message: 'Ready for security PIN setup',
        currentStep: AuthStep.securityPIN,
      );

    } catch (e) {
      return ZambianAuthResult(
        success: false,
        message: 'Security PIN setup failed: ${e.toString()}',
        currentStep: AuthStep.securityPIN,
      );
    }
  }

  /// Verify SMS OTP code
  static SMSOTPVerification verifyOTPCode(String otpCode) {
    return SMSOTP.verify(otpCode);
  }

  /// Setup security PIN
  static Future<SecurityPINResult> setupSecurityPIN({
    required String pin,
    required String confirmPin,
    int minLength = 6,
  }) async {
    return await SecurityPINService.setupSecurityPIN(
      pin: pin,
      confirmPin: confirmPin,
      minLength: minLength,
      enableBiometric: true,
    );
  }

  /// Quick authentication for returning users
  static Future<bool> quickAuth() async {
    try {
      // Try biometric first
      final biometricAuth = await SecurityPINService.authenticateWithBiometric();
      if (biometricAuth) {
        print('✅ Quick authentication via biometric');
        return true;
      }

      // Fallback to PIN verification (handled by UI)
      return false;

    } catch (e) {
      print('❌ Quick authentication failed: $e');
      return false;
    }
  }

  /// Verify PIN for authentication
  static Future<PINValidationResult> verifyPIN(String pin) async {
    return await SecurityPINService.verifyPIN(pin);
  }

  /// Get current authentication step
  static AuthStep getCurrentStep() {
    return _currentStep;
  }

  /// Get detected SIM info
  static ZambianSIMInfo? getSIMInfo() {
    return _simInfo;
  }

  /// Get verified phone number
  static String? getVerifiedPhoneNumber() {
    return _verifiedPhoneNumber;
  }

  /// Check if user is fully authenticated
  static Future<bool> isAuthenticated() async {
    try {
      // Check if PIN is setup
      final isPINSetup = await SecurityPINService.isPINSetup();
      if (!isPINSetup) return false;

      // Check if SIM is verified
      if (_simInfo == null || !_simInfo!.isZambianSIM) {
        // Re-verify SIM
        _simInfo = await ZambiaSIM.verify();
        if (_simInfo == null || !_simInfo!.isZambianSIM) {
          return false;
        }
      }

      return true;

    } catch (e) {
      return false;
    }
  }

  /// Reset authentication state
  static void reset() {
    _currentStep = AuthStep.simVerification;
    _simInfo = null;
    _verifiedPhoneNumber = null;
  }

  /// Get authentication progress percentage
  static double getProgress() {
    switch (_currentStep) {
      case AuthStep.simVerification:
        return 0.25;
      case AuthStep.smsOTP:
        return 0.50;
      case AuthStep.securityPIN:
        return 0.75;
      case AuthStep.completed:
        return 1.0;
    }
  }

  /// Get step description
  static String getStepDescription(AuthStep step) {
    switch (step) {
      case AuthStep.simVerification:
        return 'Verifying Zambian SIM card...';
      case AuthStep.smsOTP:
        return 'Sending SMS verification code...';
      case AuthStep.securityPIN:
        return 'Setting up security PIN...';
      case AuthStep.completed:
        return 'Authentication completed!';
    }
  }

  /// Get network-specific welcome message
  static String getWelcomeMessage() {
    if (_simInfo == null) return 'Welcome to Pay Mule Zambia!';

    final networkName = ZambiaSIM.getNetworkDisplayName(_simInfo!.network);
    final serviceName = ZambiaSIM.getMobileMoneyServiceName(_simInfo!.network);
    
    return 'Welcome to Pay Mule!\nConnected to $networkName\n$serviceName ready for transactions';
  }

  /// Check if mobile money is supported
  static bool isMobileMoneySupported() {
    if (_simInfo == null) return false;
    return ZambiaSIM.supportsMobileMoney(_simInfo!.network);
  }

  /// Get available mobile money services
  static List<String> getAvailableServices() {
    final services = <String>[];
    
    if (_simInfo != null && _simInfo!.isZambianSIM) {
      // Add detected network service
      final serviceName = ZambiaSIM.getMobileMoneyServiceName(_simInfo!.network);
      if (serviceName != 'Unknown Service') {
        services.add(serviceName);
      }
      
      // Add other Zambian services
      if (_simInfo!.network != ZambianNetwork.mtn) {
        services.add('MTN Mobile Money');
      }
      if (_simInfo!.network != ZambianNetwork.airtel) {
        services.add('Airtel Money');
      }
      if (_simInfo!.network != ZambianNetwork.zamtel) {
        services.add('Zamtel Kwacha');
      }
    }
    
    return services;
  }
}
