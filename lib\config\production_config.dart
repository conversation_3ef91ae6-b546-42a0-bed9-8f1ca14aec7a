// PRODUCTION CONFIGURATION - REAL ENDPOINTS ONLY
// This file contains live production endpoints for Zambian mobile money providers

class ProductionConfig {
  static const bool isProduction = true;
  static const bool isDemoMode = false;
  static const bool isTestMode = false;
  
  // MTN Mobile Money - Production
  static const String mtnBaseUrl = 'https://api.mtn.com/v1';
  static const String mtnEnvironment = 'production';
  static const String mtnCallbackUrl = 'https://paymule.zambiapay.com/mtn/callback';
  
  // Airtel Money - Production
  static const String airtelBaseUrl = 'https://api.airtel.africa/v1';
  static const String airtelEnvironment = 'production';
  static const String airtelCallbackUrl = 'https://paymule.zambiapay.com/airtel/callback';
  
  // Zamtel Kwacha - Production
  static const String zamtelBaseUrl = 'https://api.zamtel.zm/kwacha/v1';
  static const String zamtelEnvironment = 'production';
  static const String zamtelCallbackUrl = 'https://paymule.zambiapay.com/zamtel/callback';
  
  // Security Configuration
  static const bool useHttpsOnly = true;
  static const bool enableCertificatePinning = true;
  static const bool enableEncryption = true;
  
  // Performance Configuration
  static const int networkTimeoutSeconds = 30;
  static const int maxRetryAttempts = 3;
  static const bool enableOfflineMode = true;
}
