// LAUNCH NOTIFICATION DEMO
// Demonstrates Zambian launch notification system

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import '../core/notifications/launch_notification_service.dart';

class LaunchNotificationDemo extends StatefulWidget {
  @override
  _LaunchNotificationDemoState createState() => _LaunchNotificationDemoState();
}

class _LaunchNotificationDemoState extends State<LaunchNotificationDemo> {
  final TextEditingController _messageController = TextEditingController(
    text: 'Pay Mule is live! Send money, pay bills, chilimba with friends'
  );
  final TextEditingController _recipientsController = TextEditingController(
    text: '+26096XXXXXXX'
  );
  final TextEditingController _campaignController = TextEditingController();
  
  ZambianNetwork selectedNetwork = ZambianNetwork.mtn;
  bool isSending = false;
  LaunchNotificationResult? lastResult;
  
  List<String> notificationLog = [];

  @override
  void dispose() {
    _messageController.dispose();
    _recipientsController.dispose();
    _campaignController.dispose();
    super.dispose();
  }

  /// Send launch notification
  Future<void> _sendLaunchNotification() async {
    final message = _messageController.text.trim();
    final recipientsText = _recipientsController.text.trim();
    final campaignId = _campaignController.text.trim();

    if (message.isEmpty || recipientsText.isEmpty) {
      _showSnackBar('Please fill in message and recipients', isError: true);
      return;
    }

    // Parse recipients
    final recipients = recipientsText
        .split(',')
        .map((r) => r.trim())
        .where((r) => r.isNotEmpty)
        .toList();

    if (recipients.isEmpty) {
      _showSnackBar('No valid recipients found', isError: true);
      return;
    }

    setState(() {
      isSending = true;
      notificationLog.clear();
    });

    try {
      _addToLog('🚀 Starting launch notification...');
      _addToLog('📱 Message: $message');
      _addToLog('👥 Recipients: ${recipients.length}');
      _addToLog('📡 Network: ${_getNetworkDisplayName(selectedNetwork)}');

      final request = LaunchNotificationRequest(
        message: message,
        recipients: recipients,
        network: selectedNetwork,
        campaignId: campaignId.isNotEmpty ? campaignId : null,
      );

      final result = await LaunchNotificationService.sendLaunchNotification(request);

      setState(() {
        lastResult = result;
        isSending = false;
      });

      if (result.success) {
        _addToLog('✅ Launch notification completed!');
        _addToLog('📊 Successful: ${result.successfulSends}/${result.totalRecipients}');
        _showSnackBar('Launch notification sent successfully!', isError: false);
      } else {
        _addToLog('❌ Launch notification failed: ${result.message}');
        _showSnackBar('Launch notification failed: ${result.message}', isError: true);
      }

    } catch (e) {
      setState(() {
        isSending = false;
      });
      _addToLog('❌ Error: ${e.toString()}');
      _showSnackBar('Error: ${e.toString()}', isError: true);
    }
  }

  /// Add message to notification log
  void _addToLog(String message) {
    setState(() {
      notificationLog.add('${DateTime.now().toString().substring(11, 19)}: $message');
      if (notificationLog.length > 20) {
        notificationLog.removeAt(0);
      }
    });
  }

  /// Generate script command
  String _generateScriptCommand() {
    final message = _messageController.text.trim();
    final recipients = _recipientsController.text.trim();
    final network = _getNetworkName(selectedNetwork);
    final campaignId = _campaignController.text.trim();

    String command = './send_launch_alert.sh \\\n';
    command += '--message="$message" \\\n';
    command += '--recipients="$recipients" \\\n';
    command += '--network=$network';

    if (campaignId.isNotEmpty) {
      command += ' \\\n--campaign-id="$campaignId"';
    }

    return command;
  }

  /// Copy script command to clipboard
  void _copyScriptCommand() {
    final command = _generateScriptCommand();
    Clipboard.setData(ClipboardData(text: command));
    _showSnackBar('Script command copied to clipboard', isError: false);
  }

  /// Get network display name
  String _getNetworkDisplayName(ZambianNetwork network) {
    switch (network) {
      case ZambianNetwork.mtn:
        return 'MTN Zambia';
      case ZambianNetwork.airtel:
        return 'Airtel Zambia';
      case ZambianNetwork.zamtel:
        return 'Zamtel';
      case ZambianNetwork.auto:
        return 'Auto-detect';
    }
  }

  /// Get network name for script
  String _getNetworkName(ZambianNetwork network) {
    switch (network) {
      case ZambianNetwork.mtn:
        return 'MTN';
      case ZambianNetwork.airtel:
        return 'AIRTEL';
      case ZambianNetwork.zamtel:
        return 'ZAMTEL';
      case ZambianNetwork.auto:
        return 'AUTO';
    }
  }

  /// Show snackbar message
  void _showSnackBar(String message, {required bool isError}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red[600] : Colors.green[600],
        duration: Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('🚀 Launch Notification Demo'),
        backgroundColor: Colors.orange[800],
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Launch notification form
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Zambian Launch Notification',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 16),
                    
                    // Message input
                    TextField(
                      controller: _messageController,
                      decoration: InputDecoration(
                        labelText: 'Launch Message',
                        hintText: 'Pay Mule is live! Send money, pay bills, chilimba with friends',
                        border: OutlineInputBorder(),
                        counterText: '${_messageController.text.length}/160',
                      ),
                      maxLength: 160,
                      maxLines: 3,
                      onChanged: (value) => setState(() {}),
                    ),
                    SizedBox(height: 16),
                    
                    // Recipients input
                    TextField(
                      controller: _recipientsController,
                      decoration: InputDecoration(
                        labelText: 'Recipients (comma-separated)',
                        hintText: '+26096XXXXXXX, +26097XXXXXXX',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                    ),
                    SizedBox(height: 16),
                    
                    // Network selection
                    DropdownButtonFormField<ZambianNetwork>(
                      value: selectedNetwork,
                      decoration: InputDecoration(
                        labelText: 'Target Network',
                        border: OutlineInputBorder(),
                      ),
                      items: ZambianNetwork.values.map((network) => DropdownMenuItem(
                        value: network,
                        child: Text(_getNetworkDisplayName(network)),
                      )).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedNetwork = value;
                          });
                        }
                      },
                    ),
                    SizedBox(height: 16),
                    
                    // Campaign ID (optional)
                    TextField(
                      controller: _campaignController,
                      decoration: InputDecoration(
                        labelText: 'Campaign ID (Optional)',
                        hintText: 'LAUNCH_2024_Q1',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    SizedBox(height: 24),
                    
                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: isSending ? null : _sendLaunchNotification,
                            icon: isSending ? SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ) : Icon(Icons.send),
                            label: Text(isSending ? 'Sending...' : 'Send Notification'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange[600],
                              padding: EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _copyScriptCommand,
                            icon: Icon(Icons.copy),
                            label: Text('Copy Script'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue[600],
                              padding: EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),

            // Script command display
            Card(
              color: Colors.grey[100],
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Script Command:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.black87,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        _generateScriptCommand(),
                        style: TextStyle(
                          fontFamily: 'monospace',
                          color: Colors.green[300],
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),

            // Results and log
            Expanded(
              child: DefaultTabController(
                length: 2,
                child: Column(
                  children: [
                    TabBar(
                      labelColor: Colors.orange[800],
                      tabs: [
                        Tab(text: 'Results'),
                        Tab(text: 'Log (${notificationLog.length})'),
                      ],
                    ),
                    Expanded(
                      child: TabBarView(
                        children: [
                          _buildResultsTab(),
                          _buildLogTab(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultsTab() {
    if (lastResult == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.campaign, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No launch notification sent yet',
              style: TextStyle(color: Colors.grey[600]),
            ),
            SizedBox(height: 8),
            Text(
              'Fill in the form and tap "Send Notification"',
              style: TextStyle(color: Colors.grey[500], fontSize: 12),
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overall result
          Card(
            color: lastResult!.success ? Colors.green[50] : Colors.red[50],
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    lastResult!.success ? Icons.check_circle : Icons.error,
                    color: lastResult!.success ? Colors.green : Colors.red,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          lastResult!.success ? 'Launch Notification Sent!' : 'Launch Notification Failed',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: lastResult!.success ? Colors.green[800] : Colors.red[800],
                          ),
                        ),
                        if (lastResult!.message != null)
                          Text(
                            lastResult!.message!,
                            style: TextStyle(
                              color: lastResult!.success ? Colors.green[700] : Colors.red[700],
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // Statistics
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Recipients',
                  lastResult!.totalRecipients.toString(),
                  Icons.people,
                  Colors.blue,
                ),
              ),
              SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  'Successful',
                  lastResult!.successfulSends.toString(),
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
              SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  'Failed',
                  lastResult!.failedSends.toString(),
                  Icons.error,
                  Colors.red,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),

          // Successful numbers
          if (lastResult!.successfulNumbers.isNotEmpty) ...[
            Text(
              'Successful Recipients:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Container(
              height: 100,
              child: ListView.builder(
                itemCount: lastResult!.successfulNumbers.length,
                itemBuilder: (context, index) {
                  return ListTile(
                    leading: Icon(Icons.check, color: Colors.green),
                    title: Text(lastResult!.successfulNumbers[index]),
                    dense: true,
                  );
                },
              ),
            ),
          ],

          // Failed numbers
          if (lastResult!.failedNumbers.isNotEmpty) ...[
            SizedBox(height: 16),
            Text(
              'Failed Recipients:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Container(
              height: 100,
              child: ListView.builder(
                itemCount: lastResult!.failedNumbers.length,
                itemBuilder: (context, index) {
                  return ListTile(
                    leading: Icon(Icons.close, color: Colors.red),
                    title: Text(lastResult!.failedNumbers[index]),
                    dense: true,
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLogTab() {
    if (notificationLog.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.list_alt, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No notification log yet',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(8),
      child: ListView.builder(
        itemCount: notificationLog.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: EdgeInsets.symmetric(vertical: 2),
            child: Text(
              notificationLog[index],
              style: TextStyle(
                fontFamily: 'monospace',
                fontSize: 12,
                color: Colors.grey[700],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
