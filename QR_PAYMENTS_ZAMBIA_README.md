# 🇿🇲 QR Payments System for Zambian Market

## Overview

A comprehensive QR payment system optimized for Zambian mobile money networks (MTN, Airtel, Zamtel) with offline support and rural connectivity features.

## 🚀 Key Features

### ✅ **Core Functionality**
- **Dynamic QR Generation** - Generate QR codes with specific amounts and merchant info
- **QR Scanning & Processing** - Camera-based scanning with validation
- **Offline Support** - Works without internet, syncs when connected
- **Merchant Integration** - Complete business registration and payment processing
- **Security & Fraud Detection** - PIN verification, transaction limits, fraud prevention

### ✅ **Zambian Market Optimizations**
- **Mobile Money Integration** - MTN, Airtel, Zamtel provider detection
- **2G Network Support** - Compressed QR codes for slow networks
- **Rural Connectivity** - SMS fallback and offline-first design
- **Local Currency** - ZMW (Kwacha) support with proper formatting
- **BoZ Compliance** - Bank of Zambia regulatory compliance

### ✅ **Technical Features**
- **Offline-First Architecture** - Queue transactions for later sync
- **Security** - AES-256 encryption, PIN verification, fraud detection
- **Performance** - Optimized for low-end devices and poor connectivity
- **Scalability** - Handles high transaction volumes

## 📁 Project Structure

```
lib/qr/
├── zambia_qr.dart                 # Main QR payment system
├── zambia_qr_format.dart          # QR encoding/decoding for Zambia
├── qr_scanner_service.dart        # Camera-based QR scanning
├── merchant_qr_service.dart       # Merchant integration
├── offline_qr_service.dart        # Offline payment support
├── security/
│   └── qr_security_service.dart   # Security and fraud detection
├── widgets/
│   ├── qr_scanner_widget.dart     # QR scanner UI component
│   └── qr_generator_widget.dart   # QR generator UI component
└── examples/
    └── qr_payment_example.dart    # Complete usage examples
```

## 🛠️ Installation & Setup

### 1. Dependencies
Add to your `pubspec.yaml`:

```yaml
dependencies:
  qr_flutter: ^4.1.0
  mobile_scanner: ^3.5.2
  crypto: ^3.0.3
  logger: ^2.0.2+1
  uuid: ^4.1.0
  connectivity_plus: ^5.0.1
  permission_handler: ^11.0.1
```

### 2. Initialize Services

```dart
import 'package:pay_mule/qr/zambia_qr.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize QR payment system
  await PayMuleQR().initialize();
  
  runApp(MyApp());
}
```

## 📖 Usage Examples

### 1. Generate QR Code for Payment

```dart
// Generate dynamic QR for specific amount
final qrImage = await PayMuleQR.generateQR(
  merchantId: 'merchant_12345',
  amount: 50.0,
  description: 'Fresh vegetables',
  currency: 'ZMW',
  expiryDuration: Duration(hours: 24),
);

// Display QR code
Image.memory(qrImage)
```

### 2. Scan and Process QR Payment

```dart
// Scan QR code
final result = await PayMuleQR.scanAndPay(
  qrData: scannedQRData,
  payerUserId: 'customer_67890',
  customPin: '123456',
);

if (result.success) {
  print('Payment successful: ${result.transactionId}');
} else {
  print('Payment failed: ${result.message}');
}
```

### 3. Register Merchant

```dart
final merchantService = MerchantQRService();

final result = await merchantService.registerMerchant(
  userId: 'user_12345',
  businessName: 'Lusaka Market Stall',
  phoneNumber: '+260961234567',
  category: 'RETAIL',
  location: 'Lusaka Central Market',
);
```

### 4. Offline Payment Processing

```dart
final offlineService = OfflineQRService();

// Process payment offline
final result = await offlineService.processOfflinePayment(
  qrData: qrData,
  payerUserId: userId,
  pin: pin,
);

// Sync when online
await offlineService.syncOfflineTransactions();
```

## 🔒 Security Features

### PIN Verification
- Secure PIN hashing with PBKDF2
- Failed attempt tracking and lockout
- Biometric backup support

### Transaction Limits
- Daily and monthly limits
- Amount validation
- BoZ compliance checks

### Fraud Detection
- Pattern analysis
- Device fingerprinting
- Risk scoring
- Real-time blocking

### Data Protection
- AES-256 encryption
- Secure storage
- Data integrity checks

## 🌐 Offline Support

### Features
- **Offline QR Generation** - Create QR codes without internet
- **Offline Payment Processing** - Queue payments for later sync
- **SMS Fallback** - Alternative confirmation via SMS
- **Local Storage** - Encrypted local transaction storage

### Sync Process
1. Detect connectivity restoration
2. Validate queued transactions
3. Process payments through mobile money APIs
4. Update transaction status
5. Notify users of completion

## 📱 UI Components

### QR Scanner Widget
```dart
QRScannerWidget(
  onScan: (result) {
    // Handle scan result
  },
  onError: (error) {
    // Handle errors
  },
  showFlashToggle: true,
  showCameraSwitch: true,
)
```

### QR Generator Widget
```dart
QRGeneratorWidget(
  merchantId: 'merchant_12345',
  allowCustomAmount: true,
  enableOfflineMode: true,
  onQRGenerated: (qrImage, qrData) {
    // Handle generated QR
  },
)
```

## 🔧 Configuration

### Mobile Money Providers
```dart
// Configure in AppConfig
static const Map<String, String> mtnCredentials = {
  'apiKey': 'your_mtn_api_key',
  'subscriptionKey': 'your_subscription_key',
};

static const Map<String, String> airtelCredentials = {
  'clientId': 'your_airtel_client_id',
  'clientSecret': 'your_airtel_client_secret',
};
```

### Transaction Limits
```dart
// Configure in AppConstants
static const double minTransactionAmount = 1.0;
static const double maxTransactionAmount = 50000.0;
static const double maxDailyTransactionAmount = 50000.0;
static const double maxMonthlyTransactionAmount = 500000.0;
```

## 🧪 Testing

### Run Examples
```dart
import 'package:pay_mule/qr/examples/qr_payment_example.dart';

void main() async {
  await QRPaymentExample.runAllExamples();
}
```

### Test Scenarios
- ✅ Online QR generation and payment
- ✅ Offline payment processing
- ✅ Merchant registration
- ✅ Security validation
- ✅ Fraud detection
- ✅ Network connectivity changes

## 📊 Performance Optimizations

### 2G Network Support
- Compressed QR data (max 200 bytes)
- Minimal API calls
- Efficient data structures

### Low-End Device Support
- Optimized camera handling
- Memory-efficient QR processing
- Background sync management

### Rural Connectivity
- SMS fallback mechanisms
- Extended offline periods
- Intelligent sync scheduling

## 🚨 Error Handling

### Common Error Codes
- `QR_EXPIRED` - QR code has expired
- `INVALID_PIN` - Incorrect transaction PIN
- `AMOUNT_TOO_HIGH` - Exceeds transaction limits
- `MERCHANT_NOT_FOUND` - Invalid merchant ID
- `OFFLINE_QUEUE_FAILED` - Offline storage error

### Error Recovery
- Automatic retry mechanisms
- Graceful degradation
- User-friendly error messages

## 🔄 Migration & Updates

### Database Migrations
- Automatic schema updates
- Data preservation
- Backward compatibility

### API Versioning
- QR format versioning
- Graceful API transitions
- Legacy support

## 📈 Monitoring & Analytics

### Transaction Tracking
- Success/failure rates
- Processing times
- Offline sync performance

### Security Monitoring
- Fraud detection alerts
- PIN attempt tracking
- Suspicious activity logging

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Ensure BoZ compliance
5. Submit pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Email: <EMAIL>
- Phone: +260-XXX-XXXX
- Documentation: [docs.paymule.zm](https://docs.paymule.zm)

---

**Built for Zambia 🇿🇲 | Optimized for Rural Connectivity | BoZ Compliant**
