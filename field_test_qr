#!/usr/bin/env python3

"""
Field Testing Tool for Zambian QR Payment System
Tests real-world scenarios at actual market locations
Simulates merchant registration, QR generation, and customer transactions
"""

import argparse
import json
import time
import random
import sqlite3
import uuid
from datetime import datetime, timedelta
from pathlib import Path
import subprocess
import sys
import threading
from concurrent.futures import ThreadPoolExecutor

# Colors for terminal output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'  # No Color

def print_status(message):
    print(f"{Colors.BLUE}[INFO]{Colors.NC} {message}")

def print_success(message):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {message}")

def print_warning(message):
    print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {message}")

def print_error(message):
    print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")

def print_header(message):
    print(f"\n{Colors.PURPLE}{'='*70}{Colors.NC}")
    print(f"{Colors.PURPLE}{message.center(70)}{Colors.NC}")
    print(f"{Colors.PURPLE}{'='*70}{Colors.NC}\n")

class ZambianMarketData:
    """Real Zambian market data for field testing"""
    
    LOCATIONS = {
        "Soweto Market": {
            "coordinates": (-15.3875, 28.3228),
            "description": "Large informal market in Lusaka",
            "typical_businesses": ["vegetables", "fruits", "clothing", "electronics", "food"],
            "customer_demographics": "mixed_income",
            "network_quality": "fair",  # 2G/3G coverage
            "peak_hours": [(8, 12), (14, 18)],
        },
        "Kamwala Trading": {
            "coordinates": (-15.4067, 28.2833),
            "description": "Commercial trading area in Lusaka",
            "typical_businesses": ["wholesale", "retail", "services", "automotive", "hardware"],
            "customer_demographics": "business_focused",
            "network_quality": "good",  # 3G/4G coverage
            "peak_hours": [(9, 17)],
        },
        "City Market": {
            "coordinates": (-15.4167, 28.2833),
            "description": "Central market in Lusaka CBD",
            "typical_businesses": ["food", "crafts", "textiles", "electronics"],
            "customer_demographics": "tourist_local_mix",
            "network_quality": "excellent",  # 4G coverage
            "peak_hours": [(10, 16)],
        },
        "Chawama Market": {
            "coordinates": (-15.4500, 28.2500),
            "description": "Community market in Chawama compound",
            "typical_businesses": ["vegetables", "fish", "household", "services"],
            "customer_demographics": "low_income",
            "network_quality": "poor",  # 2G coverage
            "peak_hours": [(6, 10), (16, 19)],
        }
    }
    
    BUSINESS_TYPES = {
        "vegetables": {
            "typical_amounts": [5, 10, 15, 20, 25, 30, 50],
            "transaction_frequency": "high",
            "customer_type": "local",
            "payment_preference": "cash_mobile",
        },
        "fruits": {
            "typical_amounts": [10, 15, 20, 25, 30, 40, 60],
            "transaction_frequency": "high",
            "customer_type": "local",
            "payment_preference": "cash_mobile",
        },
        "clothing": {
            "typical_amounts": [50, 75, 100, 150, 200, 300, 500],
            "transaction_frequency": "medium",
            "customer_type": "mixed",
            "payment_preference": "mobile_preferred",
        },
        "electronics": {
            "typical_amounts": [100, 200, 500, 1000, 2000, 3000, 5000],
            "transaction_frequency": "low",
            "customer_type": "urban",
            "payment_preference": "mobile_only",
        },
        "food": {
            "typical_amounts": [15, 25, 35, 50, 75, 100],
            "transaction_frequency": "very_high",
            "customer_type": "local",
            "payment_preference": "cash_mobile",
        },
        "services": {
            "typical_amounts": [25, 50, 100, 200, 300, 500],
            "transaction_frequency": "medium",
            "customer_type": "business",
            "payment_preference": "mobile_preferred",
        }
    }
    
    ZAMBIAN_NAMES = {
        "first_names": [
            "Mwamba", "Chanda", "Mulenga", "Banda", "Phiri", "Tembo", "Zulu",
            "Mwanza", "Sakala", "Lungu", "Mwale", "Sichone", "Katongo", "Mumba",
            "Chilufya", "Musonda", "Kabwe", "Mbewe", "Nkole", "Simukonda"
        ],
        "surnames": [
            "Mwamba", "Chanda", "Mulenga", "Banda", "Phiri", "Tembo", "Zulu",
            "Mwanza", "Sakala", "Lungu", "Mwale", "Sichone", "Katongo", "Mumba",
            "Chilufya", "Musonda", "Kabwe", "Mbewe", "Nkole", "Simukonda"
        ]
    }

class FieldTestQR:
    def __init__(self, output_dir="field_test_results"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.db_path = self.output_dir / "field_test.db"
        self.results_file = self.output_dir / "field_test_results.json"
        
        # Mobile money providers in Zambia
        self.providers = ["MTN", "AIRTEL", "ZAMTEL"]
        self.provider_weights = [0.6, 0.3, 0.1]  # Market share approximation
        
        self.init_database()
        
    def init_database(self):
        """Initialize SQLite database for field testing"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Merchants table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS field_merchants (
                id TEXT PRIMARY KEY,
                business_name TEXT NOT NULL,
                owner_name TEXT NOT NULL,
                business_type TEXT NOT NULL,
                location TEXT NOT NULL,
                phone_number TEXT NOT NULL,
                pacra_id TEXT,
                registration_time INTEGER NOT NULL,
                verification_status TEXT DEFAULT 'PENDING',
                qr_generated INTEGER DEFAULT 0
            )
        ''')
        
        # Transactions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS field_transactions (
                id TEXT PRIMARY KEY,
                merchant_id TEXT NOT NULL,
                customer_name TEXT,
                amount REAL NOT NULL,
                currency TEXT DEFAULT 'ZMW',
                provider TEXT NOT NULL,
                transaction_time INTEGER NOT NULL,
                status TEXT DEFAULT 'COMPLETED',
                location TEXT NOT NULL,
                network_quality TEXT,
                device_type TEXT,
                FOREIGN KEY (merchant_id) REFERENCES field_merchants (id)
            )
        ''')
        
        # QR codes table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS field_qr_codes (
                id TEXT PRIMARY KEY,
                merchant_id TEXT NOT NULL,
                qr_data TEXT NOT NULL,
                amount REAL,
                generated_time INTEGER NOT NULL,
                scans_count INTEGER DEFAULT 0,
                last_scanned INTEGER,
                FOREIGN KEY (merchant_id) REFERENCES field_merchants (id)
            )
        ''')
        
        # Test events table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS field_test_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                event_type TEXT NOT NULL,
                timestamp INTEGER NOT NULL,
                location TEXT NOT NULL,
                details TEXT,
                success INTEGER DEFAULT 1
            )
        ''')
        
        conn.commit()
        conn.close()
        
        print_success("Field test database initialized")
    
    def generate_zambian_name(self):
        """Generate realistic Zambian name"""
        first_name = random.choice(ZambianMarketData.ZAMBIAN_NAMES["first_names"])
        surname = random.choice(ZambianMarketData.ZAMBIAN_NAMES["surnames"])
        return f"{first_name} {surname}"
    
    def generate_zambian_phone(self):
        """Generate realistic Zambian phone number"""
        # Zambian mobile number patterns
        prefixes = {
            "MTN": ["096", "076"],
            "AIRTEL": ["097", "077"],
            "ZAMTEL": ["095", "075"]
        }
        
        provider = random.choices(self.providers, weights=self.provider_weights)[0]
        prefix = random.choice(prefixes[provider])
        number = f"+260{prefix}{random.randint(1000000, 9999999)}"
        
        return number, provider
    
    def generate_pacra_id(self):
        """Generate realistic PACRA (business registration) ID"""
        year = random.randint(2020, 2024)
        sequence = random.randint(100000, 999999)
        return f"PACRA/{year}/{sequence}"
    
    def register_merchant(self, location, business_type):
        """Register a new merchant for field testing"""
        merchant_id = f"zm_field_{uuid.uuid4().hex[:8]}"
        owner_name = self.generate_zambian_name()
        phone_number, provider = self.generate_zambian_phone()
        pacra_id = self.generate_pacra_id()
        
        # Generate business name based on type and location
        business_names = {
            "vegetables": [f"{owner_name.split()[0]}'s Fresh Vegetables", f"{location} Vegetable Stand"],
            "fruits": [f"{owner_name.split()[0]}'s Fruits", f"Fresh Fruits - {location}"],
            "clothing": [f"{owner_name.split()[0]}'s Fashion", f"{location} Clothing Store"],
            "electronics": [f"{owner_name.split()[0]}'s Electronics", f"{location} Phone Shop"],
            "food": [f"{owner_name.split()[0]}'s Kitchen", f"{location} Food Corner"],
            "services": [f"{owner_name.split()[0]}'s Services", f"{location} Business Services"],
        }
        
        business_name = random.choice(business_names.get(business_type, [f"{owner_name}'s Business"]))
        
        merchant = {
            'id': merchant_id,
            'business_name': business_name,
            'owner_name': owner_name,
            'business_type': business_type,
            'location': location,
            'phone_number': phone_number,
            'pacra_id': pacra_id,
            'registration_time': int(time.time()),
            'verification_status': 'PENDING'
        }
        
        # Store in database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO field_merchants 
            (id, business_name, owner_name, business_type, location, phone_number, pacra_id, registration_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            merchant['id'], merchant['business_name'], merchant['owner_name'],
            merchant['business_type'], merchant['location'], merchant['phone_number'],
            merchant['pacra_id'], merchant['registration_time']
        ))
        
        conn.commit()
        conn.close()
        
        # Log registration event
        self.log_event('MERCHANT_REGISTRATION', location, {
            'merchant_id': merchant_id,
            'business_name': business_name,
            'business_type': business_type,
            'owner_name': owner_name
        })
        
        return merchant
    
    def verify_merchant(self, merchant_id):
        """Simulate merchant verification process"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Simulate verification delay
        time.sleep(random.uniform(0.5, 2.0))
        
        # 95% verification success rate
        verification_status = 'VERIFIED' if random.random() < 0.95 else 'REJECTED'
        
        cursor.execute('''
            UPDATE field_merchants 
            SET verification_status = ? 
            WHERE id = ?
        ''', (verification_status, merchant_id))
        
        conn.commit()
        conn.close()
        
        return verification_status == 'VERIFIED'
    
    def generate_qr_code(self, merchant_id, amount=None):
        """Generate QR code for merchant"""
        qr_id = f"qr_{uuid.uuid4().hex[:12]}"
        
        # Simulate QR generation
        qr_data = {
            'version': '2.0',
            'type': 'PAYMENT',
            'merchant_id': merchant_id,
            'amount': amount,
            'currency': 'ZMW',
            'timestamp': int(time.time()),
            'expiry': int(time.time()) + 3600,  # 1 hour expiry
            'qr_id': qr_id
        }
        
        qr_string = f"ZPAY:{json.dumps(qr_data)}"
        
        # Store QR code
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO field_qr_codes 
            (id, merchant_id, qr_data, amount, generated_time)
            VALUES (?, ?, ?, ?, ?)
        ''', (qr_id, merchant_id, qr_string, amount, int(time.time())))
        
        # Mark merchant as having QR generated
        cursor.execute('''
            UPDATE field_merchants 
            SET qr_generated = 1 
            WHERE id = ?
        ''', (merchant_id,))
        
        conn.commit()
        conn.close()
        
        return qr_id, qr_string
    
    def simulate_transaction(self, merchant_id, location, min_amount, max_amount):
        """Simulate a customer transaction"""
        # Get merchant info
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT business_type FROM field_merchants WHERE id = ?', (merchant_id,))
        result = cursor.fetchone()
        
        if not result:
            conn.close()
            return None
        
        business_type = result[0]
        conn.close()
        
        # Generate realistic transaction amount based on business type
        business_data = ZambianMarketData.BUSINESS_TYPES.get(business_type, {})
        typical_amounts = business_data.get('typical_amounts', [min_amount, max_amount])
        
        # Choose amount from typical amounts or random within range
        if random.random() < 0.7:  # 70% chance of typical amount
            amount = random.choice([a for a in typical_amounts if min_amount <= a <= max_amount])
        else:
            amount = random.uniform(min_amount, max_amount)
        
        amount = round(amount, 2)
        
        # Generate customer details
        customer_name = self.generate_zambian_name()
        provider = random.choices(self.providers, weights=self.provider_weights)[0]
        
        # Simulate network quality based on location
        location_data = ZambianMarketData.LOCATIONS.get(location, {})
        network_quality = location_data.get('network_quality', 'fair')
        
        # Simulate device types common in Zambia
        device_types = ['Tecno Spark 7', 'Itel P40', 'Samsung Galaxy A12', 'Infinix Hot 10', 'Huawei Y7a']
        device_weights = [0.3, 0.25, 0.2, 0.15, 0.1]  # Budget devices more common
        device_type = random.choices(device_types, weights=device_weights)[0]
        
        # Simulate transaction success based on network quality
        success_rates = {
            'excellent': 0.98,
            'good': 0.95,
            'fair': 0.90,
            'poor': 0.80
        }
        
        success_rate = success_rates.get(network_quality, 0.90)
        transaction_status = 'COMPLETED' if random.random() < success_rate else 'FAILED'
        
        # Create transaction record
        transaction_id = f"txn_{uuid.uuid4().hex[:12]}"
        
        transaction = {
            'id': transaction_id,
            'merchant_id': merchant_id,
            'customer_name': customer_name,
            'amount': amount,
            'provider': provider,
            'transaction_time': int(time.time()),
            'status': transaction_status,
            'location': location,
            'network_quality': network_quality,
            'device_type': device_type
        }
        
        # Store transaction
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO field_transactions 
            (id, merchant_id, customer_name, amount, currency, provider, 
             transaction_time, status, location, network_quality, device_type)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            transaction['id'], transaction['merchant_id'], transaction['customer_name'],
            transaction['amount'], 'ZMW', transaction['provider'],
            transaction['transaction_time'], transaction['status'],
            transaction['location'], transaction['network_quality'], transaction['device_type']
        ))
        
        conn.commit()
        conn.close()
        
        # Log transaction event
        self.log_event('TRANSACTION', location, {
            'transaction_id': transaction_id,
            'merchant_id': merchant_id,
            'amount': amount,
            'status': transaction_status,
            'provider': provider,
            'device_type': device_type
        }, success=(transaction_status == 'COMPLETED'))
        
        return transaction
    
    def log_event(self, event_type, location, details, success=True):
        """Log field test event"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO field_test_events 
            (event_type, timestamp, location, details, success)
            VALUES (?, ?, ?, ?, ?)
        ''', (event_type, int(time.time()), location, json.dumps(details), int(success)))
        
        conn.commit()
        conn.close()
    
    def run_field_test(self, locations, num_merchants, transaction_range):
        """Run comprehensive field test"""
        print_header("🇿🇲 ZAMBIAN QR PAYMENT FIELD TEST")
        
        min_amount, max_amount = transaction_range
        
        print_status(f"Field Test Configuration:")
        print(f"  Locations: {', '.join(locations)}")
        print(f"  Merchants per location: {num_merchants}")
        print(f"  Transaction range: K{min_amount} - K{max_amount}")
        
        all_merchants = []
        total_transactions = 0
        
        # Process each location
        for location in locations:
            print_header(f"TESTING AT {location.upper()}")
            
            if location not in ZambianMarketData.LOCATIONS:
                print_warning(f"Unknown location: {location}. Using default settings.")
                location_data = {
                    "typical_businesses": ["retail", "services"],
                    "network_quality": "fair"
                }
            else:
                location_data = ZambianMarketData.LOCATIONS[location]
            
            print_status(f"Location: {location}")
            print(f"  Description: {location_data.get('description', 'Market location')}")
            print(f"  Network Quality: {location_data.get('network_quality', 'fair')}")
            
            # Register merchants for this location
            print_status(f"Registering {num_merchants} merchants...")
            
            location_merchants = []
            typical_businesses = location_data.get('typical_businesses', ['retail'])
            
            for i in range(num_merchants):
                business_type = random.choice(typical_businesses)
                merchant = self.register_merchant(location, business_type)
                
                # Verify merchant
                if self.verify_merchant(merchant['id']):
                    # Generate QR codes
                    static_qr_id, static_qr = self.generate_qr_code(merchant['id'])  # Static QR
                    
                    # Generate some dynamic QRs with specific amounts
                    business_data = ZambianMarketData.BUSINESS_TYPES.get(business_type, {})
                    typical_amounts = business_data.get('typical_amounts', [min_amount, max_amount])
                    
                    for amount in random.sample(typical_amounts, min(3, len(typical_amounts))):
                        if min_amount <= amount <= max_amount:
                            dynamic_qr_id, dynamic_qr = self.generate_qr_code(merchant['id'], amount)
                    
                    location_merchants.append(merchant)
                    
                    if (i + 1) % 5 == 0:
                        print_status(f"  Registered {i + 1}/{num_merchants} merchants")
                
                # Small delay to simulate real registration time
                time.sleep(0.1)
            
            print_success(f"Registered {len(location_merchants)} verified merchants at {location}")
            all_merchants.extend(location_merchants)
            
            # Simulate transactions
            print_status("Simulating customer transactions...")
            
            # Generate realistic number of transactions per merchant
            for merchant in location_merchants:
                business_type = merchant['business_type']
                business_data = ZambianMarketData.BUSINESS_TYPES.get(business_type, {})
                frequency = business_data.get('transaction_frequency', 'medium')
                
                # Number of transactions based on frequency
                transaction_counts = {
                    'very_high': random.randint(15, 25),
                    'high': random.randint(10, 20),
                    'medium': random.randint(5, 15),
                    'low': random.randint(2, 8)
                }
                
                num_transactions = transaction_counts.get(frequency, 10)
                
                for _ in range(num_transactions):
                    transaction = self.simulate_transaction(
                        merchant['id'], location, min_amount, max_amount
                    )
                    if transaction:
                        total_transactions += 1
                    
                    # Small delay between transactions
                    time.sleep(0.05)
        
        print_header("FIELD TEST SUMMARY")
        
        # Generate comprehensive statistics
        stats = self.generate_field_statistics()
        
        print_success(f"Field test completed!")
        print(f"  📍 Locations tested: {len(locations)}")
        print(f"  🏪 Merchants registered: {len(all_merchants)}")
        print(f"  💳 Transactions processed: {total_transactions}")
        print(f"  📊 Success rate: {stats['transaction_success_rate']:.1f}%")
        
        # Generate detailed report
        report = self.generate_field_report(locations, num_merchants, transaction_range, stats)
        
        return report
    
    def generate_field_statistics(self):
        """Generate comprehensive field test statistics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Overall statistics
        cursor.execute('SELECT COUNT(*) FROM field_merchants WHERE verification_status = "VERIFIED"')
        verified_merchants = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM field_transactions')
        total_transactions = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM field_transactions WHERE status = "COMPLETED"')
        successful_transactions = cursor.fetchone()[0]
        
        # Transaction statistics by location
        cursor.execute('''
            SELECT location, COUNT(*), 
                   SUM(CASE WHEN status = "COMPLETED" THEN 1 ELSE 0 END),
                   AVG(amount)
            FROM field_transactions 
            GROUP BY location
        ''')
        location_stats = {}
        for row in cursor.fetchall():
            location, total, successful, avg_amount = row
            location_stats[location] = {
                'total_transactions': total,
                'successful_transactions': successful,
                'success_rate': (successful / total * 100) if total > 0 else 0,
                'average_amount': avg_amount or 0
            }
        
        # Provider distribution
        cursor.execute('SELECT provider, COUNT(*) FROM field_transactions GROUP BY provider')
        provider_stats = dict(cursor.fetchall())
        
        # Business type performance
        cursor.execute('''
            SELECT m.business_type, COUNT(t.id),
                   SUM(CASE WHEN t.status = "COMPLETED" THEN 1 ELSE 0 END),
                   AVG(t.amount)
            FROM field_merchants m
            JOIN field_transactions t ON m.id = t.merchant_id
            GROUP BY m.business_type
        ''')
        business_stats = {}
        for row in cursor.fetchall():
            business_type, total, successful, avg_amount = row
            business_stats[business_type] = {
                'total_transactions': total,
                'successful_transactions': successful,
                'success_rate': (successful / total * 100) if total > 0 else 0,
                'average_amount': avg_amount or 0
            }
        
        # Device performance
        cursor.execute('''
            SELECT device_type, COUNT(*),
                   SUM(CASE WHEN status = "COMPLETED" THEN 1 ELSE 0 END)
            FROM field_transactions 
            GROUP BY device_type
        ''')
        device_stats = {}
        for row in cursor.fetchall():
            device, total, successful = row
            device_stats[device] = {
                'total_transactions': total,
                'successful_transactions': successful,
                'success_rate': (successful / total * 100) if total > 0 else 0
            }
        
        # Network quality impact
        cursor.execute('''
            SELECT network_quality, COUNT(*),
                   SUM(CASE WHEN status = "COMPLETED" THEN 1 ELSE 0 END)
            FROM field_transactions 
            GROUP BY network_quality
        ''')
        network_stats = {}
        for row in cursor.fetchall():
            quality, total, successful = row
            network_stats[quality] = {
                'total_transactions': total,
                'successful_transactions': successful,
                'success_rate': (successful / total * 100) if total > 0 else 0
            }
        
        conn.close()
        
        return {
            'verified_merchants': verified_merchants,
            'total_transactions': total_transactions,
            'successful_transactions': successful_transactions,
            'transaction_success_rate': (successful_transactions / total_transactions * 100) if total_transactions > 0 else 0,
            'location_stats': location_stats,
            'provider_stats': provider_stats,
            'business_stats': business_stats,
            'device_stats': device_stats,
            'network_stats': network_stats
        }
    
    def generate_field_report(self, locations, num_merchants, transaction_range, stats):
        """Generate comprehensive field test report"""
        report = {
            'test_metadata': {
                'timestamp': datetime.now().isoformat(),
                'locations': locations,
                'merchants_per_location': num_merchants,
                'transaction_range': transaction_range,
                'database_path': str(self.db_path)
            },
            'statistics': stats,
            'summary': {
                'total_locations': len(locations),
                'total_merchants': stats['verified_merchants'],
                'total_transactions': stats['total_transactions'],
                'overall_success_rate': stats['transaction_success_rate']
            }
        }
        
        # Save JSON report
        with open(self.results_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Generate markdown report
        markdown_report = self.generate_markdown_report(report)
        with open(self.output_dir / "field_test_report.md", 'w') as f:
            f.write(markdown_report)
        
        print_success(f"Field test reports saved to {self.output_dir}")
        
        return report
    
    def generate_markdown_report(self, report):
        """Generate markdown field test report"""
        stats = report['statistics']
        summary = report['summary']
        
        markdown = f"""# Zambian QR Payment Field Test Report

## Test Overview
- **Test Date**: {report['test_metadata']['timestamp']}
- **Locations**: {', '.join(report['test_metadata']['locations'])}
- **Merchants per Location**: {report['test_metadata']['merchants_per_location']}
- **Transaction Range**: K{report['test_metadata']['transaction_range'][0]} - K{report['test_metadata']['transaction_range'][1]}

## Executive Summary
- **Total Locations**: {summary['total_locations']}
- **Verified Merchants**: {summary['total_merchants']}
- **Total Transactions**: {summary['total_transactions']}
- **Overall Success Rate**: {summary['overall_success_rate']:.1f}%

## Location Performance
"""
        
        for location, data in stats['location_stats'].items():
            markdown += f"""
### {location}
- **Transactions**: {data['total_transactions']}
- **Success Rate**: {data['success_rate']:.1f}%
- **Average Amount**: K{data['average_amount']:.2f}
"""
        
        markdown += f"""
## Business Type Analysis
"""
        
        for business_type, data in stats['business_stats'].items():
            markdown += f"""
### {business_type.title()}
- **Transactions**: {data['total_transactions']}
- **Success Rate**: {data['success_rate']:.1f}%
- **Average Amount**: K{data['average_amount']:.2f}
"""
        
        markdown += f"""
## Device Performance
"""
        
        for device, data in stats['device_stats'].items():
            markdown += f"- **{device}**: {data['success_rate']:.1f}% ({data['successful_transactions']}/{data['total_transactions']})\n"
        
        markdown += f"""
## Network Quality Impact
"""
        
        for quality, data in stats['network_stats'].items():
            markdown += f"- **{quality.title()}**: {data['success_rate']:.1f}% ({data['successful_transactions']}/{data['total_transactions']})\n"
        
        markdown += f"""
## Provider Distribution
"""
        
        total_provider_transactions = sum(stats['provider_stats'].values())
        for provider, count in stats['provider_stats'].items():
            percentage = (count / total_provider_transactions * 100) if total_provider_transactions > 0 else 0
            markdown += f"- **{provider}**: {count} transactions ({percentage:.1f}%)\n"
        
        markdown += f"""
## Recommendations
"""
        
        if summary['overall_success_rate'] < 90:
            markdown += "- Overall success rate below 90%. Investigate network and device issues.\n"
        
        # Check for poor performing locations
        poor_locations = [loc for loc, data in stats['location_stats'].items() if data['success_rate'] < 85]
        if poor_locations:
            markdown += f"- Poor performance at: {', '.join(poor_locations)}. Consider network improvements.\n"
        
        # Check device performance
        poor_devices = [device for device, data in stats['device_stats'].items() if data['success_rate'] < 80]
        if poor_devices:
            markdown += f"- Low success rates on: {', '.join(poor_devices)}. Optimize for budget devices.\n"
        
        if summary['overall_success_rate'] >= 95:
            markdown += "- Excellent performance across all metrics. System ready for deployment.\n"
        
        return markdown

def parse_transaction_range(range_str):
    """Parse transaction range string (e.g., '5-500')"""
    try:
        min_amount, max_amount = map(float, range_str.split('-'))
        return min_amount, max_amount
    except ValueError:
        raise ValueError(f"Invalid transaction range format: {range_str}")

def main():
    parser = argparse.ArgumentParser(
        description='🇿🇲 Field Testing Tool for Zambian QR Payment System',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --locations="Soweto Market,Kamwala Trading" --merchants=20 --transaction-range="5-500"
  %(prog)s --locations="City Market" --merchants=10 --transaction-range="10-1000"
  %(prog)s --locations="Chawama Market,Soweto Market" --merchants=15 --transaction-range="5-200"
        """
    )
    
    parser.add_argument('--locations', type=str, required=True,
                       help='Comma-separated list of test locations')
    
    parser.add_argument('--merchants', type=int, default=20,
                       help='Number of merchants to register per location (default: 20)')
    
    parser.add_argument('--transaction-range', type=str, default='5-500',
                       help='Transaction amount range (e.g., "5-500") (default: 5-500)')
    
    parser.add_argument('--output-dir', type=str, default='field_test_results',
                       help='Output directory for results (default: field_test_results)')
    
    args = parser.parse_args()
    
    # Parse locations
    locations = [loc.strip() for loc in args.locations.split(',')]
    
    # Parse transaction range
    try:
        transaction_range = parse_transaction_range(args.transaction_range)
    except ValueError as e:
        print_error(str(e))
        sys.exit(1)
    
    # Run field test
    field_tester = FieldTestQR(args.output_dir)
    report = field_tester.run_field_test(
        locations=locations,
        num_merchants=args.merchants,
        transaction_range=transaction_range
    )
    
    print_status(f"📁 Field test results saved to: {args.output_dir}")

if __name__ == '__main__':
    main()
