/// QR Generation Tests for Zambian Mobile Money
/// Tests QR code generation, validation, and format compliance
/// Optimized for Zambian market conditions and BoZ requirements

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import '../../lib/qr/zambia_qr.dart';
import '../../lib/qr/zambia_qr_format.dart';
import '../../lib/qr/merchant_qr_service.dart';
import '../../lib/qr/security/merchant_registry.dart';
import '../../lib/core/constants/app_constants.dart';

@GenerateMocks([MerchantQRService, MerchantRegistry])
import 'generation_test.mocks.dart';

void main() {
  group('QR Generation Tests - Zambian Production Environment', () {
    late PayMuleQR qrSystem;
    late MockMerchantQRService mockMerchantService;
    late MockMerchantRegistry mockMerchantRegistry;

    const testEnvironment = String.fromEnvironment('ENV', defaultValue: 'test');
    const isZambianProduction = testEnvironment == 'zm_prod';

    setUpAll(() async {
      // Initialize test environment
      print('🇿🇲 Running QR Generation Tests');
      print('Environment: $testEnvironment');
      print('Zambian Production Mode: $isZambianProduction');
    });

    setUp(() async {
      qrSystem = PayMuleQR();
      mockMerchantService = MockMerchantQRService();
      mockMerchantRegistry = MockMerchantRegistry();
      
      // Initialize QR system for testing
      await qrSystem.initialize();
    });

    group('Basic QR Generation', () {
      test('should generate valid QR for Zambian merchant', () async {
        // Arrange
        const merchantId = 'zm_merchant_001';
        const amount = 25.0;
        const currency = 'ZMW';
        const description = 'Fresh vegetables - Lusaka Market';

        // Act
        final qrData = await PayMuleQR.generateQR(
          merchantId: merchantId,
          amount: amount,
          currency: currency,
          description: description,
          expiryDuration: const Duration(minutes: 10),
        );

        // Assert
        expect(qrData, isNotNull);
        expect(qrData.length, greaterThan(0));
        
        // Verify QR format compliance
        final decodedData = await ZambiaQRFormat.decode(
          String.fromCharCodes(qrData),
        );
        
        expect(decodedData.merchantId, equals(merchantId));
        expect(decodedData.amount, equals(amount));
        expect(decodedData.currency, equals(currency));
        expect(decodedData.description, equals(description));
      });

      test('should generate QR with Zambian provider detection', () async {
        // Test MTN number
        final mtnQR = await PayMuleQR.generateQR(
          merchantId: 'zm_mtn_merchant',
          amount: 50.0,
          currency: 'ZMW',
          phoneNumber: '+260961234567', // MTN number
        );

        final mtnDecoded = await ZambiaQRFormat.decode(
          String.fromCharCodes(mtnQR),
        );
        expect(mtnDecoded.provider, equals('MTN'));

        // Test Airtel number
        final airtelQR = await PayMuleQR.generateQR(
          merchantId: 'zm_airtel_merchant',
          amount: 75.0,
          currency: 'ZMW',
          phoneNumber: '+260971234567', // Airtel number
        );

        final airtelDecoded = await ZambiaQRFormat.decode(
          String.fromCharCodes(airtelQR),
        );
        expect(airtelDecoded.provider, equals('AIRTEL'));
      });

      test('should generate 2G-optimized QR codes', () async {
        // Arrange
        const merchantId = 'zm_rural_merchant';
        const amount = 15.0;

        // Act
        final qrData = await PayMuleQR.generateQR(
          merchantId: merchantId,
          amount: amount,
          currency: 'ZMW',
          description: 'Rural market payment',
        );

        final qrString = String.fromCharCodes(qrData);

        // Assert
        expect(ZambiaQRFormat.is2GOptimized(qrString), isTrue);
        expect(ZambiaQRFormat.getDataSize(qrString), lessThanOrEqualTo(200));
      });
    });

    group('Amount Validation', () {
      test('should validate Zambian amount limits', () {
        // Test valid amounts
        expect(ZambiaQRFormat.validateAmount(5.0), isTrue);
        expect(ZambiaQRFormat.validateAmount(100.0), isTrue);
        expect(ZambiaQRFormat.validateAmount(5000.0), isTrue);
        expect(ZambiaQRFormat.validateAmount(0.0), isTrue); // Open amount

        // Test invalid amounts
        expect(ZambiaQRFormat.validateAmount(-10.0), isFalse);
        expect(ZambiaQRFormat.validateAmount(3.0), isFalse);
        expect(ZambiaQRFormat.validateAmount(6000.0), isFalse);
      });

      test('should validate provider-specific limits', () {
        // MTN limits (K3,000)
        expect(ZambiaQRFormat.validateAmountForProvider(2500.0, 'MTN'), isTrue);
        expect(ZambiaQRFormat.validateAmountForProvider(3500.0, 'MTN'), isFalse);

        // Airtel limits (K2,500)
        expect(ZambiaQRFormat.validateAmountForProvider(2000.0, 'AIRTEL'), isTrue);
        expect(ZambiaQRFormat.validateAmountForProvider(3000.0, 'AIRTEL'), isFalse);

        // Zamtel limits (K2,000)
        expect(ZambiaQRFormat.validateAmountForProvider(1500.0, 'ZAMTEL'), isTrue);
        expect(ZambiaQRFormat.validateAmountForProvider(2500.0, 'ZAMTEL'), isFalse);
      });

      test('should provide detailed amount validation', () {
        // Test too low amount
        final lowValidation = ZambiaQRFormat.validateAmountDetailed(3.0);
        expect(lowValidation.isValid, isFalse);
        expect(lowValidation.error, equals('AMOUNT_TOO_LOW'));
        expect(lowValidation.suggestedAmount, equals(5.0));

        // Test too high amount
        final highValidation = ZambiaQRFormat.validateAmountDetailed(6000.0);
        expect(highValidation.isValid, isFalse);
        expect(highValidation.error, equals('AMOUNT_TOO_HIGH'));
        expect(highValidation.suggestedAmount, equals(5000.0));

        // Test open amount
        final openValidation = ZambiaQRFormat.validateAmountDetailed(0.0);
        expect(openValidation.isValid, isTrue);
        expect(openValidation.isOpenAmount, isTrue);
      });
    });

    group('Merchant Category Recommendations', () {
      test('should provide category-specific amount suggestions', () {
        final retailAmounts = ZambiaQRFormat.getSuggestedAmounts('RETAIL');
        expect(retailAmounts, contains(10.0));
        expect(retailAmounts, contains(20.0));
        expect(retailAmounts, contains(50.0));

        final transportAmounts = ZambiaQRFormat.getSuggestedAmounts('TRANSPORT');
        expect(transportAmounts, contains(15.0));
        expect(transportAmounts, contains(25.0));

        final foodAmounts = ZambiaQRFormat.getSuggestedAmounts('FOOD');
        expect(foodAmounts, contains(25.0));
        expect(foodAmounts, contains(50.0));
      });

      test('should validate amounts within category ranges', () {
        final ranges = ZambiaQRFormat.getRecommendedAmountRanges();
        
        final retailRange = ranges['RETAIL']!;
        expect(retailRange.contains(50.0), isTrue);
        expect(retailRange.contains(600.0), isFalse);

        final transportRange = ranges['TRANSPORT']!;
        expect(transportRange.contains(25.0), isTrue);
        expect(transportRange.contains(300.0), isFalse);
      });
    });

    group('QR Format Compliance', () {
      test('should generate QR with correct Zambian format', () async {
        // Arrange
        const merchantId = 'zm_test_merchant';
        const amount = 100.0;

        // Act
        final qrData = await PayMuleQR.generateQR(
          merchantId: merchantId,
          amount: amount,
          currency: 'ZMW',
        );

        final qrString = String.fromCharCodes(qrData);

        // Assert
        expect(qrString, startsWith('ZPAY:'));
        expect(ZambiaQRFormat.isValidFormat(qrString), isTrue);
      });

      test('should include required Zambian QR fields', () async {
        // Arrange
        const merchantId = 'zm_complete_merchant';
        const amount = 75.0;
        const description = 'Test payment';

        // Act
        final qrData = await PayMuleQR.generateQR(
          merchantId: merchantId,
          amount: amount,
          currency: 'ZMW',
          description: description,
        );

        final qrString = String.fromCharCodes(qrData);
        final decoded = await ZambiaQRFormat.decode(qrString);

        // Assert - Check all required fields
        expect(decoded.merchantId, isNotEmpty);
        expect(decoded.amount, greaterThan(0));
        expect(decoded.currency, equals('ZMW'));
        expect(decoded.provider, isNotEmpty);
        expect(decoded.timestamp, isNotNull);
        expect(decoded.isValid, isTrue);
      });

      test('should generate QR with proper expiry', () async {
        // Arrange
        const merchantId = 'zm_expiry_merchant';
        const amount = 30.0;
        final expiryDuration = const Duration(minutes: 15);

        // Act
        final qrData = await PayMuleQR.generateQR(
          merchantId: merchantId,
          amount: amount,
          currency: 'ZMW',
          expiryDuration: expiryDuration,
        );

        final qrString = String.fromCharCodes(qrData);
        final decoded = await ZambiaQRFormat.decode(qrString);

        // Assert
        expect(decoded.expiryTime, isNotNull);
        expect(decoded.isExpired, isFalse);
        
        final expectedExpiry = DateTime.now().add(expiryDuration);
        final actualExpiry = decoded.expiryTime!;
        final timeDifference = actualExpiry.difference(expectedExpiry).abs();
        expect(timeDifference.inMinutes, lessThanOrEqualTo(1));
      });
    });

    group('Performance Tests', () {
      test('should generate QR within acceptable time limits', () async {
        // Arrange
        const merchantId = 'zm_performance_merchant';
        const amount = 50.0;
        final stopwatch = Stopwatch()..start();

        // Act
        final qrData = await PayMuleQR.generateQR(
          merchantId: merchantId,
          amount: amount,
          currency: 'ZMW',
        );

        stopwatch.stop();

        // Assert
        expect(qrData, isNotNull);
        expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // Under 1 second
        
        print('QR Generation Time: ${stopwatch.elapsedMilliseconds}ms');
      });

      test('should handle batch QR generation efficiently', () async {
        // Arrange
        const batchSize = 10;
        final stopwatch = Stopwatch()..start();

        // Act
        final futures = List.generate(batchSize, (index) {
          return PayMuleQR.generateQR(
            merchantId: 'zm_batch_merchant_$index',
            amount: 10.0 + (index * 5),
            currency: 'ZMW',
          );
        });

        final results = await Future.wait(futures);
        stopwatch.stop();

        // Assert
        expect(results.length, equals(batchSize));
        expect(results.every((qr) => qr.isNotEmpty), isTrue);
        expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // Under 5 seconds
        
        print('Batch QR Generation Time: ${stopwatch.elapsedMilliseconds}ms for $batchSize QRs');
      });
    });

    group('Error Handling', () {
      test('should handle invalid merchant ID gracefully', () async {
        // Arrange
        const invalidMerchantId = '';
        const amount = 50.0;

        // Act & Assert
        expect(
          () => PayMuleQR.generateQR(
            merchantId: invalidMerchantId,
            amount: amount,
            currency: 'ZMW',
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle invalid amounts gracefully', () async {
        // Arrange
        const merchantId = 'zm_error_merchant';
        const invalidAmount = -50.0;

        // Act & Assert
        expect(
          () => PayMuleQR.generateQR(
            merchantId: merchantId,
            amount: invalidAmount,
            currency: 'ZMW',
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle network errors during generation', () async {
        // This test would be more relevant in integration tests
        // For unit tests, we can mock network failures
        
        // Arrange
        const merchantId = 'zm_network_merchant';
        const amount = 25.0;

        // Act & Assert
        // In a real scenario, this would test offline QR generation
        final qrData = await PayMuleQR.generateQR(
          merchantId: merchantId,
          amount: amount,
          currency: 'ZMW',
        );

        expect(qrData, isNotNull);
      });
    });

    group('Zambian Production Environment Tests', () {
      test('should use production settings when ENV=zm_prod', () {
        if (isZambianProduction) {
          // Test production-specific configurations
          expect(AppConstants.currencyCode, equals('ZMW'));
          expect(AppConstants.currencySymbol, equals('K'));
          expect(AppConstants.maxTransactionAmount, equals(50000.0));
          
          print('✅ Production environment settings verified');
        } else {
          print('ℹ️ Skipping production tests (not in zm_prod environment)');
        }
      });

      test('should generate QR compatible with Zambian mobile money', () async {
        if (isZambianProduction) {
          // Arrange
          const merchantId = 'zm_prod_merchant_001';
          const amount = 150.0;

          // Act
          final qrData = await PayMuleQR.generateQR(
            merchantId: merchantId,
            amount: amount,
            currency: 'ZMW',
            description: 'Production test payment',
          );

          final qrString = String.fromCharCodes(qrData);
          final decoded = await ZambiaQRFormat.decode(qrString);

          // Assert production compliance
          expect(decoded.currency, equals('ZMW'));
          expect(decoded.provider, isIn(['MTN', 'AIRTEL', 'ZAMTEL']));
          expect(ZambiaQRFormat.is2GOptimized(qrString), isTrue);
          
          print('✅ Production QR generated and validated');
        }
      });
    });

    tearDown(() async {
      // Clean up test resources
    });

    tearDownAll(() async {
      print('🏁 QR Generation Tests completed');
    });
  });
}
