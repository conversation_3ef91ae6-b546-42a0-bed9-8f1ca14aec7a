// SECURITY PIN SERVICE
// Secure PIN setup and validation with biometric support

import 'dart:async';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecurityPINResult {
  final bool success;
  final String? message;
  final bool requiresBiometric;

  SecurityPINResult({
    required this.success,
    this.message,
    this.requiresBiometric = false,
  });
}

class PINValidationResult {
  final bool isValid;
  final String? message;
  final int attemptsRemaining;
  final bool isLocked;
  final DateTime? lockUntil;

  PINValidationResult({
    required this.isValid,
    this.message,
    required this.attemptsRemaining,
    required this.isLocked,
    this.lockUntil,
  });
}

class SecurityPINService {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  static const LocalAuthentication _localAuth = LocalAuthentication();
  
  // Storage keys
  static const String _pinHashKey = 'security_pin_hash';
  static const String _pinSaltKey = 'security_pin_salt';
  static const String _failedAttemptsKey = 'pin_failed_attempts';
  static const String _lockUntilKey = 'pin_lock_until';
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _pinSetupCompleteKey = 'pin_setup_complete';
  
  // Security settings
  static const int _maxFailedAttempts = 5;
  static const int _lockDurationMinutes = 30;
  static const int _defaultMinLength = 6;

  /// Setup security PIN with minimum length requirement
  static Future<SecurityPINResult> setupSecurityPIN({
    required String pin,
    required String confirmPin,
    int minLength = _defaultMinLength,
    bool enableBiometric = true,
  }) async {
    try {
      print('🔐 Setting up security PIN...');

      // Validate PIN requirements
      final validation = _validatePINRequirements(pin, confirmPin, minLength);
      if (!validation.success) {
        return validation;
      }

      // Check if biometric is available and requested
      bool biometricAvailable = false;
      if (enableBiometric) {
        biometricAvailable = await _checkBiometricAvailability();
      }

      // Generate salt and hash PIN
      final salt = _generateSalt();
      final hashedPIN = _hashPIN(pin, salt);

      // Store PIN securely
      await _secureStorage.write(key: _pinHashKey, value: hashedPIN);
      await _secureStorage.write(key: _pinSaltKey, value: salt);
      await _secureStorage.write(key: _biometricEnabledKey, value: biometricAvailable.toString());
      await _secureStorage.write(key: _pinSetupCompleteKey, value: 'true');

      // Clear any previous failed attempts
      await _clearFailedAttempts();

      print('✅ Security PIN setup completed');
      print('🔒 Biometric enabled: $biometricAvailable');

      return SecurityPINResult(
        success: true,
        message: 'Security PIN setup completed successfully',
        requiresBiometric: biometricAvailable,
      );

    } catch (e) {
      print('❌ PIN setup failed: $e');
      return SecurityPINResult(
        success: false,
        message: 'Failed to setup security PIN',
      );
    }
  }

  /// Validate PIN requirements
  static SecurityPINResult _validatePINRequirements(
    String pin,
    String confirmPin,
    int minLength,
  ) {
    // Check PIN length
    if (pin.length < minLength) {
      return SecurityPINResult(
        success: false,
        message: 'PIN must be at least $minLength digits long',
      );
    }

    // Check if PIN contains only digits
    if (!RegExp(r'^\d+$').hasMatch(pin)) {
      return SecurityPINResult(
        success: false,
        message: 'PIN must contain only numbers',
      );
    }

    // Check PIN confirmation
    if (pin != confirmPin) {
      return SecurityPINResult(
        success: false,
        message: 'PIN confirmation does not match',
      );
    }

    // Check for weak PINs
    if (_isWeakPIN(pin)) {
      return SecurityPINResult(
        success: false,
        message: 'PIN is too weak. Avoid sequential or repeated numbers',
      );
    }

    return SecurityPINResult(success: true);
  }

  /// Check if PIN is weak (sequential or repeated digits)
  static bool _isWeakPIN(String pin) {
    // Check for repeated digits (e.g., 111111, 000000)
    if (RegExp(r'^(\d)\1+$').hasMatch(pin)) {
      return true;
    }

    // Check for sequential digits (e.g., 123456, 654321)
    bool isSequential = true;
    for (int i = 1; i < pin.length; i++) {
      int current = int.parse(pin[i]);
      int previous = int.parse(pin[i - 1]);
      if (current != previous + 1 && current != previous - 1) {
        isSequential = false;
        break;
      }
    }

    return isSequential;
  }

  /// Verify security PIN
  static Future<PINValidationResult> verifyPIN(String pin) async {
    try {
      // Check if PIN is locked
      final lockStatus = await _checkLockStatus();
      if (lockStatus.isLocked) {
        return lockStatus;
      }

      // Get stored PIN hash and salt
      final storedHash = await _secureStorage.read(key: _pinHashKey);
      final salt = await _secureStorage.read(key: _pinSaltKey);

      if (storedHash == null || salt == null) {
        return PINValidationResult(
          isValid: false,
          message: 'No PIN found. Please setup your PIN first.',
          attemptsRemaining: 0,
          isLocked: false,
        );
      }

      // Hash provided PIN and compare
      final hashedPIN = _hashPIN(pin, salt);
      final isValid = hashedPIN == storedHash;

      if (isValid) {
        // Clear failed attempts on successful verification
        await _clearFailedAttempts();
        print('✅ PIN verified successfully');
        
        return PINValidationResult(
          isValid: true,
          message: 'PIN verified successfully',
          attemptsRemaining: _maxFailedAttempts,
          isLocked: false,
        );
      } else {
        // Increment failed attempts
        final attemptsRemaining = await _incrementFailedAttempts();
        print('❌ Invalid PIN. Attempts remaining: $attemptsRemaining');
        
        if (attemptsRemaining <= 0) {
          final lockUntil = await _lockPIN();
          return PINValidationResult(
            isValid: false,
            message: 'Too many failed attempts. PIN locked for $_lockDurationMinutes minutes.',
            attemptsRemaining: 0,
            isLocked: true,
            lockUntil: lockUntil,
          );
        }
        
        return PINValidationResult(
          isValid: false,
          message: 'Invalid PIN. $attemptsRemaining attempts remaining.',
          attemptsRemaining: attemptsRemaining,
          isLocked: false,
        );
      }

    } catch (e) {
      print('❌ PIN verification failed: $e');
      return PINValidationResult(
        isValid: false,
        message: 'PIN verification failed',
        attemptsRemaining: 0,
        isLocked: false,
      );
    }
  }

  /// Authenticate with biometric
  static Future<bool> authenticateWithBiometric() async {
    try {
      // Check if biometric is enabled
      final biometricEnabled = await _secureStorage.read(key: _biometricEnabledKey);
      if (biometricEnabled != 'true') {
        return false;
      }

      // Check if biometric is available
      final isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) {
        return false;
      }

      // Authenticate
      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: 'Authenticate to access Pay Mule',
        options: AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (isAuthenticated) {
        print('✅ Biometric authentication successful');
        await _clearFailedAttempts();
      }

      return isAuthenticated;

    } catch (e) {
      print('❌ Biometric authentication failed: $e');
      return false;
    }
  }

  /// Check biometric availability
  static Future<bool> _checkBiometricAvailability() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) return false;

      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      return availableBiometrics.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Generate salt for PIN hashing
  static String _generateSalt() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = timestamp.toString() + DateTime.now().microsecond.toString();
    return sha256.convert(utf8.encode(random)).toString().substring(0, 16);
  }

  /// Hash PIN with salt
  static String _hashPIN(String pin, String salt) {
    final combined = pin + salt;
    return sha256.convert(utf8.encode(combined)).toString();
  }

  /// Check lock status
  static Future<PINValidationResult> _checkLockStatus() async {
    try {
      final lockUntilString = await _secureStorage.read(key: _lockUntilKey);
      if (lockUntilString == null) {
        return PINValidationResult(
          isValid: false,
          attemptsRemaining: _maxFailedAttempts,
          isLocked: false,
        );
      }

      final lockUntil = DateTime.parse(lockUntilString);
      final now = DateTime.now();

      if (now.isBefore(lockUntil)) {
        final remainingMinutes = lockUntil.difference(now).inMinutes + 1;
        return PINValidationResult(
          isValid: false,
          message: 'PIN is locked. Try again in $remainingMinutes minutes.',
          attemptsRemaining: 0,
          isLocked: true,
          lockUntil: lockUntil,
        );
      } else {
        // Lock expired, clear it
        await _clearLock();
        return PINValidationResult(
          isValid: false,
          attemptsRemaining: _maxFailedAttempts,
          isLocked: false,
        );
      }
    } catch (e) {
      return PINValidationResult(
        isValid: false,
        attemptsRemaining: _maxFailedAttempts,
        isLocked: false,
      );
    }
  }

  /// Increment failed attempts
  static Future<int> _incrementFailedAttempts() async {
    try {
      final attemptsString = await _secureStorage.read(key: _failedAttemptsKey);
      final currentAttempts = int.tryParse(attemptsString ?? '0') ?? 0;
      final newAttempts = currentAttempts + 1;
      
      await _secureStorage.write(key: _failedAttemptsKey, value: newAttempts.toString());
      
      return _maxFailedAttempts - newAttempts;
    } catch (e) {
      return 0;
    }
  }

  /// Lock PIN for specified duration
  static Future<DateTime> _lockPIN() async {
    final lockUntil = DateTime.now().add(Duration(minutes: _lockDurationMinutes));
    await _secureStorage.write(key: _lockUntilKey, value: lockUntil.toIso8601String());
    return lockUntil;
  }

  /// Clear failed attempts
  static Future<void> _clearFailedAttempts() async {
    await _secureStorage.delete(key: _failedAttemptsKey);
  }

  /// Clear lock
  static Future<void> _clearLock() async {
    await _secureStorage.delete(key: _lockUntilKey);
    await _clearFailedAttempts();
  }

  /// Check if PIN is setup
  static Future<bool> isPINSetup() async {
    final setupComplete = await _secureStorage.read(key: _pinSetupCompleteKey);
    return setupComplete == 'true';
  }

  /// Check if biometric is enabled
  static Future<bool> isBiometricEnabled() async {
    final biometricEnabled = await _secureStorage.read(key: _biometricEnabledKey);
    return biometricEnabled == 'true';
  }

  /// Change PIN
  static Future<SecurityPINResult> changePIN({
    required String currentPIN,
    required String newPIN,
    required String confirmNewPIN,
    int minLength = _defaultMinLength,
  }) async {
    try {
      // Verify current PIN
      final currentPINResult = await verifyPIN(currentPIN);
      if (!currentPINResult.isValid) {
        return SecurityPINResult(
          success: false,
          message: 'Current PIN is incorrect',
        );
      }

      // Setup new PIN
      return await setupSecurityPIN(
        pin: newPIN,
        confirmPin: confirmNewPIN,
        minLength: minLength,
        enableBiometric: await isBiometricEnabled(),
      );

    } catch (e) {
      return SecurityPINResult(
        success: false,
        message: 'Failed to change PIN',
      );
    }
  }

  /// Reset PIN (requires biometric or other verification)
  static Future<bool> resetPIN() async {
    try {
      // Require biometric authentication for reset
      final biometricAuth = await authenticateWithBiometric();
      if (!biometricAuth) {
        return false;
      }

      // Clear all PIN data
      await _secureStorage.delete(key: _pinHashKey);
      await _secureStorage.delete(key: _pinSaltKey);
      await _secureStorage.delete(key: _pinSetupCompleteKey);
      await _clearLock();

      print('✅ PIN reset completed');
      return true;

    } catch (e) {
      print('❌ PIN reset failed: $e');
      return false;
    }
  }

  /// Get remaining lock time in minutes
  static Future<int> getRemainingLockTimeMinutes() async {
    try {
      final lockUntilString = await _secureStorage.read(key: _lockUntilKey);
      if (lockUntilString == null) return 0;

      final lockUntil = DateTime.parse(lockUntilString);
      final remaining = lockUntil.difference(DateTime.now()).inMinutes;
      return remaining > 0 ? remaining : 0;
    } catch (e) {
      return 0;
    }
  }
}
