// VALIDATION DEMO
// Demonstrates in-app production validation with assertions

import 'package:flutter/material.dart';
import 'dart:async';
import '../core/validation/in_app_validator.dart';
import '../core/validation/environment_validator.dart';
import '../core/validation/demo_mode_validator.dart';
import '../core/validation/mtn_service_validator.dart';

class ValidationDemo extends StatefulWidget {
  @override
  _ValidationDemoState createState() => _ValidationDemoState();
}

class _ValidationDemoState extends State<ValidationDemo> {
  bool isInitialized = false;
  bool isValidating = false;
  ValidationResult? validationResult;
  Map<String, dynamic>? validationSummary;
  String? assertionError;
  
  @override
  void initState() {
    super.initState();
    _initializeValidation();
  }

  /// Initialize validation system
  Future<void> _initializeValidation() async {
    try {
      setState(() {
        isInitialized = false;
      });

      await InAppValidator.initialize();

      setState(() {
        isInitialized = true;
      });

      print('✅ Validation system initialized');
    } catch (e) {
      print('❌ Validation initialization failed: $e');
      setState(() {
        assertionError = 'Initialization failed: ${e.toString()}';
      });
    }
  }

  /// Perform production validation
  Future<void> _performValidation() async {
    if (!isInitialized) return;

    setState(() {
      isValidating = true;
      validationResult = null;
      assertionError = null;
    });

    try {
      final result = await InAppValidator.validateProduction();
      final summary = InAppValidator.getValidationSummary();

      setState(() {
        validationResult = result;
        validationSummary = summary;
        isValidating = false;
      });

    } catch (e) {
      setState(() {
        assertionError = 'Validation failed: ${e.toString()}';
        isValidating = false;
      });
    }
  }

  /// Test production assertions
  void _testAssertions() {
    setState(() {
      assertionError = null;
    });

    try {
      print('🔍 Testing production assertions...');

      // IN-APP VALIDATION IMPLEMENTATION
      // assert(Environment.isProduction, "Must be production mode");
      InAppValidator.assertProduction();
      
      // assert(!DemoMode.active, "Demo mode disabled");
      InAppValidator.assertDemoModeDisabled();
      
      // assert(MTNService.isRealEndpoint, "MTN production active");
      InAppValidator.assertMTNRealEndpoint();

      // All assertions passed
      setState(() {
        assertionError = null;
      });

      _showSnackBar('✅ All production assertions passed!', isError: false);

    } catch (ValidationException e) {
      setState(() {
        assertionError = 'Assertion failed: ${e.message}';
      });
      _showSnackBar('❌ Assertion failed: ${e.message}', isError: true);
    } catch (e) {
      setState(() {
        assertionError = 'Assertion error: ${e.toString()}';
      });
      _showSnackBar('❌ Assertion error: ${e.toString()}', isError: true);
    }
  }

  /// Show snackbar message
  void _showSnackBar(String message, {required bool isError}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red[600] : Colors.green[600],
        duration: Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('🔍 In-App Validation Demo'),
        backgroundColor: Colors.indigo[800],
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Initialization status
            Card(
              color: isInitialized ? Colors.green[50] : Colors.orange[50],
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      isInitialized ? Icons.check_circle : Icons.hourglass_empty,
                      color: isInitialized ? Colors.green : Colors.orange,
                    ),
                    SizedBox(width: 8),
                    Text(
                      isInitialized 
                          ? 'Validation System: Initialized'
                          : 'Validation System: Initializing...',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),

            // Validation code display
            Card(
              color: Colors.grey[100],
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Production Validation Code:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.black87,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '// IN-APP VALIDATION\n'
                        'assert(Environment.isProduction, "Must be production mode");\n'
                        'assert(!DemoMode.active, "Demo mode disabled");\n'
                        'assert(MTNService.isRealEndpoint, "MTN production active");',
                        style: TextStyle(
                          fontFamily: 'monospace',
                          color: Colors.green[300],
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: isInitialized && !isValidating ? _performValidation : null,
                    icon: isValidating ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ) : Icon(Icons.security),
                    label: Text('Validate Production'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: isInitialized ? _testAssertions : null,
                    icon: Icon(Icons.verified),
                    label: Text('Test Assertions'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green[600],
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),

            // Validation results
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Assertion error display
                    if (assertionError != null) ...[
                      Card(
                        color: Colors.red[50],
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.error, color: Colors.red),
                                  SizedBox(width: 8),
                                  Text(
                                    'Assertion Error',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.red[800],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 8),
                              Text(
                                assertionError!,
                                style: TextStyle(color: Colors.red[700]),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 16),
                    ],

                    // Validation result display
                    if (validationResult != null) ...[
                      Card(
                        color: validationResult!.isValid ? Colors.green[50] : Colors.red[50],
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    validationResult!.isValid ? Icons.check_circle : Icons.error,
                                    color: validationResult!.isValid ? Colors.green : Colors.red,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Validation Result',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: validationResult!.isValid ? Colors.green[800] : Colors.red[800],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 8),
                              Text(
                                validationResult!.isValid 
                                    ? 'All production validations passed!'
                                    : validationResult!.message ?? 'Validation failed',
                                style: TextStyle(
                                  color: validationResult!.isValid ? Colors.green[700] : Colors.red[700],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 16),
                    ],

                    // Validation summary
                    if (validationSummary != null) ...[
                      _buildValidationSummary(),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildValidationSummary() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Validation Summary',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),

            // Environment status
            _buildStatusTile(
              'Environment',
              validationSummary!['environment']['isProduction'] ?? false,
              'Current: ${validationSummary!['environment']['name'] ?? 'Unknown'}',
              Icons.settings,
            ),

            // Demo mode status
            _buildStatusTile(
              'Demo Mode',
              !(validationSummary!['demoMode']['active'] ?? true),
              validationSummary!['demoMode']['active'] == true ? 'Active (❌)' : 'Disabled (✅)',
              Icons.science,
            ),

            // MTN service status
            _buildStatusTile(
              'MTN Service',
              validationSummary!['mtnService']['isRealEndpoint'] ?? false,
              'Endpoint: ${validationSummary!['mtnService']['currentEndpoint'] ?? 'Unknown'}',
              Icons.api,
            ),

            SizedBox(height: 16),

            // Detailed validation results
            if (validationSummary!['validationResults'] != null) ...[
              Text(
                'Detailed Results:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              ...((validationSummary!['validationResults'] as Map<String, dynamic>).entries.map((entry) {
                final result = entry.value as Map<String, dynamic>;
                return ListTile(
                  leading: Icon(
                    result['isValid'] ? Icons.check : Icons.close,
                    color: result['isValid'] ? Colors.green : Colors.red,
                  ),
                  title: Text(entry.key.replaceAll('_', ' ').toUpperCase()),
                  subtitle: result['message'] != null ? Text(result['message']) : null,
                  dense: true,
                );
              }).toList()),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusTile(String title, bool isValid, String subtitle, IconData icon) {
    return ListTile(
      leading: Icon(
        icon,
        color: isValid ? Colors.green : Colors.red,
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Icon(
        isValid ? Icons.check_circle : Icons.error,
        color: isValid ? Colors.green : Colors.red,
      ),
    );
  }
}
