// TEST TRANSACTION SERVICE
// Handles test transactions via ADB broadcasts and cross-network transfers

import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/services.dart';
import '../wallet/mobile_money_detector.dart';

enum TransactionType {
  mtnToAirtel,
  airtelToMtn,
  mtnToZamtel,
  zamtelToMtn,
  airtelToZamtel,
  zamtelToAirtel,
  sameNetwork,
  unknown
}

enum TransactionStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
  timeout
}

class TestTransactionRequest {
  final String receiver;
  final double amount;
  final TransactionType type;
  final String? reference;
  final Map<String, dynamic>? metadata;

  TestTransactionRequest({
    required this.receiver,
    required this.amount,
    required this.type,
    this.reference,
    this.metadata,
  });

  factory TestTransactionRequest.fromBroadcast(Map<String, dynamic> data) {
    return TestTransactionRequest(
      receiver: data['receiver'] ?? '',
      amount: double.tryParse(data['amount']?.toString() ?? '0') ?? 0.0,
      type: _parseTransactionType(data['type']?.toString()),
      reference: data['reference'],
      metadata: data['metadata'],
    );
  }

  static TransactionType _parseTransactionType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'mtn_to_airtel':
        return TransactionType.mtnToAirtel;
      case 'airtel_to_mtn':
        return TransactionType.airtelToMtn;
      case 'mtn_to_zamtel':
        return TransactionType.mtnToZamtel;
      case 'zamtel_to_mtn':
        return TransactionType.zamtelToMtn;
      case 'airtel_to_zamtel':
        return TransactionType.airtelToZamtel;
      case 'zamtel_to_airtel':
        return TransactionType.zamtelToAirtel;
      case 'same_network':
        return TransactionType.sameNetwork;
      default:
        return TransactionType.unknown;
    }
  }

  Map<String, dynamic> toJson() => {
    'receiver': receiver,
    'amount': amount,
    'type': type.toString(),
    'reference': reference,
    'metadata': metadata,
  };
}

class TestTransactionResult {
  final bool success;
  final String transactionId;
  final TransactionStatus status;
  final String? message;
  final double? fee;
  final DateTime timestamp;
  final Map<String, dynamic>? details;

  TestTransactionResult({
    required this.success,
    required this.transactionId,
    required this.status,
    this.message,
    this.fee,
    required this.timestamp,
    this.details,
  });

  Map<String, dynamic> toJson() => {
    'success': success,
    'transactionId': transactionId,
    'status': status.toString(),
    'message': message,
    'fee': fee,
    'timestamp': timestamp.toIso8601String(),
    'details': details,
  };
}

class TestTransactionService {
  static const MethodChannel _channel = MethodChannel('test_transaction_service');
  static const String _broadcastAction = 'com.zm.paymule.TEST_TRANSACTION';
  
  static final StreamController<TestTransactionRequest> _requestController = 
      StreamController<TestTransactionRequest>.broadcast();
  static final StreamController<TestTransactionResult> _resultController = 
      StreamController<TestTransactionResult>.broadcast();
  
  static bool _isInitialized = false;
  static final Map<String, TestTransactionResult> _transactionHistory = {};

  /// Initialize test transaction service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🧪 Initializing test transaction service...');

      // Setup broadcast receiver
      await _setupBroadcastReceiver();
      
      // Setup method channel handler
      _channel.setMethodCallHandler(_handleMethodCall);
      
      _isInitialized = true;
      print('✅ Test transaction service initialized');

    } catch (e) {
      print('❌ Failed to initialize test transaction service: $e');
    }
  }

  /// Setup broadcast receiver for ADB commands
  static Future<void> _setupBroadcastReceiver() async {
    try {
      await _channel.invokeMethod('setupBroadcastReceiver', {
        'action': _broadcastAction,
      });
      print('📡 Broadcast receiver setup for: $_broadcastAction');
    } catch (e) {
      print('❌ Failed to setup broadcast receiver: $e');
    }
  }

  /// Handle method calls from native side
  static Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onTestTransactionReceived':
        await _handleTestTransactionBroadcast(call.arguments);
        break;
      default:
        print('Unknown method call: ${call.method}');
    }
  }

  /// Handle test transaction broadcast from ADB
  static Future<void> _handleTestTransactionBroadcast(Map<dynamic, dynamic> data) async {
    try {
      print('📨 Received test transaction broadcast:');
      print('   Data: $data');

      // Parse broadcast data
      final request = TestTransactionRequest.fromBroadcast(
        Map<String, dynamic>.from(data)
      );

      print('📋 Parsed transaction request:');
      print('   Receiver: ${request.receiver}');
      print('   Amount: K${request.amount.toStringAsFixed(2)}');
      print('   Type: ${request.type}');

      // Validate request
      final validation = _validateTransactionRequest(request);
      if (!validation.isValid) {
        print('❌ Transaction validation failed: ${validation.message}');
        return;
      }

      // Add to request stream
      _requestController.add(request);

      // Execute transaction
      final result = await executeTestTransaction(request);
      
      // Add to result stream
      _resultController.add(result);

      print('✅ Test transaction completed: ${result.transactionId}');

    } catch (e) {
      print('❌ Failed to handle test transaction broadcast: $e');
    }
  }

  /// Execute test transaction
  static Future<TestTransactionResult> executeTestTransaction(TestTransactionRequest request) async {
    final transactionId = _generateTransactionId();
    final timestamp = DateTime.now();

    try {
      print('🚀 Executing test transaction: $transactionId');
      print('   From: ${_getSourceProvider(request.type)}');
      print('   To: ${_getDestinationProvider(request.type)}');
      print('   Amount: K${request.amount.toStringAsFixed(2)}');
      print('   Receiver: ${request.receiver}');

      // Validate transaction parameters
      final validation = _validateTransactionRequest(request);
      if (!validation.isValid) {
        final result = TestTransactionResult(
          success: false,
          transactionId: transactionId,
          status: TransactionStatus.failed,
          message: validation.message,
          timestamp: timestamp,
        );
        _transactionHistory[transactionId] = result;
        return result;
      }

      // Calculate transaction fee
      final fee = _calculateTransactionFee(request);

      // Simulate transaction processing
      await _simulateTransactionProcessing(request);

      // Execute cross-network transfer
      final transferResult = await _executeCrossNetworkTransfer(request, transactionId);

      final result = TestTransactionResult(
        success: transferResult.success,
        transactionId: transactionId,
        status: transferResult.success ? TransactionStatus.completed : TransactionStatus.failed,
        message: transferResult.message,
        fee: fee,
        timestamp: timestamp,
        details: {
          'sourceProvider': _getSourceProvider(request.type),
          'destinationProvider': _getDestinationProvider(request.type),
          'receiver': request.receiver,
          'amount': request.amount,
          'fee': fee,
          'totalAmount': request.amount + fee,
          'reference': request.reference,
          'processingTime': DateTime.now().difference(timestamp).inMilliseconds,
        },
      );

      _transactionHistory[transactionId] = result;
      
      print('✅ Transaction ${result.success ? "completed" : "failed"}: $transactionId');
      if (result.success) {
        print('   Fee: K${fee.toStringAsFixed(2)}');
        print('   Total: K${(request.amount + fee).toStringAsFixed(2)}');
      }

      return result;

    } catch (e) {
      print('❌ Transaction execution failed: $e');
      
      final result = TestTransactionResult(
        success: false,
        transactionId: transactionId,
        status: TransactionStatus.failed,
        message: 'Transaction execution failed: ${e.toString()}',
        timestamp: timestamp,
      );
      
      _transactionHistory[transactionId] = result;
      return result;
    }
  }

  /// Validate transaction request
  static TransactionValidation _validateTransactionRequest(TestTransactionRequest request) {
    // Validate receiver phone number
    if (!_isValidZambianPhoneNumber(request.receiver)) {
      return TransactionValidation(
        isValid: false,
        message: 'Invalid Zambian phone number: ${request.receiver}',
      );
    }

    // Validate amount
    if (request.amount <= 0) {
      return TransactionValidation(
        isValid: false,
        message: 'Amount must be greater than 0',
      );
    }

    if (request.amount < 1.0) {
      return TransactionValidation(
        isValid: false,
        message: 'Minimum transaction amount is K1.00',
      );
    }

    if (request.amount > 50000.0) {
      return TransactionValidation(
        isValid: false,
        message: 'Maximum transaction amount is K50,000.00',
      );
    }

    // Validate transaction type
    if (request.type == TransactionType.unknown) {
      return TransactionValidation(
        isValid: false,
        message: 'Invalid transaction type',
      );
    }

    return TransactionValidation(isValid: true);
  }

  /// Calculate transaction fee
  static double _calculateTransactionFee(TestTransactionRequest request) {
    // Base fee structure for cross-network transfers
    const baseFee = 2.0; // K2.00 base fee
    const percentageFee = 0.01; // 1% of amount
    
    // Cross-network transfers have higher fees
    final crossNetworkMultiplier = request.type == TransactionType.sameNetwork ? 1.0 : 1.5;
    
    final calculatedFee = (baseFee + (request.amount * percentageFee)) * crossNetworkMultiplier;
    
    // Round to 2 decimal places
    return double.parse(calculatedFee.toStringAsFixed(2));
  }

  /// Simulate transaction processing delay
  static Future<void> _simulateTransactionProcessing(TestTransactionRequest request) async {
    // Simulate realistic processing times
    final processingTime = request.type == TransactionType.sameNetwork 
        ? 2000 + Random().nextInt(1000)  // 2-3 seconds for same network
        : 3000 + Random().nextInt(2000); // 3-5 seconds for cross-network
    
    print('⏳ Processing transaction... (${processingTime}ms)');
    await Future.delayed(Duration(milliseconds: processingTime));
  }

  /// Execute cross-network transfer
  static Future<TransferResult> _executeCrossNetworkTransfer(
    TestTransactionRequest request,
    String transactionId,
  ) async {
    try {
      final sourceProvider = _getSourceProvider(request.type);
      final destinationProvider = _getDestinationProvider(request.type);

      print('🔄 Executing cross-network transfer:');
      print('   $sourceProvider → $destinationProvider');

      // Simulate provider-specific processing
      switch (request.type) {
        case TransactionType.mtnToAirtel:
          return await _processMTNToAirtel(request, transactionId);
        case TransactionType.airtelToMtn:
          return await _processAirtelToMTN(request, transactionId);
        case TransactionType.mtnToZamtel:
          return await _processMTNToZamtel(request, transactionId);
        case TransactionType.zamtelToMtn:
          return await _processZamtelToMTN(request, transactionId);
        case TransactionType.airtelToZamtel:
          return await _processAirtelToZamtel(request, transactionId);
        case TransactionType.zamtelToAirtel:
          return await _processZamtelToAirtel(request, transactionId);
        case TransactionType.sameNetwork:
          return await _processSameNetwork(request, transactionId);
        default:
          return TransferResult(
            success: false,
            message: 'Unsupported transaction type',
          );
      }

    } catch (e) {
      return TransferResult(
        success: false,
        message: 'Cross-network transfer failed: ${e.toString()}',
      );
    }
  }

  /// Process MTN to Airtel transfer
  static Future<TransferResult> _processMTNToAirtel(
    TestTransactionRequest request,
    String transactionId,
  ) async {
    print('📱 Processing MTN → Airtel transfer...');
    
    // Simulate MTN debit
    await Future.delayed(Duration(milliseconds: 1000));
    print('   ✅ MTN debit successful');
    
    // Simulate Airtel credit
    await Future.delayed(Duration(milliseconds: 1500));
    print('   ✅ Airtel credit successful');
    
    return TransferResult(
      success: true,
      message: 'MTN to Airtel transfer completed successfully',
    );
  }

  /// Process Airtel to MTN transfer
  static Future<TransferResult> _processAirtelToMTN(
    TestTransactionRequest request,
    String transactionId,
  ) async {
    print('📱 Processing Airtel → MTN transfer...');
    
    await Future.delayed(Duration(milliseconds: 1200));
    print('   ✅ Airtel debit successful');
    
    await Future.delayed(Duration(milliseconds: 1300));
    print('   ✅ MTN credit successful');
    
    return TransferResult(
      success: true,
      message: 'Airtel to MTN transfer completed successfully',
    );
  }

  /// Process MTN to Zamtel transfer
  static Future<TransferResult> _processMTNToZamtel(
    TestTransactionRequest request,
    String transactionId,
  ) async {
    print('📱 Processing MTN → Zamtel transfer...');
    
    await Future.delayed(Duration(milliseconds: 1100));
    print('   ✅ MTN debit successful');
    
    await Future.delayed(Duration(milliseconds: 1400));
    print('   ✅ Zamtel credit successful');
    
    return TransferResult(
      success: true,
      message: 'MTN to Zamtel transfer completed successfully',
    );
  }

  /// Process Zamtel to MTN transfer
  static Future<TransferResult> _processZamtelToMTN(
    TestTransactionRequest request,
    String transactionId,
  ) async {
    print('📱 Processing Zamtel → MTN transfer...');
    
    await Future.delayed(Duration(milliseconds: 1300));
    print('   ✅ Zamtel debit successful');
    
    await Future.delayed(Duration(milliseconds: 1200));
    print('   ✅ MTN credit successful');
    
    return TransferResult(
      success: true,
      message: 'Zamtel to MTN transfer completed successfully',
    );
  }

  /// Process Airtel to Zamtel transfer
  static Future<TransferResult> _processAirtelToZamtel(
    TestTransactionRequest request,
    String transactionId,
  ) async {
    print('📱 Processing Airtel → Zamtel transfer...');
    
    await Future.delayed(Duration(milliseconds: 1250));
    print('   ✅ Airtel debit successful');
    
    await Future.delayed(Duration(milliseconds: 1350));
    print('   ✅ Zamtel credit successful');
    
    return TransferResult(
      success: true,
      message: 'Airtel to Zamtel transfer completed successfully',
    );
  }

  /// Process Zamtel to Airtel transfer
  static Future<TransferResult> _processZamtelToAirtel(
    TestTransactionRequest request,
    String transactionId,
  ) async {
    print('📱 Processing Zamtel → Airtel transfer...');
    
    await Future.delayed(Duration(milliseconds: 1350));
    print('   ✅ Zamtel debit successful');
    
    await Future.delayed(Duration(milliseconds: 1250));
    print('   ✅ Airtel credit successful');
    
    return TransferResult(
      success: true,
      message: 'Zamtel to Airtel transfer completed successfully',
    );
  }

  /// Process same network transfer
  static Future<TransferResult> _processSameNetwork(
    TestTransactionRequest request,
    String transactionId,
  ) async {
    print('📱 Processing same network transfer...');
    
    await Future.delayed(Duration(milliseconds: 800));
    print('   ✅ Same network transfer successful');
    
    return TransferResult(
      success: true,
      message: 'Same network transfer completed successfully',
    );
  }

  /// Get source provider from transaction type
  static String _getSourceProvider(TransactionType type) {
    switch (type) {
      case TransactionType.mtnToAirtel:
      case TransactionType.mtnToZamtel:
        return 'MTN Mobile Money';
      case TransactionType.airtelToMtn:
      case TransactionType.airtelToZamtel:
        return 'Airtel Money';
      case TransactionType.zamtelToMtn:
      case TransactionType.zamtelToAirtel:
        return 'Zamtel Kwacha';
      case TransactionType.sameNetwork:
        return 'Same Network';
      default:
        return 'Unknown';
    }
  }

  /// Get destination provider from transaction type
  static String _getDestinationProvider(TransactionType type) {
    switch (type) {
      case TransactionType.mtnToAirtel:
      case TransactionType.zamtelToAirtel:
        return 'Airtel Money';
      case TransactionType.airtelToMtn:
      case TransactionType.zamtelToMtn:
        return 'MTN Mobile Money';
      case TransactionType.mtnToZamtel:
      case TransactionType.airtelToZamtel:
        return 'Zamtel Kwacha';
      case TransactionType.sameNetwork:
        return 'Same Network';
      default:
        return 'Unknown';
    }
  }

  /// Validate Zambian phone number
  static bool _isValidZambianPhoneNumber(String phoneNumber) {
    final cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Check various formats
    if (cleaned.startsWith('260') && cleaned.length == 12) {
      final localNumber = cleaned.substring(3);
      return _isValidLocalNumber(localNumber);
    } else if (cleaned.startsWith('0') && cleaned.length == 10) {
      final localNumber = cleaned.substring(1);
      return _isValidLocalNumber(localNumber);
    } else if (cleaned.length == 9) {
      return _isValidLocalNumber(cleaned);
    }
    
    return false;
  }

  /// Check if local number is valid
  static bool _isValidLocalNumber(String localNumber) {
    if (localNumber.length != 9) return false;
    
    final prefix = localNumber.substring(0, 2);
    return ['96', '97', '95'].contains(prefix); // MTN, Airtel, Zamtel
  }

  /// Generate transaction ID
  static String _generateTransactionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(9999);
    return 'TEST${timestamp.toString().substring(8)}$random';
  }

  /// Get transaction history
  static Map<String, TestTransactionResult> getTransactionHistory() {
    return Map.from(_transactionHistory);
  }

  /// Get transaction by ID
  static TestTransactionResult? getTransaction(String transactionId) {
    return _transactionHistory[transactionId];
  }

  /// Get request stream
  static Stream<TestTransactionRequest> get requestStream => _requestController.stream;

  /// Get result stream
  static Stream<TestTransactionResult> get resultStream => _resultController.stream;

  /// Clear transaction history
  static void clearHistory() {
    _transactionHistory.clear();
  }

  /// Dispose resources
  static void dispose() {
    _requestController.close();
    _resultController.close();
  }
}

class TransactionValidation {
  final bool isValid;
  final String? message;

  TransactionValidation({required this.isValid, this.message});
}

class TransferResult {
  final bool success;
  final String? message;

  TransferResult({required this.success, this.message});
}
