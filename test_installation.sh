#!/bin/bash

# TEST INSTALLATION ON ZAMBIAN DEVICE PROFILES
# Comprehensive testing for Pay Mule Zambia APK installation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}📱 TEST INSTALLATION ON ZAMBIAN DEVICE PROFILES 🇿🇲${NC}"
echo -e "${CYAN}==========================================================${NC}"
echo ""

# Default parameters
DEVICES="Tecno Spark 7, Itel P40, Samsung A05s"
ANDROID_VERSIONS="8.0,10,13"
APK_FILE="paymule_production_fixed.apk"
APK_FILE=""
TEST_RESULTS_DIR="test_results_$(date +%Y%m%d_%H%M%S)"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_test() {
    echo -e "${MAGENTA}[TEST]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --android-versions=LIST    Comma-separated Android versions (default: 5.0,7.1,10,13)"
    echo "  --device-models=LIST       Comma-separated device models (default: tecno_spark7,samsung_a10)"
    echo "  --apk=FILE                 APK file to test (required)"
    echo "  --help                     Show this help message"
    echo ""
    echo "Supported Android versions:"
    echo "  5.0, 5.1, 6.0, 7.0, 7.1, 8.0, 8.1, 9, 10, 11, 12, 13, 14"
    echo ""
    echo "Supported device models:"
    echo "  tecno_spark7, samsung_a10, infinix_hot10, oppo_a15, xiaomi_redmi9"
    echo ""
    echo "Example:"
    echo "  $0 --android-versions=\"5.0,7.1,10,13\" --device-models=\"tecno_spark7,samsung_a10\" --apk=paymule_compatible_v1.1.apk"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --android-versions=*)
            ANDROID_VERSIONS="${1#*=}"
            shift
            ;;
        --device-models=*)
            DEVICE_MODELS="${1#*=}"
            shift
            ;;
        --apk=*)
            APK_FILE="${1#*=}"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$APK_FILE" ]]; then
    print_error "APK file is required. Use --apk=FILE"
    show_usage
    exit 1
fi

if [[ ! -f "$APK_FILE" ]]; then
    print_error "APK file not found: $APK_FILE"
    exit 1
fi

# Create test results directory
mkdir -p "$TEST_RESULTS_DIR"

print_header "📱 APK INSTALLATION TESTING"
print_status "APK File: $APK_FILE"
print_status "Android Versions: $ANDROID_VERSIONS"
print_status "Device Models: $DEVICE_MODELS"
print_status "Results Directory: $TEST_RESULTS_DIR"
echo ""

# Function to map Android version to API level
get_api_level() {
    case $1 in
        "5.0") echo "21" ;;
        "5.1") echo "22" ;;
        "6.0") echo "23" ;;
        "7.0") echo "24" ;;
        "7.1") echo "25" ;;
        "8.0") echo "26" ;;
        "8.1") echo "27" ;;
        "9") echo "28" ;;
        "10") echo "29" ;;
        "11") echo "30" ;;
        "12") echo "31" ;;
        "13") echo "33" ;;
        "14") echo "34" ;;
        *) echo "unknown" ;;
    esac
}

# Function to get device specifications
get_device_specs() {
    case $1 in
        "tecno_spark7")
            echo "RAM:4GB CPU:Helio_A22 Screen:6.52_HD+ Density:mdpi"
            ;;
        "samsung_a10")
            echo "RAM:2GB CPU:Exynos_7884 Screen:6.2_HD+ Density:hdpi"
            ;;
        "infinix_hot10")
            echo "RAM:4GB CPU:Helio_G70 Screen:6.78_HD+ Density:hdpi"
            ;;
        "oppo_a15")
            echo "RAM:3GB CPU:Helio_P35 Screen:6.52_HD+ Density:mdpi"
            ;;
        "xiaomi_redmi9")
            echo "RAM:4GB CPU:Helio_G80 Screen:6.53_FHD+ Density:xhdpi"
            ;;
        *)
            echo "RAM:2GB CPU:Generic Screen:5.5_HD+ Density:mdpi"
            ;;
    esac
}

# Function to create emulator AVD
create_emulator() {
    local android_version=$1
    local device_model=$2
    local api_level=$(get_api_level "$android_version")
    local avd_name="test_${device_model}_api${api_level}"
    
    print_status "Creating emulator: $avd_name (Android $android_version)"
    
    # Check if AVD already exists
    if avdmanager list avd | grep -q "$avd_name"; then
        print_status "AVD $avd_name already exists"
        return 0
    fi
    
    # Create AVD based on device model
    case $device_model in
        "tecno_spark7"|"samsung_a10"|"oppo_a15")
            # Low-end device profile
            echo "no" | avdmanager create avd \
                -n "$avd_name" \
                -k "system-images;android-${api_level};google_apis;x86_64" \
                -d "Nexus 5" \
                --force 2>/dev/null || true
            ;;
        "infinix_hot10"|"xiaomi_redmi9")
            # Mid-range device profile
            echo "no" | avdmanager create avd \
                -n "$avd_name" \
                -k "system-images;android-${api_level};google_apis;x86_64" \
                -d "Nexus 6" \
                --force 2>/dev/null || true
            ;;
        *)
            # Generic device profile
            echo "no" | avdmanager create avd \
                -n "$avd_name" \
                -k "system-images;android-${api_level};google_apis;x86_64" \
                -d "Nexus 5" \
                --force 2>/dev/null || true
            ;;
    esac
    
    return 0
}

# Function to start emulator
start_emulator() {
    local android_version=$1
    local device_model=$2
    local api_level=$(get_api_level "$android_version")
    local avd_name="test_${device_model}_api${api_level}"
    local device_specs=$(get_device_specs "$device_model")
    
    print_status "Starting emulator: $avd_name"
    print_status "Device specs: $device_specs"
    
    # Configure emulator based on device model
    local memory_mb="2048"
    local heap_mb="256"
    
    case $device_model in
        "samsung_a10")
            memory_mb="2048"
            heap_mb="256"
            ;;
        "tecno_spark7"|"infinix_hot10"|"xiaomi_redmi9")
            memory_mb="4096"
            heap_mb="512"
            ;;
        "oppo_a15")
            memory_mb="3072"
            heap_mb="384"
            ;;
    esac
    
    # Start emulator in background
    emulator -avd "$avd_name" \
        -memory "$memory_mb" \
        -vmheap "$heap_mb" \
        -no-audio \
        -no-window \
        -gpu off \
        -no-snapshot \
        -wipe-data &
    
    local emulator_pid=$!
    
    # Wait for emulator to boot
    print_status "Waiting for emulator to boot..."
    local timeout=300  # 5 minutes
    local elapsed=0
    
    while [[ $elapsed -lt $timeout ]]; do
        if adb shell getprop sys.boot_completed 2>/dev/null | grep -q "1"; then
            print_success "Emulator booted successfully"
            return 0
        fi
        sleep 5
        elapsed=$((elapsed + 5))
        echo -n "."
    done
    
    print_error "Emulator failed to boot within $timeout seconds"
    kill $emulator_pid 2>/dev/null || true
    return 1
}

# Function to test APK installation
test_apk_installation() {
    local android_version=$1
    local device_model=$2
    local test_name="${device_model}_android${android_version}"
    local result_file="$TEST_RESULTS_DIR/${test_name}_result.txt"
    
    print_test "Testing installation on $device_model (Android $android_version)"
    
    # Record test start
    echo "=== APK Installation Test ===" > "$result_file"
    echo "Device Model: $device_model" >> "$result_file"
    echo "Android Version: $android_version" >> "$result_file"
    echo "APK File: $APK_FILE" >> "$result_file"
    echo "Test Start: $(date)" >> "$result_file"
    echo "" >> "$result_file"
    
    # Test 1: APK Analysis
    print_status "Analyzing APK compatibility..."
    if command -v aapt &> /dev/null; then
        echo "=== APK Analysis ===" >> "$result_file"
        aapt dump badging "$APK_FILE" >> "$result_file" 2>&1 || true
        echo "" >> "$result_file"
    fi
    
    # Test 2: Installation
    print_status "Installing APK..."
    echo "=== Installation Test ===" >> "$result_file"
    
    if adb install -r -g -t "$APK_FILE" >> "$result_file" 2>&1; then
        print_success "✅ Installation successful"
        echo "RESULT: INSTALLATION SUCCESS" >> "$result_file"
        
        # Test 3: Launch test
        print_status "Testing app launch..."
        local package_name=$(aapt dump badging "$APK_FILE" 2>/dev/null | grep "package:" | sed "s/.*name='\([^']*\)'.*/\1/" || echo "com.zm.paymule")
        
        echo "=== Launch Test ===" >> "$result_file"
        if adb shell monkey -p "$package_name" -c android.intent.category.LAUNCHER 1 >> "$result_file" 2>&1; then
            print_success "✅ App launch successful"
            echo "RESULT: LAUNCH SUCCESS" >> "$result_file"
        else
            print_warning "⚠️  App launch failed"
            echo "RESULT: LAUNCH FAILED" >> "$result_file"
        fi
        
        # Test 4: Uninstall
        print_status "Cleaning up..."
        adb uninstall "$package_name" >> "$result_file" 2>&1 || true
        
    else
        print_error "❌ Installation failed"
        echo "RESULT: INSTALLATION FAILED" >> "$result_file"
    fi
    
    echo "Test End: $(date)" >> "$result_file"
    echo "" >> "$result_file"
}

# Main testing loop
print_header "🚀 STARTING INSTALLATION TESTS"

# Convert comma-separated lists to arrays
IFS=',' read -ra VERSIONS <<< "$ANDROID_VERSIONS"
IFS=',' read -ra MODELS <<< "$DEVICE_MODELS"

total_tests=$((${#VERSIONS[@]} * ${#MODELS[@]}))
current_test=0

for android_version in "${VERSIONS[@]}"; do
    for device_model in "${MODELS[@]}"; do
        current_test=$((current_test + 1))
        
        print_header "TEST $current_test/$total_tests: $device_model (Android $android_version)"
        
        # Check if we can create/start emulator
        if command -v avdmanager &> /dev/null && command -v emulator &> /dev/null; then
            # Create and start emulator
            if create_emulator "$android_version" "$device_model"; then
                if start_emulator "$android_version" "$device_model"; then
                    # Run installation test
                    test_apk_installation "$android_version" "$device_model"
                    
                    # Stop emulator
                    adb emu kill 2>/dev/null || true
                    sleep 5
                else
                    print_error "Failed to start emulator for $device_model (Android $android_version)"
                fi
            else
                print_error "Failed to create emulator for $device_model (Android $android_version)"
            fi
        else
            print_warning "Android SDK tools not found. Skipping emulator test for $device_model (Android $android_version)"
            
            # Create a placeholder result
            local test_name="${device_model}_android${android_version}"
            local result_file="$TEST_RESULTS_DIR/${test_name}_result.txt"
            echo "RESULT: SKIPPED - Android SDK tools not available" > "$result_file"
        fi
        
        echo ""
    done
done

# Generate summary report
print_header "📊 TEST SUMMARY"
summary_file="$TEST_RESULTS_DIR/test_summary.txt"

echo "=== APK Installation Test Summary ===" > "$summary_file"
echo "APK File: $APK_FILE" >> "$summary_file"
echo "Test Date: $(date)" >> "$summary_file"
echo "Total Tests: $total_tests" >> "$summary_file"
echo "" >> "$summary_file"

successful_tests=0
failed_tests=0
skipped_tests=0

for result_file in "$TEST_RESULTS_DIR"/*_result.txt; do
    if [[ -f "$result_file" ]]; then
        test_name=$(basename "$result_file" "_result.txt")
        
        if grep -q "INSTALLATION SUCCESS" "$result_file"; then
            echo "✅ $test_name: PASSED" >> "$summary_file"
            successful_tests=$((successful_tests + 1))
        elif grep -q "INSTALLATION FAILED" "$result_file"; then
            echo "❌ $test_name: FAILED" >> "$summary_file"
            failed_tests=$((failed_tests + 1))
        else
            echo "⏭️  $test_name: SKIPPED" >> "$summary_file"
            skipped_tests=$((skipped_tests + 1))
        fi
    fi
done

echo "" >> "$summary_file"
echo "Results:" >> "$summary_file"
echo "  Successful: $successful_tests" >> "$summary_file"
echo "  Failed: $failed_tests" >> "$summary_file"
echo "  Skipped: $skipped_tests" >> "$summary_file"

# Display summary
cat "$summary_file"

print_success "🎉 Testing completed!"
print_status "📁 Detailed results saved in: $TEST_RESULTS_DIR"
print_status "📊 Summary report: $summary_file"

if [[ $failed_tests -gt 0 ]]; then
    print_warning "⚠️  $failed_tests test(s) failed. Check individual result files for details."
    exit 1
else
    print_success "✅ All tests passed or were skipped!"
    exit 0
fi
