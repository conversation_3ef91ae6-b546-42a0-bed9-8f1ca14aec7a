/// Anti-Fraud Protection Service for Zambian QR Payments
/// Comprehensive fraud detection and prevention system
/// Implements BoZ compliance and security best practices

import 'dart:convert';
import 'package:logger/logger.dart';
import 'package:crypto/crypto.dart';

import '../../core/constants/app_constants.dart';
import '../../core/security/encryption_service.dart';
import '../../data/database/database_helper.dart';
import '../zambia_qr_format.dart';
import 'merchant_registry.dart';

/// Fraud risk levels
enum FraudRiskLevel {
  LOW,
  MEDIUM,
  HIGH,
  CRITICAL,
}

/// Anti-Fraud Protection Service
class AntiFraudService {
  static final AntiFraudService _instance = AntiFraudService._internal();
  factory AntiFraudService() => _instance;
  AntiFraudService._internal();

  static final Logger _logger = Logger();
  static final DatabaseHelper _dbHelper = DatabaseHelper();
  static final EncryptionService _encryption = EncryptionService();

  static bool _isInitialized = false;

  /// Initialize anti-fraud service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _encryption.initialize();
      await MerchantRegistry.initialize();
      await _createFraudTables();
      
      _isInitialized = true;
      _logger.i('🛡️ Anti-fraud service initialized');
    } catch (e) {
      _logger.e('Failed to initialize anti-fraud service: $e');
      rethrow;
    }
  }

  /// Validate QR code for fraud protection
  static Future<QRValidationResult> validateQR(ZambiaQRData data) async {
    try {
      if (!_isInitialized) await initialize();

      _logger.d('🔍 Validating QR for fraud protection');
      _logger.d('   Merchant: ${data.merchantId.substring(0, 8)}...');
      _logger.d('   Amount: K${data.amount}');
      _logger.d('   Timestamp: ${data.timestamp}');

      // 1. Validate merchant registration
      final merchantValidation = await _validateMerchant(data.merchantId);
      if (!merchantValidation.isValid) {
        await _logFraudAttempt(
          type: 'UNREGISTERED_MERCHANT',
          data: data,
          details: {'merchant_id': data.merchantId},
        );
        
        throw FraudException('Unregistered merchant: ${merchantValidation.message}');
      }

      // 2. Validate QR expiry
      final expiryValidation = _validateQRExpiry(data);
      if (!expiryValidation.isValid) {
        await _logFraudAttempt(
          type: 'EXPIRED_QR',
          data: data,
          details: {
            'qr_timestamp': data.timestamp?.millisecondsSinceEpoch,
            'current_timestamp': DateTime.now().millisecondsSinceEpoch,
          },
        );
        
        throw FraudException('Expired QR code: ${expiryValidation.message}');
      }

      // 3. Validate QR replay attacks
      final replayValidation = await _validateQRReplay(data);
      if (!replayValidation.isValid) {
        await _logFraudAttempt(
          type: 'QR_REPLAY_ATTACK',
          data: data,
          details: {'usage_count': replayValidation.usageCount},
        );
        
        throw FraudException('QR replay attack detected: ${replayValidation.message}');
      }

      // 4. Validate amount manipulation
      final amountValidation = await _validateAmountManipulation(data);
      if (!amountValidation.isValid) {
        await _logFraudAttempt(
          type: 'AMOUNT_MANIPULATION',
          data: data,
          details: {'suspicious_amount': data.amount},
        );
        
        throw FraudException('Amount manipulation detected: ${amountValidation.message}');
      }

      // 5. Check merchant risk score
      final riskValidation = await _validateMerchantRisk(data.merchantId);
      if (!riskValidation.isValid) {
        await _logFraudAttempt(
          type: 'HIGH_RISK_MERCHANT',
          data: data,
          details: {'risk_score': riskValidation.riskScore},
        );
        
        throw FraudException('High-risk merchant: ${riskValidation.message}');
      }

      // 6. Validate transaction patterns
      final patternValidation = await _validateTransactionPatterns(data);
      if (!patternValidation.isValid) {
        await _logFraudAttempt(
          type: 'SUSPICIOUS_PATTERN',
          data: data,
          details: {'pattern_type': patternValidation.patternType},
        );
        
        if (patternValidation.riskLevel == FraudRiskLevel.CRITICAL) {
          throw FraudException('Critical fraud pattern detected: ${patternValidation.message}');
        }
      }

      _logger.i('✅ QR validation passed fraud checks');
      
      return QRValidationResult(
        isValid: true,
        message: 'QR validation successful',
        riskLevel: patternValidation.riskLevel,
        riskScore: patternValidation.riskScore,
      );

    } catch (e) {
      if (e is FraudException) {
        _logger.e('🚫 Fraud detected: ${e.message}');
        rethrow;
      } else {
        _logger.e('QR validation failed: $e');
        throw FraudException('QR validation failed: ${e.toString()}');
      }
    }
  }

  /// Validate merchant registration
  static Future<MerchantValidationResult> _validateMerchant(String merchantId) async {
    try {
      final isValid = await MerchantRegistry.isValid(merchantId);
      
      if (!isValid) {
        return MerchantValidationResult(
          isValid: false,
          message: 'Merchant not registered or suspended',
        );
      }

      final merchantInfo = await MerchantRegistry.getMerchantInfo(merchantId);
      if (merchantInfo == null) {
        return MerchantValidationResult(
          isValid: false,
          message: 'Merchant information not found',
        );
      }

      return MerchantValidationResult(
        isValid: true,
        message: 'Merchant is valid and verified',
        merchantInfo: merchantInfo,
      );
    } catch (e) {
      return MerchantValidationResult(
        isValid: false,
        message: 'Merchant validation failed: ${e.toString()}',
      );
    }
  }

  /// Validate QR expiry
  static QRExpiryValidationResult _validateQRExpiry(ZambiaQRData data) {
    if (data.timestamp == null) {
      return QRExpiryValidationResult(
        isValid: false,
        message: 'QR timestamp missing',
      );
    }

    final now = DateTime.now();
    final qrAge = now.difference(data.timestamp!);

    // Check if QR is too old (default: 10 minutes)
    if (qrAge.inMinutes > AppConstants.qrExpiryMinutes) {
      return QRExpiryValidationResult(
        isValid: false,
        message: 'QR code expired ${qrAge.inMinutes} minutes ago',
        ageMinutes: qrAge.inMinutes,
      );
    }

    // Check if QR is from the future (clock skew protection)
    if (qrAge.inMinutes < -2) {
      return QRExpiryValidationResult(
        isValid: false,
        message: 'QR code timestamp is in the future',
        ageMinutes: qrAge.inMinutes,
      );
    }

    return QRExpiryValidationResult(
      isValid: true,
      message: 'QR timestamp is valid',
      ageMinutes: qrAge.inMinutes,
    );
  }

  /// Validate QR replay attacks
  static Future<QRReplayValidationResult> _validateQRReplay(ZambiaQRData data) async {
    try {
      // Create QR hash for tracking
      final qrHash = _createQRHash(data);
      
      // Check usage count
      final usageCount = await _getQRUsageCount(qrHash);
      
      // Allow multiple uses for open amount QRs (amount = 0)
      final maxUsage = data.amount == 0 ? 100 : 3;
      
      if (usageCount >= maxUsage) {
        return QRReplayValidationResult(
          isValid: false,
          message: 'QR code used too many times',
          usageCount: usageCount,
        );
      }

      // Record this usage
      await _recordQRUsage(qrHash, data);

      return QRReplayValidationResult(
        isValid: true,
        message: 'QR usage is within limits',
        usageCount: usageCount + 1,
      );
    } catch (e) {
      return QRReplayValidationResult(
        isValid: false,
        message: 'QR replay validation failed: ${e.toString()}',
        usageCount: 0,
      );
    }
  }

  /// Validate amount manipulation
  static Future<AmountValidationResult> _validateAmountManipulation(ZambiaQRData data) async {
    try {
      // Check for suspicious round numbers that might indicate manipulation
      if (data.amount > 0) {
        // Very round numbers above certain thresholds are suspicious
        if (data.amount >= 1000 && data.amount % 1000 == 0) {
          return AmountValidationResult(
            isValid: false,
            message: 'Suspicious round amount detected',
          );
        }

        // Check for amounts with unusual precision
        final decimalPlaces = _getDecimalPlaces(data.amount);
        if (decimalPlaces > 2) {
          return AmountValidationResult(
            isValid: false,
            message: 'Unusual amount precision detected',
          );
        }
      }

      return AmountValidationResult(
        isValid: true,
        message: 'Amount appears legitimate',
      );
    } catch (e) {
      return AmountValidationResult(
        isValid: false,
        message: 'Amount validation failed: ${e.toString()}',
      );
    }
  }

  /// Validate merchant risk score
  static Future<RiskValidationResult> _validateMerchantRisk(String merchantId) async {
    try {
      final merchantInfo = await MerchantRegistry.getMerchantInfo(merchantId);
      if (merchantInfo == null) {
        return RiskValidationResult(
          isValid: false,
          message: 'Merchant not found',
          riskScore: 1.0,
        );
      }

      // High risk threshold
      if (merchantInfo.riskScore > 0.8) {
        return RiskValidationResult(
          isValid: false,
          message: 'Merchant has high risk score',
          riskScore: merchantInfo.riskScore,
        );
      }

      return RiskValidationResult(
        isValid: true,
        message: 'Merchant risk score is acceptable',
        riskScore: merchantInfo.riskScore,
      );
    } catch (e) {
      return RiskValidationResult(
        isValid: false,
        message: 'Risk validation failed: ${e.toString()}',
        riskScore: 1.0,
      );
    }
  }

  /// Validate transaction patterns
  static Future<PatternValidationResult> _validateTransactionPatterns(ZambiaQRData data) async {
    try {
      double riskScore = 0.0;
      String? patternType;
      FraudRiskLevel riskLevel = FraudRiskLevel.LOW;

      // Check for rapid-fire transactions
      final recentTransactions = await _getRecentTransactions(data.merchantId, 5);
      if (recentTransactions.length > 10) {
        riskScore += 0.3;
        patternType = 'RAPID_TRANSACTIONS';
      }

      // Check for unusual time patterns
      final hour = DateTime.now().hour;
      if (hour < 5 || hour > 23) {
        riskScore += 0.2;
        patternType = 'UNUSUAL_TIME';
      }

      // Check for amount patterns
      if (data.amount > 0) {
        final avgAmount = await _getAverageTransactionAmount(data.merchantId);
        if (avgAmount > 0 && data.amount > avgAmount * 10) {
          riskScore += 0.4;
          patternType = 'UNUSUAL_AMOUNT';
        }
      }

      // Determine risk level
      if (riskScore >= 0.8) {
        riskLevel = FraudRiskLevel.CRITICAL;
      } else if (riskScore >= 0.6) {
        riskLevel = FraudRiskLevel.HIGH;
      } else if (riskScore >= 0.3) {
        riskLevel = FraudRiskLevel.MEDIUM;
      }

      return PatternValidationResult(
        isValid: riskLevel != FraudRiskLevel.CRITICAL,
        message: riskLevel == FraudRiskLevel.CRITICAL 
            ? 'Critical fraud pattern detected'
            : 'Transaction pattern analyzed',
        riskLevel: riskLevel,
        riskScore: riskScore,
        patternType: patternType,
      );
    } catch (e) {
      return PatternValidationResult(
        isValid: false,
        message: 'Pattern validation failed: ${e.toString()}',
        riskLevel: FraudRiskLevel.HIGH,
        riskScore: 0.8,
      );
    }
  }

  /// Create QR hash for tracking
  static String _createQRHash(ZambiaQRData data) {
    final content = '${data.merchantId}:${data.amount}:${data.timestamp?.millisecondsSinceEpoch}';
    return sha256.convert(utf8.encode(content)).toString();
  }

  /// Get QR usage count
  static Future<int> _getQRUsageCount(String qrHash) async {
    try {
      final results = await _dbHelper.query(
        'qr_usage_tracking',
        where: 'qr_hash = ?',
        whereArgs: [qrHash],
        limit: 1,
      );

      if (results.isNotEmpty) {
        return results.first['usage_count'] as int;
      }
      return 0;
    } catch (e) {
      _logger.e('Failed to get QR usage count: $e');
      return 0;
    }
  }

  /// Record QR usage
  static Future<void> _recordQRUsage(String qrHash, ZambiaQRData data) async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch;
      
      // Try to update existing record
      final updated = await _dbHelper.rawUpdate(
        'UPDATE qr_usage_tracking SET usage_count = usage_count + 1, last_used_at = ? WHERE qr_hash = ?',
        [now, qrHash],
      );

      // If no existing record, create new one
      if (updated == 0) {
        await _dbHelper.insert('qr_usage_tracking', {
          'qr_hash': qrHash,
          'merchant_id': data.merchantId,
          'amount': data.amount,
          'usage_count': 1,
          'first_used_at': now,
          'last_used_at': now,
        });
      }
    } catch (e) {
      _logger.e('Failed to record QR usage: $e');
    }
  }

  /// Log fraud attempt
  static Future<void> _logFraudAttempt({
    required String type,
    required ZambiaQRData data,
    Map<String, dynamic>? details,
  }) async {
    try {
      final fraudLog = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'fraud_type': type,
        'merchant_id': data.merchantId,
        'amount': data.amount,
        'qr_timestamp': data.timestamp?.millisecondsSinceEpoch,
        'detected_at': DateTime.now().millisecondsSinceEpoch,
        'details': details != null ? jsonEncode(details) : null,
      };

      await _dbHelper.insert('fraud_attempts', fraudLog);
      
      _logger.w('🚨 Fraud attempt logged: $type');
    } catch (e) {
      _logger.e('Failed to log fraud attempt: $e');
    }
  }

  /// Get recent transactions
  static Future<List<Map<String, dynamic>>> _getRecentTransactions(String merchantId, int minutes) async {
    try {
      final since = DateTime.now().subtract(Duration(minutes: minutes)).millisecondsSinceEpoch;
      
      return await _dbHelper.query(
        'qr_usage_tracking',
        where: 'merchant_id = ? AND last_used_at >= ?',
        whereArgs: [merchantId, since],
      );
    } catch (e) {
      _logger.e('Failed to get recent transactions: $e');
      return [];
    }
  }

  /// Get average transaction amount
  static Future<double> _getAverageTransactionAmount(String merchantId) async {
    try {
      final results = await _dbHelper.rawQuery(
        'SELECT AVG(amount) as avg_amount FROM qr_usage_tracking WHERE merchant_id = ? AND amount > 0',
        [merchantId],
      );

      if (results.isNotEmpty && results.first['avg_amount'] != null) {
        return (results.first['avg_amount'] as num).toDouble();
      }
      return 0.0;
    } catch (e) {
      _logger.e('Failed to get average transaction amount: $e');
      return 0.0;
    }
  }

  /// Get decimal places in amount
  static int _getDecimalPlaces(double value) {
    final str = value.toString();
    if (!str.contains('.')) return 0;
    return str.split('.')[1].length;
  }

  /// Create fraud detection tables
  static Future<void> _createFraudTables() async {
    final db = await _dbHelper.database;

    // QR usage tracking table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS qr_usage_tracking (
        qr_hash TEXT PRIMARY KEY,
        merchant_id TEXT NOT NULL,
        amount REAL NOT NULL,
        usage_count INTEGER DEFAULT 1,
        first_used_at INTEGER NOT NULL,
        last_used_at INTEGER NOT NULL
      )
    ''');

    // Fraud attempts log table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS fraud_attempts (
        id TEXT PRIMARY KEY,
        fraud_type TEXT NOT NULL,
        merchant_id TEXT NOT NULL,
        amount REAL,
        qr_timestamp INTEGER,
        detected_at INTEGER NOT NULL,
        details TEXT
      )
    ''');

    // Create indexes
    await db.execute('CREATE INDEX IF NOT EXISTS idx_qr_usage_merchant ON qr_usage_tracking(merchant_id)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_fraud_type ON fraud_attempts(fraud_type)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_fraud_merchant ON fraud_attempts(merchant_id)');
  }
}

/// Fraud exception
class FraudException implements Exception {
  final String message;
  FraudException(this.message);
  
  @override
  String toString() => 'FraudException: $message';
}

/// QR validation result
class QRValidationResult {
  final bool isValid;
  final String message;
  final FraudRiskLevel riskLevel;
  final double riskScore;

  QRValidationResult({
    required this.isValid,
    required this.message,
    this.riskLevel = FraudRiskLevel.LOW,
    this.riskScore = 0.0,
  });
}

/// Merchant validation result
class MerchantValidationResult {
  final bool isValid;
  final String message;
  final MerchantRecord? merchantInfo;

  MerchantValidationResult({
    required this.isValid,
    required this.message,
    this.merchantInfo,
  });
}

/// QR expiry validation result
class QRExpiryValidationResult {
  final bool isValid;
  final String message;
  final int? ageMinutes;

  QRExpiryValidationResult({
    required this.isValid,
    required this.message,
    this.ageMinutes,
  });
}

/// QR replay validation result
class QRReplayValidationResult {
  final bool isValid;
  final String message;
  final int usageCount;

  QRReplayValidationResult({
    required this.isValid,
    required this.message,
    required this.usageCount,
  });
}

/// Amount validation result
class AmountValidationResult {
  final bool isValid;
  final String message;

  AmountValidationResult({
    required this.isValid,
    required this.message,
  });
}

/// Risk validation result
class RiskValidationResult {
  final bool isValid;
  final String message;
  final double riskScore;

  RiskValidationResult({
    required this.isValid,
    required this.message,
    required this.riskScore,
  });
}

/// Pattern validation result
class PatternValidationResult {
  final bool isValid;
  final String message;
  final FraudRiskLevel riskLevel;
  final double riskScore;
  final String? patternType;

  PatternValidationResult({
    required this.isValid,
    required this.message,
    required this.riskLevel,
    required this.riskScore,
    this.patternType,
  });
}
