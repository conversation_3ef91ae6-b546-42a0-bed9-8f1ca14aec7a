# release.yml
# PAY MULE ZAMBIA - PRODUCTION RELEASE CONFIGURATION
# Comprehensive settings for Zambian market deployment

# ============================================================================
# PRODUCTION SETTINGS
# ============================================================================
production_settings:
  # Android SDK Configuration
  min_sdk: 21                 # Android 5.0 (Lollipop) - 95% Zambian device coverage
  target_sdk: 33              # Android 13 - Optimal compatibility
  compile_sdk: 34             # Android 14 - Latest build tools
  
  # Application Configuration
  app_id: "com.zambiapay.zambia_pay"
  version_name: "1.0.0"
  version_code: 1
  
  # Signing Configuration
  signing:
    v1_signing: true          # Critical for older devices (Android 6.0 and below)
    v2_signing: true          # Modern signing for Android 7.0+
    keystore_path: "android/app/keystore/zm_release_key.jks"
    key_alias: "zm_release_key"
    store_password: "zambiapay2024"
    key_password: "zambiapay2024"
  
  # Zambia-Specific Optimizations
  zambia_specific:
    # Architecture Filters (optimized for Zambian devices)
    abi_filters: [armeabi-v7a, arm64-v8a]  # ARM only (covers 99% of Zambian devices)
    
    # Production Data Cleanup
    remove_test_ids: true     # Remove all test account IDs
    remove_demo_data: true    # Remove demo transaction data
    remove_debug_logs: true   # Remove debug logging statements
    
    # Mobile Money Endpoints
    real_momo_endpoints: true # Use production mobile money endpoints
    mtn_production: true      # MTN Mobile Money production API
    airtel_production: true   # Airtel Money production API
    zamtel_production: true   # Zamtel Kwacha production API
    
    # Network Optimizations
    network_optimization:
      enable_2g_support: true # Optimize for 2G networks
      enable_3g_support: true # Optimize for 3G networks
      enable_4g_support: true # Optimize for 4G networks
      low_bandwidth_mode: true # Enable low bandwidth optimizations
      offline_mode: true      # Enable offline transaction queuing
    
    # Device-Specific Optimizations
    device_optimizations:
      tecno_optimization: true    # Tecno Spark series optimizations
      itel_optimization: true     # Itel P series optimizations
      samsung_optimization: true  # Samsung Galaxy A series optimizations
      android_go_support: true    # Android Go Edition support
      low_memory_mode: true       # Low memory device optimizations

# ============================================================================
# BUILD CONFIGURATION
# ============================================================================
build_configuration:
  # Build Type
  build_type: "release"
  
  # Optimization Settings
  minify_enabled: false       # Disabled to prevent ZIP corruption
  shrink_resources: false     # Disabled for stability
  proguard_enabled: false     # Disabled for compatibility
  
  # Multi-APK Configuration
  generate_separate_apks: false # Single universal APK for simplicity
  
  # Native Libraries
  native_libraries:
    include_arm32: true       # ARMv7 (32-bit) for older devices
    include_arm64: true       # ARMv8 (64-bit) for newer devices
    exclude_x86: true         # Exclude x86 to reduce APK size
    exclude_x86_64: true      # Exclude x86_64 to reduce APK size
  
  # Resources Optimization
  resources:
    remove_unused: true       # Remove unused resources
    compress_images: true     # Compress image assets
    optimize_vectors: true    # Optimize vector drawables
    
  # Localization
  localization:
    primary_language: "en"    # English (primary)
    secondary_languages: ["sw"] # Swahili (optional)
    remove_unused_locales: true # Remove unused language resources

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================
security_configuration:
  # Code Protection
  obfuscation:
    enabled: false            # Disabled for debugging compatibility
    keep_debug_info: true     # Keep debug info for crash reports
  
  # API Security
  api_security:
    use_https_only: true      # Force HTTPS for all API calls
    certificate_pinning: true # Enable SSL certificate pinning
    api_key_encryption: true  # Encrypt API keys
  
  # Data Protection
  data_protection:
    encrypt_local_storage: true    # Encrypt local data storage
    secure_preferences: true       # Use encrypted shared preferences
    biometric_authentication: true # Enable biometric auth where available
  
  # Network Security
  network_security:
    disable_http: true        # Disable HTTP connections
    enable_network_security_config: true # Use network security config

# ============================================================================
# MOBILE MONEY CONFIGURATION
# ============================================================================
mobile_money_configuration:
  # MTN Mobile Money
  mtn:
    environment: "production"
    api_base_url: "https://api.mtn.com/v1"
    subscription_key: "${MTN_SUBSCRIPTION_KEY}"
    callback_url: "https://paymule.zambiapay.com/mtn/callback"
    timeout_seconds: 30
    retry_attempts: 3
  
  # Airtel Money
  airtel:
    environment: "production"
    api_base_url: "https://api.airtel.africa/v1"
    client_id: "${AIRTEL_CLIENT_ID}"
    client_secret: "${AIRTEL_CLIENT_SECRET}"
    callback_url: "https://paymule.zambiapay.com/airtel/callback"
    timeout_seconds: 30
    retry_attempts: 3
  
  # Zamtel Kwacha
  zamtel:
    environment: "production"
    api_base_url: "https://api.zamtel.zm/kwacha/v1"
    merchant_id: "${ZAMTEL_MERCHANT_ID}"
    api_key: "${ZAMTEL_API_KEY}"
    callback_url: "https://paymule.zambiapay.com/zamtel/callback"
    timeout_seconds: 30
    retry_attempts: 3

# ============================================================================
# DEVICE COMPATIBILITY
# ============================================================================
device_compatibility:
  # Minimum Requirements
  minimum_requirements:
    android_version: "5.0"    # Android 5.0 (API 21)
    ram_mb: 1024             # 1GB RAM minimum
    storage_mb: 64           # 64MB free storage minimum
    
  # Supported Devices (Zambian Market)
  supported_devices:
    tecno_devices:
      - "Tecno Spark 7"
      - "Tecno Spark 8"
      - "Tecno Spark 9"
      - "Tecno Camon 17"
      - "Tecno Camon 18"
    
    itel_devices:
      - "Itel P40"
      - "Itel P55"
      - "Itel A56 Pro"
      - "Itel A48"
    
    samsung_devices:
      - "Samsung Galaxy A05s"
      - "Samsung Galaxy A10"
      - "Samsung Galaxy A20"
      - "Samsung Galaxy A30"
    
    infinix_devices:
      - "Infinix Hot 10"
      - "Infinix Hot 11"
      - "Infinix Note 8"
      - "Infinix Smart 5"
  
  # Screen Compatibility
  screen_compatibility:
    min_screen_size: "small"  # Support all screen sizes
    densities: [120, 160, 240, 320, 480, 640] # All density buckets
    orientations: ["portrait", "landscape"] # Both orientations

# ============================================================================
# PERFORMANCE OPTIMIZATION
# ============================================================================
performance_optimization:
  # Memory Management
  memory:
    enable_large_heap: false  # Keep memory usage low
    max_heap_size: "256m"     # Maximum heap size
    enable_multidex: true     # Enable multidex for large apps
  
  # Startup Optimization
  startup:
    lazy_loading: true        # Enable lazy loading of components
    preload_critical: true    # Preload critical components
    background_init: true     # Initialize non-critical components in background
  
  # Network Performance
  network:
    connection_pooling: true  # Enable HTTP connection pooling
    request_caching: true     # Enable request caching
    compression: true         # Enable response compression
    
  # Database Optimization
  database:
    enable_wal_mode: true     # Enable WAL mode for SQLite
    vacuum_on_startup: false # Disable vacuum on startup for faster boot
    index_optimization: true  # Optimize database indexes

# ============================================================================
# TESTING CONFIGURATION
# ============================================================================
testing_configuration:
  # Unit Testing
  unit_tests:
    enabled: true
    coverage_threshold: 80    # Minimum 80% code coverage
    
  # Integration Testing
  integration_tests:
    enabled: true
    test_mobile_money: true   # Test mobile money integrations
    test_offline_mode: true   # Test offline functionality
    
  # Device Testing
  device_testing:
    test_devices:
      - "Tecno Spark 7 (Android 8.0)"
      - "Itel P40 (Android 10)"
      - "Samsung A05s (Android 13)"
    
    test_scenarios:
      - "Fresh installation"
      - "App upgrade"
      - "Low memory conditions"
      - "Poor network conditions"
      - "Offline usage"

# ============================================================================
# DEPLOYMENT CONFIGURATION
# ============================================================================
deployment_configuration:
  # Release Channels
  release_channels:
    primary: "direct_apk"     # Direct APK distribution
    secondary: "google_play"  # Google Play Store (future)
  
  # Distribution
  distribution:
    apk_name_format: "paymule_zambia_v{version}_{timestamp}.apk"
    generate_checksums: true  # Generate MD5/SHA256 checksums
    create_backup: true       # Create backup of previous version
  
  # Rollout Strategy
  rollout:
    initial_percentage: 10    # Start with 10% of users
    increment_percentage: 25  # Increase by 25% each phase
    monitoring_period_hours: 24 # Monitor for 24 hours between phases
  
  # Monitoring
  monitoring:
    crash_reporting: true     # Enable crash reporting
    analytics: true           # Enable usage analytics
    performance_monitoring: true # Enable performance monitoring

# ============================================================================
# COMPLIANCE & REGULATORY
# ============================================================================
compliance:
  # Zambian Regulations
  zambian_compliance:
    bank_of_zambia_approved: true    # BoZ approval required
    zicta_compliance: true           # ZICTA telecommunications compliance
    data_protection_act: true       # Zambian Data Protection Act compliance
  
  # International Standards
  international_standards:
    iso27001_compliance: true       # Information security management
    pci_dss_compliance: true        # Payment card industry compliance
    gdpr_compliance: true           # General Data Protection Regulation
  
  # Privacy
  privacy:
    data_minimization: true         # Collect only necessary data
    user_consent: true              # Explicit user consent required
    data_retention_days: 2555       # 7 years data retention (regulatory requirement)

# ============================================================================
# METADATA
# ============================================================================
metadata:
  release_name: "Pay Mule Zambia Production v1.0"
  release_date: "2025-08-02"
  target_market: "Zambia"
  supported_languages: ["en", "sw"]
  maintainer: "Pay Mule Development Team"
  support_email: "<EMAIL>"
  documentation_url: "https://docs.paymule.zambiapay.com"
  
  # Build Information
  build_info:
    build_system: "Flutter/Gradle"
    flutter_version: "3.x"
    gradle_version: "8.x"
    android_gradle_plugin: "8.x"
    build_tools_version: "34.0.0"
