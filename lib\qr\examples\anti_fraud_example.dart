/// Anti-Fraud Protection Example for Zambian QR Payments
/// Demonstrates comprehensive fraud detection and prevention
/// Shows merchant registry, QR validation, and fraud monitoring

import 'package:flutter/material.dart';

import '../zambia_qr.dart';
import '../zambia_qr_format.dart';
import '../security/anti_fraud_service.dart';
import '../security/merchant_registry.dart';

/// Anti-fraud protection examples
class AntiFraudExample {
  /// Example 1: Merchant registration and verification
  static Future<void> merchantRegistrationExample() async {
    print('🏢 === MERCHANT REGISTRATION EXAMPLE ===');

    try {
      // Initialize services
      await MerchantRegistry.initialize();

      print('📝 Registering merchants...');

      // Register legitimate merchant
      final legitimateResult = await MerchantRegistry.registerMerchant(
        merchantId: 'merchant_001',
        businessName: 'Lusaka Fresh Market',
        phoneNumber: '+260961234567',
        category: 'RETAIL',
        location: 'Lusaka Central Market, Stall 15',
        description: 'Fresh fruits and vegetables',
        businessDetails: {
          'license_number': 'LCC/2024/001',
          'operating_hours': '06:00-18:00',
          'owner_name': '<PERSON>',
        },
        verificationDocuments: ['business_license.pdf', 'id_copy.pdf'],
      );

      if (legitimateResult.success) {
        print('✅ Legitimate merchant registered: ${legitimateResult.merchantId}');
        
        // Verify the merchant (admin action)
        final verified = await MerchantRegistry.verifyMerchant(
          merchantId: legitimateResult.merchantId!,
          verificationLevel: 2,
          verificationNotes: 'Verified through physical inspection and document review',
        );
        
        if (verified) {
          print('✅ Merchant verified successfully');
        }
      } else {
        print('❌ Merchant registration failed: ${legitimateResult.message}');
      }

      // Try to register duplicate merchant
      print('\n🔄 Testing duplicate registration...');
      final duplicateResult = await MerchantRegistry.registerMerchant(
        merchantId: 'merchant_002',
        businessName: 'Another Business',
        phoneNumber: '+260961234567', // Same phone number
        category: 'SERVICES',
        location: 'Different location',
      );

      if (!duplicateResult.success) {
        print('✅ Duplicate phone number correctly rejected: ${duplicateResult.message}');
      }

    } catch (e) {
      print('❌ Merchant registration example failed: $e');
    }
  }

  /// Example 2: QR validation with fraud detection
  static Future<void> qrValidationExample() async {
    print('\n🛡️ === QR VALIDATION EXAMPLE ===');

    try {
      // Initialize services
      await AntiFraudService.initialize();

      print('🔍 Testing various QR scenarios...');

      // Test 1: Valid QR from registered merchant
      print('\n1️⃣ Testing valid QR...');
      final validQR = ZambiaQRData(
        merchantId: 'merchant_001',
        amount: 25.0,
        currency: 'ZMW',
        provider: 'MTN',
        description: 'Fresh vegetables',
        timestamp: DateTime.now().subtract(const Duration(minutes: 2)),
      );

      try {
        final validResult = await AntiFraudService.validateQR(validQR);
        print('✅ Valid QR passed: ${validResult.message}');
        print('   Risk Level: ${validResult.riskLevel.name}');
        print('   Risk Score: ${(validResult.riskScore * 100).toStringAsFixed(1)}%');
      } catch (e) {
        print('❌ Valid QR failed: $e');
      }

      // Test 2: Unregistered merchant
      print('\n2️⃣ Testing unregistered merchant...');
      final unregisteredQR = ZambiaQRData(
        merchantId: 'fake_merchant_999',
        amount: 100.0,
        currency: 'ZMW',
        provider: 'MTN',
        description: 'Suspicious transaction',
        timestamp: DateTime.now(),
      );

      try {
        await AntiFraudService.validateQR(unregisteredQR);
        print('❌ Unregistered merchant should have been rejected');
      } catch (e) {
        print('✅ Unregistered merchant correctly rejected: $e');
      }

      // Test 3: Expired QR
      print('\n3️⃣ Testing expired QR...');
      final expiredQR = ZambiaQRData(
        merchantId: 'merchant_001',
        amount: 50.0,
        currency: 'ZMW',
        provider: 'MTN',
        description: 'Old transaction',
        timestamp: DateTime.now().subtract(const Duration(minutes: 15)), // 15 minutes old
      );

      try {
        await AntiFraudService.validateQR(expiredQR);
        print('❌ Expired QR should have been rejected');
      } catch (e) {
        print('✅ Expired QR correctly rejected: $e');
      }

      // Test 4: Suspicious amount
      print('\n4️⃣ Testing suspicious amount...');
      final suspiciousQR = ZambiaQRData(
        merchantId: 'merchant_001',
        amount: 5000.0, // Exactly K5000 - suspicious round number
        currency: 'ZMW',
        provider: 'MTN',
        description: 'Large round amount',
        timestamp: DateTime.now(),
      );

      try {
        final suspiciousResult = await AntiFraudService.validateQR(suspiciousQR);
        print('⚠️ Suspicious amount passed with warnings: ${suspiciousResult.message}');
        print('   Risk Level: ${suspiciousResult.riskLevel.name}');
      } catch (e) {
        print('✅ Suspicious amount correctly flagged: $e');
      }

    } catch (e) {
      print('❌ QR validation example failed: $e');
    }
  }

  /// Example 3: Merchant risk management
  static Future<void> merchantRiskManagementExample() async {
    print('\n⚠️ === MERCHANT RISK MANAGEMENT EXAMPLE ===');

    try {
      await MerchantRegistry.initialize();

      const merchantId = 'merchant_001';

      print('📊 Managing merchant risk scores...');

      // Update risk score
      await MerchantRegistry.updateRiskScore(merchantId, 0.3);
      print('✅ Risk score updated to 0.3 (Low risk)');

      // Get merchant info
      final merchantInfo = await MerchantRegistry.getMerchantInfo(merchantId);
      if (merchantInfo != null) {
        print('📋 Merchant Status:');
        print('   Business: ${merchantInfo.businessName}');
        print('   Status: ${merchantInfo.status.name}');
        print('   Risk Score: ${merchantInfo.riskScore.toStringAsFixed(2)}');
        print('   Verification Level: ${merchantInfo.verificationLevel}');
        print('   Last Verified: ${merchantInfo.lastVerifiedAt}');
      }

      // Test high risk scenario
      print('\n⚠️ Testing high-risk merchant...');
      await MerchantRegistry.updateRiskScore(merchantId, 0.9);
      
      final highRiskQR = ZambiaQRData(
        merchantId: merchantId,
        amount: 100.0,
        currency: 'ZMW',
        provider: 'MTN',
        timestamp: DateTime.now(),
      );

      try {
        await AntiFraudService.validateQR(highRiskQR);
        print('❌ High-risk merchant should have been blocked');
      } catch (e) {
        print('✅ High-risk merchant correctly blocked: $e');
      }

      // Reset risk score
      await MerchantRegistry.updateRiskScore(merchantId, 0.2);
      print('✅ Risk score reset to 0.2');

      // Test suspension
      print('\n🚫 Testing merchant suspension...');
      final suspended = await MerchantRegistry.suspendMerchant(
        merchantId: merchantId,
        reason: 'Suspicious activity detected',
      );

      if (suspended) {
        print('✅ Merchant suspended successfully');
        
        // Test payment with suspended merchant
        try {
          await AntiFraudService.validateQR(highRiskQR);
          print('❌ Suspended merchant should have been blocked');
        } catch (e) {
          print('✅ Suspended merchant correctly blocked: $e');
        }
      }

    } catch (e) {
      print('❌ Merchant risk management example failed: $e');
    }
  }

  /// Example 4: QR replay attack detection
  static Future<void> qrReplayAttackExample() async {
    print('\n🔄 === QR REPLAY ATTACK EXAMPLE ===');

    try {
      await AntiFraudService.initialize();

      print('🔍 Testing QR replay attack detection...');

      // Create a QR with fixed amount
      final qrData = ZambiaQRData(
        merchantId: 'merchant_001',
        amount: 15.0,
        currency: 'ZMW',
        provider: 'MTN',
        description: 'Test transaction',
        timestamp: DateTime.now(),
      );

      // Use the QR multiple times
      for (int i = 1; i <= 5; i++) {
        try {
          final result = await AntiFraudService.validateQR(qrData);
          print('✅ Usage $i: ${result.message}');
        } catch (e) {
          print('🚫 Usage $i blocked: $e');
          break;
        }
      }

      // Test open amount QR (should allow more uses)
      print('\n🔄 Testing open amount QR (multiple uses allowed)...');
      final openAmountQR = ZambiaQRData(
        merchantId: 'merchant_001',
        amount: 0.0, // Open amount
        currency: 'ZMW',
        provider: 'MTN',
        description: 'Open amount payment',
        timestamp: DateTime.now(),
      );

      for (int i = 1; i <= 3; i++) {
        try {
          final result = await AntiFraudService.validateQR(openAmountQR);
          print('✅ Open amount usage $i: ${result.message}');
        } catch (e) {
          print('🚫 Open amount usage $i blocked: $e');
        }
      }

    } catch (e) {
      print('❌ QR replay attack example failed: $e');
    }
  }

  /// Example 5: Complete fraud protection workflow
  static Future<void> completeFraudProtectionExample() async {
    print('\n🛡️ === COMPLETE FRAUD PROTECTION WORKFLOW ===');

    try {
      // Initialize all services
      await PayMuleQR().initialize();

      print('🔄 Testing complete payment workflow with fraud protection...');

      // Create QR data
      const qrDataString = 'ZPAY:************************************************************************************************************************************************************************************';

      // Process payment with fraud protection
      final result = await PayMuleQR.scanAndPay(
        qrData: qrDataString,
        payerUserId: 'customer_12345',
        customPin: '123456',
      );

      if (result.success) {
        print('✅ Payment completed with fraud protection');
        print('   Transaction ID: ${result.transactionId}');
        print('   Amount: K${result.amount}');
        print('   Merchant: ${result.merchantId}');
      } else {
        print('❌ Payment failed: ${result.message}');
        print('   Error: ${result.error}');
      }

    } catch (e) {
      print('❌ Complete fraud protection example failed: $e');
    }
  }

  /// Build fraud monitoring dashboard widget
  static Widget buildFraudMonitoringWidget() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.security, color: Colors.red),
                SizedBox(width: 8),
                Text(
                  'Fraud Protection Status',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatusItem('Merchant Registry', true, 'Active'),
            _buildStatusItem('QR Validation', true, 'Monitoring'),
            _buildStatusItem('Replay Detection', true, 'Enabled'),
            _buildStatusItem('Risk Assessment', true, 'Real-time'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'All fraud protection systems operational',
                    style: TextStyle(color: Colors.green),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Widget _buildStatusItem(String label, bool isActive, String status) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Row(
            children: [
              Icon(
                isActive ? Icons.check_circle : Icons.error,
                color: isActive ? Colors.green : Colors.red,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                status,
                style: TextStyle(
                  color: isActive ? Colors.green : Colors.red,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Run all anti-fraud examples
  static Future<void> runAllExamples() async {
    print('🇿🇲 === ZAMBIAN QR ANTI-FRAUD PROTECTION EXAMPLES ===\n');

    await merchantRegistrationExample();
    await qrValidationExample();
    await merchantRiskManagementExample();
    await qrReplayAttackExample();
    await completeFraudProtectionExample();

    print('\n✅ All anti-fraud examples completed successfully!');
    print('\n📋 Summary:');
    print('   ✅ Merchant registration and verification');
    print('   ✅ QR validation with fraud detection');
    print('   ✅ Merchant risk management');
    print('   ✅ QR replay attack prevention');
    print('   ✅ Complete fraud protection workflow');
    print('\n🛡️ The anti-fraud system is ready for production use!');
  }
}

/// Example usage in main.dart
/// 
/// ```dart
/// void main() async {
///   WidgetsFlutterBinding.ensureInitialized();
///   
///   // Run anti-fraud examples
///   await AntiFraudExample.runAllExamples();
///   
///   runApp(MyApp());
/// }
/// 
/// class MyApp extends StatelessWidget {
///   @override
///   Widget build(BuildContext context) {
///     return MaterialApp(
///       title: 'Anti-Fraud Protection Demo',
///       home: Scaffold(
///         appBar: AppBar(title: Text('QR Anti-Fraud Protection')),
///         body: Padding(
///           padding: EdgeInsets.all(16),
///           child: AntiFraudExample.buildFraudMonitoringWidget(),
///         ),
///       ),
///     );
///   }
/// }
/// ```
