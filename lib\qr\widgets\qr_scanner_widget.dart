/// QR Scanner Widget for Zambian Mobile Money
/// Optimized for low-light conditions and poor camera quality
/// Includes offline support and user guidance

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

import '../../core/constants/app_constants.dart';
import '../qr_scanner_service.dart';
import '../zambia_qr_format.dart';

/// QR Scanner Widget with Zambian optimizations
class QRScannerWidget extends StatefulWidget {
  final Function(QRScanResult) onScan;
  final Function(String)? onError;
  final bool showFlashToggle;
  final bool showCameraSwitch;
  final String? instructionText;
  final Color? overlayColor;

  const QRScannerWidget({
    Key? key,
    required this.onScan,
    this.onError,
    this.showFlashToggle = true,
    this.showCameraSwitch = true,
    this.instructionText,
    this.overlayColor,
  }) : super(key: key);

  @override
  State<QRScannerWidget> createState() => _QRScannerWidgetState();
}

class _QRScannerWidgetState extends State<QRScannerWidget>
    with WidgetsBindingObserver {
  final QRScannerService _scannerService = QRScannerService();
  
  bool _isInitialized = false;
  bool _isScanning = false;
  bool _flashOn = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeScanner();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _scannerService.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _startScanning();
    } else if (state == AppLifecycleState.paused) {
      _stopScanning();
    }
  }

  Future<void> _initializeScanner() async {
    try {
      final result = await _scannerService.initialize();
      
      if (mounted) {
        setState(() {
          _isInitialized = result.success;
          _errorMessage = result.success ? null : result.message;
        });

        if (result.success) {
          await _startScanning();
        } else {
          widget.onError?.call(result.message);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to initialize camera: ${e.toString()}';
        });
        widget.onError?.call(_errorMessage!);
      }
    }
  }

  Future<void> _startScanning() async {
    if (!_isInitialized || _isScanning) return;

    try {
      setState(() {
        _isScanning = true;
        _errorMessage = null;
      });

      await _scannerService.startScanning(
        onScan: (result) {
          if (mounted) {
            widget.onScan(result);
            if (result.success) {
              _provideFeedback();
            }
          }
        },
        onError: (error) {
          if (mounted) {
            setState(() {
              _errorMessage = error;
            });
            widget.onError?.call(error);
          }
        },
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _isScanning = false;
          _errorMessage = 'Failed to start scanning: ${e.toString()}';
        });
        widget.onError?.call(_errorMessage!);
      }
    }
  }

  Future<void> _stopScanning() async {
    if (!_isScanning) return;

    try {
      await _scannerService.stopScanning();
      if (mounted) {
        setState(() {
          _isScanning = false;
        });
      }
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _toggleFlash() async {
    try {
      await _scannerService.toggleFlash();
      final isOn = await _scannerService.isTorchOn();
      
      if (mounted) {
        setState(() {
          _flashOn = isOn;
        });
      }
    } catch (e) {
      widget.onError?.call('Failed to toggle flash: ${e.toString()}');
    }
  }

  Future<void> _switchCamera() async {
    try {
      await _scannerService.switchCamera();
    } catch (e) {
      widget.onError?.call('Failed to switch camera: ${e.toString()}');
    }
  }

  void _provideFeedback() {
    HapticFeedback.lightImpact();
    SystemSound.play(SystemSoundType.click);
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return _buildLoadingView();
    }

    if (_errorMessage != null) {
      return _buildErrorView();
    }

    return Stack(
      children: [
        // Scanner view
        _scannerService.getScannerWidget(
          onScan: widget.onScan,
          onError: widget.onError,
          overlay: _buildScannerOverlay(),
        ),
        
        // Controls overlay
        _buildControlsOverlay(),
      ],
    );
  }

  Widget _buildLoadingView() {
    return Container(
      color: Colors.black,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            SizedBox(height: 16),
            Text(
              'Initializing camera...',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializeScanner,
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScannerOverlay() {
    return Container(
      decoration: ShapeDecoration(
        shape: QRScannerOverlayShape(
          borderColor: widget.overlayColor ?? const Color(0xFF2E7D32),
          borderRadius: 10,
          borderLength: 30,
          borderWidth: 10,
          cutOutSize: 250,
        ),
      ),
      child: Column(
        children: [
          const Spacer(),
          Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              widget.instructionText ?? 
              'Point your camera at a Pay Mule QR code\nMake sure the code is well lit and in focus',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  Widget _buildControlsOverlay() {
    return Positioned(
      bottom: 50,
      left: 0,
      right: 0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          if (widget.showFlashToggle)
            _buildControlButton(
              icon: _flashOn ? Icons.flash_on : Icons.flash_off,
              label: 'Flash',
              onPressed: _toggleFlash,
            ),
          
          if (widget.showCameraSwitch)
            _buildControlButton(
              icon: Icons.flip_camera_android,
              label: 'Switch',
              onPressed: _switchCamera,
            ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          decoration: const BoxDecoration(
            color: Colors.black54,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: Icon(icon, color: Colors.white),
            onPressed: onPressed,
            iconSize: 32,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}

/// Custom QR Scanner Overlay Shape
class QRScannerOverlayShape extends ShapeBorder {
  final Color borderColor;
  final double borderWidth;
  final double borderLength;
  final double borderRadius;
  final double cutOutSize;

  const QRScannerOverlayShape({
    this.borderColor = Colors.green,
    this.borderWidth = 3.0,
    this.borderLength = 40,
    this.borderRadius = 10,
    this.cutOutSize = 250,
  });

  @override
  EdgeInsetsGeometry get dimensions => const EdgeInsets.all(10);

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return Path()
      ..fillType = PathFillType.evenOdd
      ..addPath(getOuterPath(rect), Offset.zero);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    Path path = Path()..addRect(rect);
    
    final cutOutRect = Rect.fromCenter(
      center: rect.center,
      width: cutOutSize,
      height: cutOutSize,
    );
    
    path.addRRect(RRect.fromRectAndRadius(
      cutOutRect,
      Radius.circular(borderRadius),
    ));
    
    return path;
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    final cutOutRect = Rect.fromCenter(
      center: rect.center,
      width: cutOutSize,
      height: cutOutSize,
    );

    final paint = Paint()
      ..color = borderColor
      ..strokeWidth = borderWidth
      ..style = PaintingStyle.stroke;

    // Draw corner borders
    _drawCornerBorders(canvas, cutOutRect, paint);
    
    // Draw overlay
    final overlayPaint = Paint()
      ..color = Colors.black54;
    
    final overlayPath = Path()
      ..addRect(rect)
      ..addRRect(RRect.fromRectAndRadius(
        cutOutRect,
        Radius.circular(borderRadius),
      ))
      ..fillType = PathFillType.evenOdd;
    
    canvas.drawPath(overlayPath, overlayPaint);
  }

  void _drawCornerBorders(Canvas canvas, Rect rect, Paint paint) {
    // Top-left corner
    canvas.drawPath(
      Path()
        ..moveTo(rect.left, rect.top + borderLength)
        ..lineTo(rect.left, rect.top + borderRadius)
        ..arcToPoint(
          Offset(rect.left + borderRadius, rect.top),
          radius: Radius.circular(borderRadius),
        )
        ..lineTo(rect.left + borderLength, rect.top),
      paint,
    );

    // Top-right corner
    canvas.drawPath(
      Path()
        ..moveTo(rect.right - borderLength, rect.top)
        ..lineTo(rect.right - borderRadius, rect.top)
        ..arcToPoint(
          Offset(rect.right, rect.top + borderRadius),
          radius: Radius.circular(borderRadius),
        )
        ..lineTo(rect.right, rect.top + borderLength),
      paint,
    );

    // Bottom-left corner
    canvas.drawPath(
      Path()
        ..moveTo(rect.left, rect.bottom - borderLength)
        ..lineTo(rect.left, rect.bottom - borderRadius)
        ..arcToPoint(
          Offset(rect.left + borderRadius, rect.bottom),
          radius: Radius.circular(borderRadius),
        )
        ..lineTo(rect.left + borderLength, rect.bottom),
      paint,
    );

    // Bottom-right corner
    canvas.drawPath(
      Path()
        ..moveTo(rect.right - borderLength, rect.bottom)
        ..lineTo(rect.right - borderRadius, rect.bottom)
        ..arcToPoint(
          Offset(rect.right, rect.bottom - borderRadius),
          radius: Radius.circular(borderRadius),
        )
        ..lineTo(rect.right, rect.bottom - borderLength),
      paint,
    );
  }

  @override
  ShapeBorder scale(double t) => QRScannerOverlayShape(
        borderColor: borderColor,
        borderWidth: borderWidth,
        borderLength: borderLength,
        borderRadius: borderRadius,
        cutOutSize: cutOutSize,
      );
}
