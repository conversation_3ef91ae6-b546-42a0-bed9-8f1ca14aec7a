/// Offline Handling Example for Zambian QR Payments
/// Demonstrates complete offline workflow with queue management and receipts
/// Shows connectivity monitoring and automatic sync capabilities

import 'package:flutter/material.dart';

import '../zambia_qr.dart';
import '../zambia_qr_format.dart';
import '../utils/connectivity_helper.dart';
import '../utils/offline_queue.dart';
import '../utils/receipt_generator.dart';

/// Complete offline handling example
class OfflineHandlingExample {
  /// Example 1: Basic offline result handling
  static Future<void> basicOfflineHandlingExample() async {
    print('📱 === BASIC OFFLINE HANDLING EXAMPLE ===');

    try {
      // Initialize the system
      await PayMuleQR().initialize();

      // Create sample QR data
      final qrData = ZambiaQRData(
        merchantId: 'merchant_12345',
        amount: 25.0,
        currency: 'ZMW',
        provider: 'MTN',
        description: 'Fresh vegetables',
        timestamp: DateTime.now(),
      );

      print('📊 Current connectivity status:');
      final connectivityStatus = PayMuleQR.getConnectivityStatus();
      connectivityStatus.forEach((key, value) {
        print('   $key: $value');
      });

      // Simulate offline scenario
      print('\n🔄 Simulating offline payment processing...');
      
      // Handle offline result - success case
      PayMuleQR._handleOfflineResult(true, qrData);
      
      // Check queue stats
      final queueStats = await PayMuleQR.getOfflineQueueStats();
      print('\n📦 Queue Statistics:');
      print('   Total: ${queueStats.totalCount}');
      print('   Pending: ${queueStats.pendingCount}');
      print('   Completed: ${queueStats.completedCount}');
      print('   Failed: ${queueStats.failedCount}');
      print('   Success Rate: ${(queueStats.successRate * 100).toStringAsFixed(1)}%');

    } catch (e) {
      print('❌ Basic offline handling example failed: $e');
    }
  }

  /// Example 2: Complete offline payment workflow
  static Future<void> completeOfflineWorkflowExample() async {
    print('\n🔄 === COMPLETE OFFLINE WORKFLOW EXAMPLE ===');

    try {
      // Initialize services
      await PayMuleQR().initialize();

      // Sample QR code data
      const qrDataString = 'ZPAY:eyJ2IjoiMi4wIiwicCI6Ik0iLCJtIjoiYWJjZGVmZ2giLCJhIjoiMzUuMDAiLCJjIjoiSyIsImQiOiJPZmZsaW5lIHBheW1lbnQiLCJ0IjoxNzA0MTAwMDAwLCJlIjoxNzA0MTg2NDAwLCJoIjoiYWJjZGVmZ2gifQ==';

      print('🔍 Processing QR payment with offline handling...');
      
      // Process payment with offline handling
      final result = await PayMuleQR.processQRPaymentWithOfflineHandling(
        qrData: qrDataString,
        payerUserId: 'customer_67890',
        customPin: '123456',
      );

      print('\n💳 Payment Result:');
      print('   Success: ${result.success}');
      print('   Message: ${result.message}');
      print('   Offline: ${result.isOffline}');
      
      if (result.transactionId != null) {
        print('   Transaction ID: ${result.transactionId}');
        
        // Get receipts for this transaction
        final receipts = await ReceiptGenerator.getReceiptsForTransaction(
          result.transactionId!,
        );
        
        print('\n🧾 Generated Receipts: ${receipts.length}');
        for (final receipt in receipts) {
          print('   Receipt ID: ${receipt.id}');
          print('   Type: ${receipt.isOffline ? 'Offline' : 'Online'}');
          print('   Created: ${receipt.createdAt}');
        }
      }

    } catch (e) {
      print('❌ Complete offline workflow example failed: $e');
    }
  }

  /// Example 3: Connectivity monitoring and sync
  static Future<void> connectivityMonitoringExample() async {
    print('\n🌐 === CONNECTIVITY MONITORING EXAMPLE ===');

    try {
      // Initialize connectivity monitoring
      await Connectivity.initialize();

      print('📡 Current Network Status:');
      print('   Online: ${Connectivity.isOnline}');
      print('   Connection Type: ${Connectivity.connectionType}');
      print('   Network Type: ${Connectivity.networkType}');
      print('   Network Quality: ${(Connectivity.networkQuality * 100).toStringAsFixed(1)}%');
      print('   Suitable for Payments: ${Connectivity.isSuitableForPayments}');
      print('   Should Use Offline: ${Connectivity.shouldUseOfflineMode}');

      print('\n💡 Network Recommendation:');
      print('   ${Connectivity.getNetworkRecommendation()}');

      // Test specific operations
      final operations = ['QR_GENERATION', 'QR_PAYMENT', 'MERCHANT_REGISTRATION', 'SYNC_TRANSACTIONS'];
      print('\n🔧 Operation Recommendations:');
      for (final operation in operations) {
        final shouldPerformOnline = Connectivity.shouldPerformOnline(operation);
        print('   $operation: ${shouldPerformOnline ? 'Online' : 'Offline'}');
      }

      // Simulate connectivity test
      print('\n🧪 Testing Connection...');
      final hasConnection = await Connectivity.testConnection(
        timeout: const Duration(seconds: 5),
      );
      print('   Connection Test: ${hasConnection ? 'PASSED' : 'FAILED'}');

    } catch (e) {
      print('❌ Connectivity monitoring example failed: $e');
    }
  }

  /// Example 4: Queue management and sync
  static Future<void> queueManagementExample() async {
    print('\n📦 === QUEUE MANAGEMENT EXAMPLE ===');

    try {
      // Initialize queue
      await OfflineQueue.initialize();

      // Add sample transactions to queue
      print('➕ Adding transactions to offline queue...');
      
      final sampleTransactions = [
        ZambiaQRData(
          merchantId: 'merchant_001',
          amount: 15.0,
          currency: 'ZMW',
          provider: 'MTN',
          description: 'Groceries',
        ),
        ZambiaQRData(
          merchantId: 'merchant_002',
          amount: 30.0,
          currency: 'ZMW',
          provider: 'AIRTEL',
          description: 'Transport',
        ),
        ZambiaQRData(
          merchantId: 'merchant_003',
          amount: 50.0,
          currency: 'ZMW',
          provider: 'ZAMTEL',
          description: 'Market goods',
        ),
      ];

      for (int i = 0; i < sampleTransactions.length; i++) {
        final queueId = await OfflineQueue.add(
          transaction: sampleTransactions[i],
          status: Status.PENDING,
          priority: i == 0 ? Priority.HIGH : Priority.NORMAL,
        );
        print('   Added: $queueId (${sampleTransactions[i].description})');
      }

      // Get queue statistics
      final stats = await OfflineQueue.getStats();
      print('\n📊 Queue Statistics:');
      print('   Total: ${stats.totalCount}');
      print('   Pending: ${stats.pendingCount}');
      print('   Processing: ${stats.processingCount}');
      print('   Completed: ${stats.completedCount}');
      print('   Failed: ${stats.failedCount}');

      // Get pending transactions
      final pendingTransactions = await OfflineQueue.getPendingTransactions(
        limit: 10,
        minPriority: Priority.NORMAL,
      );
      
      print('\n⏳ Pending Transactions: ${pendingTransactions.length}');
      for (final transaction in pendingTransactions) {
        print('   ${transaction.id}: K${transaction.transaction.amount} (${transaction.priority.name})');
      }

      // Simulate queue processing
      print('\n🔄 Processing offline queue...');
      final syncResult = await OfflineQueue.processQueue();
      
      print('   Sync Result: ${syncResult.success ? 'SUCCESS' : 'FAILED'}');
      print('   Message: ${syncResult.message}');
      print('   Processed: ${syncResult.processedCount}');
      print('   Failed: ${syncResult.failedCount}');

      // Clean up completed transactions
      print('\n🧹 Cleaning up completed transactions...');
      final cleanedCount = await OfflineQueue.clearCompleted(
        olderThan: const Duration(hours: 1),
      );
      print('   Cleaned: $cleanedCount transactions');

    } catch (e) {
      print('❌ Queue management example failed: $e');
    }
  }

  /// Example 5: Receipt generation and management
  static Future<void> receiptManagementExample() async {
    print('\n🧾 === RECEIPT MANAGEMENT EXAMPLE ===');

    try {
      // Initialize receipt generator
      await ReceiptGenerator.initialize();

      // Create sample transaction data
      final transactionData = ZambiaQRData(
        merchantId: 'merchant_12345',
        amount: 75.0,
        currency: 'ZMW',
        provider: 'MTN',
        description: 'Restaurant bill',
        timestamp: DateTime.now(),
      );

      print('📝 Generating offline receipt...');
      
      // Generate offline receipt
      final receiptId = await ReceiptGenerator.saveOfflineReceipt(
        transactionData,
        transactionId: 'txn_67890',
        payerName: 'John Mwanza',
        merchantName: 'Lusaka Restaurant',
        isOffline: true,
      );

      print('   Receipt ID: $receiptId');

      // Retrieve receipt
      final receiptData = await ReceiptGenerator.getReceipt(receiptId);
      if (receiptData != null) {
        print('\n📄 Receipt Details:');
        print('   Transaction: ${receiptData.transactionId}');
        print('   Amount: K${receiptData.content.amount}');
        print('   Merchant: ${receiptData.content.merchantName}');
        print('   Payer: ${receiptData.content.payerName}');
        print('   Provider: ${receiptData.content.provider}');
        print('   Offline: ${receiptData.isOffline}');
        print('   Created: ${receiptData.createdAt}');

        print('\n📱 SMS Receipt:');
        print(receiptData.smsReceipt);

        // Share receipt
        print('\n📤 Sharing receipt...');
        final shared = await ReceiptGenerator.shareReceipt(receiptId);
        print('   Shared: ${shared ? 'SUCCESS' : 'FAILED'}');
      }

    } catch (e) {
      print('❌ Receipt management example failed: $e');
    }
  }

  /// Build offline status widget
  static Widget buildOfflineStatusWidget() {
    return StreamBuilder<Map<String, dynamic>>(
      stream: _getConnectivityStream(),
      builder: (context, snapshot) {
        final status = snapshot.data ?? {};
        final isOnline = status['isOnline'] ?? false;
        final networkQuality = status['networkQuality'] ?? 0.0;
        final shouldUseOffline = status['shouldUseOfflineMode'] ?? true;

        return Card(
          color: isOnline ? Colors.green.shade50 : Colors.orange.shade50,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      isOnline ? Icons.wifi : Icons.wifi_off,
                      color: isOnline ? Colors.green : Colors.orange,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      isOnline ? 'Online' : 'Offline',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isOnline ? Colors.green : Colors.orange,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                if (isOnline) ...[
                  Text('Network Quality: ${(networkQuality * 100).toStringAsFixed(0)}%'),
                  LinearProgressIndicator(
                    value: networkQuality,
                    backgroundColor: Colors.grey.shade300,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      networkQuality > 0.7 ? Colors.green : 
                      networkQuality > 0.4 ? Colors.orange : Colors.red,
                    ),
                  ),
                ],
                const SizedBox(height: 8),
                Text(
                  shouldUseOffline 
                      ? 'Using offline mode for better reliability'
                      : 'Online mode available',
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Get connectivity stream (mock implementation)
  static Stream<Map<String, dynamic>> _getConnectivityStream() async* {
    while (true) {
      yield Connectivity.getConnectionStatus();
      await Future.delayed(const Duration(seconds: 5));
    }
  }

  /// Run all offline handling examples
  static Future<void> runAllExamples() async {
    print('🇿🇲 === ZAMBIAN QR OFFLINE HANDLING EXAMPLES ===\n');

    await basicOfflineHandlingExample();
    await completeOfflineWorkflowExample();
    await connectivityMonitoringExample();
    await queueManagementExample();
    await receiptManagementExample();

    print('\n✅ All offline handling examples completed successfully!');
    print('\n📋 Summary:');
    print('   ✅ Offline result handling');
    print('   ✅ Complete offline workflow');
    print('   ✅ Connectivity monitoring');
    print('   ✅ Queue management');
    print('   ✅ Receipt generation');
    print('\n🎯 The system is ready for production use in rural Zambian conditions!');
  }
}

/// Example usage in main.dart
/// 
/// ```dart
/// void main() async {
///   WidgetsFlutterBinding.ensureInitialized();
///   
///   // Run offline handling examples
///   await OfflineHandlingExample.runAllExamples();
///   
///   runApp(MyApp());
/// }
/// 
/// class MyApp extends StatelessWidget {
///   @override
///   Widget build(BuildContext context) {
///     return MaterialApp(
///       title: 'Pay Mule Offline QR',
///       home: Scaffold(
///         appBar: AppBar(title: Text('Offline QR Payments')),
///         body: Column(
///           children: [
///             OfflineHandlingExample.buildOfflineStatusWidget(),
///             // Your other widgets
///           ],
///         ),
///       ),
///     );
///   }
/// }
/// ```
