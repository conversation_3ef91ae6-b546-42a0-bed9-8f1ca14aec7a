// TEST TRANSACTION DEMO
// Demonstrates ADB broadcast test transactions and cross-network transfers

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import '../core/transactions/test_transaction_service.dart';

class TestTransactionDemo extends StatefulWidget {
  @override
  _TestTransactionDemoState createState() => _TestTransactionDemoState();
}

class _TestTransactionDemoState extends State<TestTransactionDemo> {
  final TextEditingController _receiverController = TextEditingController(text: '260971111111');
  final TextEditingController _amountController = TextEditingController(text: '1.0');
  final TextEditingController _referenceController = TextEditingController();
  
  TransactionType selectedType = TransactionType.mtnToAirtel;
  bool isServiceInitialized = false;
  
  List<TestTransactionRequest> receivedRequests = [];
  List<TestTransactionResult> transactionResults = [];
  
  StreamSubscription? requestSubscription;
  StreamSubscription? resultSubscription;

  @override
  void initState() {
    super.initState();
    _initializeService();
    _setupListeners();
  }

  @override
  void dispose() {
    requestSubscription?.cancel();
    resultSubscription?.cancel();
    _receiverController.dispose();
    _amountController.dispose();
    _referenceController.dispose();
    super.dispose();
  }

  /// Initialize test transaction service
  Future<void> _initializeService() async {
    try {
      await TestTransactionService.initialize();
      setState(() {
        isServiceInitialized = true;
      });
      print('✅ Test transaction service initialized');
    } catch (e) {
      print('❌ Failed to initialize service: $e');
    }
  }

  /// Setup stream listeners
  void _setupListeners() {
    requestSubscription = TestTransactionService.requestStream.listen((request) {
      setState(() {
        receivedRequests.insert(0, request);
        if (receivedRequests.length > 10) {
          receivedRequests.removeLast();
        }
      });
    });

    resultSubscription = TestTransactionService.resultStream.listen((result) {
      setState(() {
        transactionResults.insert(0, result);
        if (transactionResults.length > 10) {
          transactionResults.removeLast();
        }
      });
    });
  }

  /// Execute test transaction manually
  Future<void> _executeTestTransaction() async {
    if (!isServiceInitialized) {
      _showSnackBar('Service not initialized', isError: true);
      return;
    }

    final receiver = _receiverController.text.trim();
    final amountText = _amountController.text.trim();
    final reference = _referenceController.text.trim();

    if (receiver.isEmpty || amountText.isEmpty) {
      _showSnackBar('Please fill in receiver and amount', isError: true);
      return;
    }

    final amount = double.tryParse(amountText);
    if (amount == null || amount <= 0) {
      _showSnackBar('Please enter a valid amount', isError: true);
      return;
    }

    try {
      final request = TestTransactionRequest(
        receiver: receiver,
        amount: amount,
        type: selectedType,
        reference: reference.isNotEmpty ? reference : null,
      );

      _showSnackBar('Executing test transaction...', isError: false);
      
      final result = await TestTransactionService.executeTestTransaction(request);
      
      if (result.success) {
        _showSnackBar('Transaction completed: ${result.transactionId}', isError: false);
      } else {
        _showSnackBar('Transaction failed: ${result.message}', isError: true);
      }

    } catch (e) {
      _showSnackBar('Error: ${e.toString()}', isError: true);
    }
  }

  /// Generate ADB command
  String _generateADBCommand() {
    final receiver = _receiverController.text.trim();
    final amount = _amountController.text.trim();
    final type = _getTransactionTypeString(selectedType);
    final reference = _referenceController.text.trim();

    String command = '''adb shell am broadcast -a com.zm.paymule.TEST_TRANSACTION \\
--es "receiver" "$receiver" \\
--es "amount" "$amount" \\
--es "type" "$type"''';

    if (reference.isNotEmpty) {
      command += ''' \\
--es "reference" "$reference"''';
    }

    return command;
  }

  /// Copy ADB command to clipboard
  void _copyADBCommand() {
    final command = _generateADBCommand();
    Clipboard.setData(ClipboardData(text: command));
    _showSnackBar('ADB command copied to clipboard', isError: false);
  }

  /// Get transaction type string
  String _getTransactionTypeString(TransactionType type) {
    switch (type) {
      case TransactionType.mtnToAirtel:
        return 'mtn_to_airtel';
      case TransactionType.airtelToMtn:
        return 'airtel_to_mtn';
      case TransactionType.mtnToZamtel:
        return 'mtn_to_zamtel';
      case TransactionType.zamtelToMtn:
        return 'zamtel_to_mtn';
      case TransactionType.airtelToZamtel:
        return 'airtel_to_zamtel';
      case TransactionType.zamtelToAirtel:
        return 'zamtel_to_airtel';
      case TransactionType.sameNetwork:
        return 'same_network';
      default:
        return 'unknown';
    }
  }

  /// Get transaction type display name
  String _getTransactionTypeDisplayName(TransactionType type) {
    switch (type) {
      case TransactionType.mtnToAirtel:
        return 'MTN → Airtel';
      case TransactionType.airtelToMtn:
        return 'Airtel → MTN';
      case TransactionType.mtnToZamtel:
        return 'MTN → Zamtel';
      case TransactionType.zamtelToMtn:
        return 'Zamtel → MTN';
      case TransactionType.airtelToZamtel:
        return 'Airtel → Zamtel';
      case TransactionType.zamtelToAirtel:
        return 'Zamtel → Airtel';
      case TransactionType.sameNetwork:
        return 'Same Network';
      default:
        return 'Unknown';
    }
  }

  /// Show snackbar message
  void _showSnackBar(String message, {required bool isError}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red[600] : Colors.green[600],
        duration: Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('🧪 Test Transaction Demo'),
        backgroundColor: Colors.purple[800],
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Service status
            Card(
              color: isServiceInitialized ? Colors.green[50] : Colors.red[50],
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      isServiceInitialized ? Icons.check_circle : Icons.error,
                      color: isServiceInitialized ? Colors.green : Colors.red,
                    ),
                    SizedBox(width: 8),
                    Text(
                      isServiceInitialized 
                          ? 'Test Transaction Service: Ready'
                          : 'Test Transaction Service: Not Ready',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),

            // Transaction form
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Transaction Details',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 16),
                    
                    // Receiver phone number
                    TextField(
                      controller: _receiverController,
                      decoration: InputDecoration(
                        labelText: 'Receiver Phone Number',
                        hintText: '260971111111',
                        prefixText: '+',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.phone,
                    ),
                    SizedBox(height: 16),
                    
                    // Amount
                    TextField(
                      controller: _amountController,
                      decoration: InputDecoration(
                        labelText: 'Amount (ZMW)',
                        hintText: '1.0',
                        prefixText: 'K ',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.numberWithOptions(decimal: true),
                    ),
                    SizedBox(height: 16),
                    
                    // Transaction type
                    DropdownButtonFormField<TransactionType>(
                      value: selectedType,
                      decoration: InputDecoration(
                        labelText: 'Transaction Type',
                        border: OutlineInputBorder(),
                      ),
                      items: TransactionType.values
                          .where((type) => type != TransactionType.unknown)
                          .map((type) => DropdownMenuItem(
                                value: type,
                                child: Text(_getTransactionTypeDisplayName(type)),
                              ))
                          .toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedType = value;
                          });
                        }
                      },
                    ),
                    SizedBox(height: 16),
                    
                    // Reference (optional)
                    TextField(
                      controller: _referenceController,
                      decoration: InputDecoration(
                        labelText: 'Reference (Optional)',
                        hintText: 'Transaction reference',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    SizedBox(height: 24),
                    
                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: isServiceInitialized ? _executeTestTransaction : null,
                            icon: Icon(Icons.play_arrow),
                            label: Text('Execute Test'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green[600],
                              padding: EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _copyADBCommand,
                            icon: Icon(Icons.copy),
                            label: Text('Copy ADB'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue[600],
                              padding: EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),

            // ADB Command display
            Card(
              color: Colors.grey[100],
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'ADB Command:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.black87,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        _generateADBCommand(),
                        style: TextStyle(
                          fontFamily: 'monospace',
                          color: Colors.green[300],
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),

            // Transaction history
            Expanded(
              child: DefaultTabController(
                length: 2,
                child: Column(
                  children: [
                    TabBar(
                      labelColor: Colors.purple[800],
                      tabs: [
                        Tab(text: 'Requests (${receivedRequests.length})'),
                        Tab(text: 'Results (${transactionResults.length})'),
                      ],
                    ),
                    Expanded(
                      child: TabBarView(
                        children: [
                          _buildRequestsList(),
                          _buildResultsList(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequestsList() {
    if (receivedRequests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No requests received',
              style: TextStyle(color: Colors.grey[600]),
            ),
            SizedBox(height: 8),
            Text(
              'Execute a test or send ADB broadcast',
              style: TextStyle(color: Colors.grey[500], fontSize: 12),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: receivedRequests.length,
      itemBuilder: (context, index) {
        final request = receivedRequests[index];
        return Card(
          margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: ListTile(
            leading: Icon(Icons.call_received, color: Colors.blue),
            title: Text('${_getTransactionTypeDisplayName(request.type)}'),
            subtitle: Text(
              'To: ${request.receiver}\n'
              'Amount: K${request.amount.toStringAsFixed(2)}',
            ),
            trailing: Text(
              DateTime.now().toString().substring(11, 19),
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            isThreeLine: true,
          ),
        );
      },
    );
  }

  Widget _buildResultsList() {
    if (transactionResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No transaction results',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: transactionResults.length,
      itemBuilder: (context, index) {
        final result = transactionResults[index];
        return Card(
          margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: ListTile(
            leading: Icon(
              result.success ? Icons.check_circle : Icons.error,
              color: result.success ? Colors.green : Colors.red,
            ),
            title: Text('${result.transactionId}'),
            subtitle: Text(
              '${result.message}\n'
              'Fee: K${result.fee?.toStringAsFixed(2) ?? "0.00"}',
            ),
            trailing: Text(
              result.timestamp.toString().substring(11, 19),
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            isThreeLine: true,
          ),
        );
      },
    );
  }
}
