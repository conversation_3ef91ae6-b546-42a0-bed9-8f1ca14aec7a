/// Amount Validation Example for Zambian QR Payments
/// Demonstrates comprehensive amount validation with provider limits
/// Shows formatting, parsing, and merchant category recommendations

import 'package:flutter/material.dart';

import '../zambia_qr_format.dart';

/// Amount validation examples and utilities
class AmountValidationExample {
  /// Example 1: Basic amount validation
  static void basicAmountValidationExample() {
    print('💰 === BASIC AMOUNT VALIDATION EXAMPLE ===');

    final testAmounts = [
      -10.0,    // Negative
      0.0,      // Open amount
      2.5,      // Too low
      5.0,      // Minimum valid
      50.0,     // Valid
      500.0,    // Valid
      2500.0,   // Valid
      5000.0,   // Maximum valid
      6000.0,   // Too high
      25.555,   // Too many decimals
    ];

    print('🧪 Testing amounts:');
    for (final amount in testAmounts) {
      final isValid = ZambiaQRFormat.validateAmount(amount);
      final formatted = ZambiaQRFormat.formatAmountForDisplay(amount);
      
      print('   $formatted: ${isValid ? '✅ VALID' : '❌ INVALID'}');
    }
  }

  /// Example 2: Detailed amount validation
  static void detailedAmountValidationExample() {
    print('\n🔍 === DETAILED AMOUNT VALIDATION EXAMPLE ===');

    final testAmounts = [-5.0, 0.0, 3.0, 25.555, 50.0, 6000.0];

    print('📋 Detailed validation results:');
    for (final amount in testAmounts) {
      final validation = ZambiaQRFormat.validateAmountDetailed(amount);
      
      print('\n💰 Amount: ${ZambiaQRFormat.formatAmountForDisplay(amount)}');
      print('   Valid: ${validation.isValid ? '✅' : '❌'}');
      print('   Message: ${validation.message}');
      
      if (validation.error != null) {
        print('   Error: ${validation.error}');
      }
      
      if (validation.suggestedAmount != null) {
        print('   Suggested: ${ZambiaQRFormat.formatAmountForDisplay(validation.suggestedAmount!)}');
      }
      
      if (validation.isOpenAmount) {
        print('   Type: Open Amount (Customer enters amount)');
      }
    }
  }

  /// Example 3: Provider-specific validation
  static void providerValidationExample() {
    print('\n📱 === PROVIDER-SPECIFIC VALIDATION EXAMPLE ===');

    final providers = ['MTN', 'AIRTEL', 'ZAMTEL', 'UNKNOWN'];
    final testAmounts = [100.0, 1500.0, 2500.0, 3500.0, 4500.0];

    print('🏢 Provider limits comparison:');
    print('   MTN: K3,000 daily limit');
    print('   AIRTEL: K2,500 daily limit');
    print('   ZAMTEL: K2,000 daily limit');

    for (final amount in testAmounts) {
      print('\n💰 Testing ${ZambiaQRFormat.formatAmountForDisplay(amount)}:');
      
      for (final provider in providers) {
        final isValid = ZambiaQRFormat.validateAmountForProvider(amount, provider);
        print('   $provider: ${isValid ? '✅ VALID' : '❌ INVALID'}');
      }
    }
  }

  /// Example 4: Merchant category recommendations
  static void merchantCategoryExample() {
    print('\n🏪 === MERCHANT CATEGORY RECOMMENDATIONS EXAMPLE ===');

    final categories = ['RETAIL', 'TRANSPORT', 'UTILITIES', 'FOOD', 'SERVICES', 'MARKET'];
    
    print('📊 Recommended amounts by category:');
    
    for (final category in categories) {
      final suggestedAmounts = ZambiaQRFormat.getSuggestedAmounts(category);
      final ranges = ZambiaQRFormat.getRecommendedAmountRanges();
      final range = ranges[category];
      
      print('\n🏷️  $category:');
      if (range != null) {
        print('   Range: ${ZambiaQRFormat.formatAmountForDisplay(range.min)} - ${ZambiaQRFormat.formatAmountForDisplay(range.max)}');
      }
      
      print('   Suggested: ${suggestedAmounts.map((a) => ZambiaQRFormat.formatAmountForDisplay(a, includeSymbol: false)).join(', ')}');
    }
  }

  /// Example 5: Amount formatting and parsing
  static void formattingAndParsingExample() {
    print('\n🎨 === FORMATTING AND PARSING EXAMPLE ===');

    final testAmounts = [5.0, 25.50, 100.0, 1234.56, 5000.0];
    
    print('💱 Amount formatting:');
    for (final amount in testAmounts) {
      final withSymbol = ZambiaQRFormat.formatAmountForDisplay(amount, includeSymbol: true);
      final withoutSymbol = ZambiaQRFormat.formatAmountForDisplay(amount, includeSymbol: false);
      
      print('   $amount → "$withSymbol" / "$withoutSymbol"');
    }

    print('\n🔤 String parsing:');
    final testStrings = [
      'K 25.50',
      '100',
      'ZMW 1,234.56',
      'K5000.00',
      '  250.75  ',
      'invalid',
      '',
    ];

    for (final str in testStrings) {
      final parsed = ZambiaQRFormat.parseAmountFromString(str);
      print('   "$str" → ${parsed?.toString() ?? 'null'}');
    }
  }

  /// Example 6: Typical amount checking
  static void typicalAmountExample() {
    print('\n⭐ === TYPICAL AMOUNT CHECKING EXAMPLE ===');

    final testAmounts = [5.0, 7.5, 10.0, 15.0, 23.0, 25.0, 50.0, 75.0, 100.0, 500.0, 1000.0];
    
    print('🎯 Checking for typical amounts:');
    for (final amount in testAmounts) {
      final isTypical = ZambiaQRFormat.isTypicalAmount(amount);
      final formatted = ZambiaQRFormat.formatAmountForDisplay(amount);
      
      print('   $formatted: ${isTypical ? '⭐ TYPICAL' : '   Regular'}');
    }
  }

  /// Build amount input widget with validation
  static Widget buildAmountInputWidget({
    required Function(double?) onAmountChanged,
    String? merchantCategory,
    String? provider,
  }) {
    return StatefulWidget(
      child: _AmountInputWidget(
        onAmountChanged: onAmountChanged,
        merchantCategory: merchantCategory,
        provider: provider,
      ),
    );
  }

  /// Build suggested amounts widget
  static Widget buildSuggestedAmountsWidget({
    required String merchantCategory,
    required Function(double) onAmountSelected,
  }) {
    final suggestedAmounts = ZambiaQRFormat.getSuggestedAmounts(merchantCategory);
    
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: suggestedAmounts.map((amount) {
        return ActionChip(
          label: Text(ZambiaQRFormat.formatAmountForDisplay(amount)),
          onPressed: () => onAmountSelected(amount),
          backgroundColor: Colors.green.shade50,
          labelStyle: const TextStyle(color: Colors.green),
        );
      }).toList(),
    );
  }

  /// Build amount validation indicator
  static Widget buildValidationIndicator(double? amount, {String? provider}) {
    if (amount == null) {
      return const SizedBox.shrink();
    }

    final validation = ZambiaQRFormat.validateAmountDetailed(amount);
    final providerValid = provider != null 
        ? ZambiaQRFormat.validateAmountForProvider(amount, provider)
        : true;

    Color color;
    IconData icon;
    String message;

    if (!validation.isValid || !providerValid) {
      color = Colors.red;
      icon = Icons.error;
      message = !validation.isValid ? validation.message : 'Exceeds $provider limits';
    } else if (validation.isOpenAmount) {
      color = Colors.blue;
      icon = Icons.edit;
      message = validation.message;
    } else {
      color = Colors.green;
      icon = Icons.check_circle;
      message = validation.message;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              message,
              style: TextStyle(color: color, fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// Run all amount validation examples
  static void runAllExamples() {
    print('🇿🇲 === ZAMBIAN QR AMOUNT VALIDATION EXAMPLES ===\n');

    basicAmountValidationExample();
    detailedAmountValidationExample();
    providerValidationExample();
    merchantCategoryExample();
    formattingAndParsingExample();
    typicalAmountExample();

    print('\n✅ All amount validation examples completed successfully!');
    print('\n📋 Summary:');
    print('   ✅ Basic validation (K5 - K5,000)');
    print('   ✅ Provider-specific limits');
    print('   ✅ Merchant category recommendations');
    print('   ✅ Amount formatting and parsing');
    print('   ✅ Typical amount detection');
    print('   ✅ Open amount support (0 = customer enters)');
    print('\n🎯 The amount validation system is ready for Zambian mobile money!');
  }
}

/// Amount input widget with validation
class _AmountInputWidget extends StatefulWidget {
  final Function(double?) onAmountChanged;
  final String? merchantCategory;
  final String? provider;

  const _AmountInputWidget({
    required this.onAmountChanged,
    this.merchantCategory,
    this.provider,
  });

  @override
  State<_AmountInputWidget> createState() => _AmountInputWidgetState();
}

class _AmountInputWidgetState extends State<_AmountInputWidget> {
  final TextEditingController _controller = TextEditingController();
  double? _currentAmount;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTextChanged(String value) {
    final amount = ZambiaQRFormat.parseAmountFromString(value);
    setState(() {
      _currentAmount = amount;
    });
    widget.onAmountChanged(amount);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _controller,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          decoration: const InputDecoration(
            labelText: 'Amount',
            prefixText: 'K ',
            border: OutlineInputBorder(),
            helperText: 'Enter 0 for open amount',
          ),
          onChanged: _onTextChanged,
        ),
        const SizedBox(height: 8),
        AmountValidationExample.buildValidationIndicator(
          _currentAmount,
          provider: widget.provider,
        ),
        if (widget.merchantCategory != null) ...[
          const SizedBox(height: 16),
          const Text('Suggested amounts:', style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          AmountValidationExample.buildSuggestedAmountsWidget(
            merchantCategory: widget.merchantCategory!,
            onAmountSelected: (amount) {
              _controller.text = amount.toString();
              _onTextChanged(amount.toString());
            },
          ),
        ],
      ],
    );
  }
}

/// Example usage in main.dart
/// 
/// ```dart
/// void main() async {
///   // Run amount validation examples
///   AmountValidationExample.runAllExamples();
///   
///   runApp(MyApp());
/// }
/// 
/// class MyApp extends StatelessWidget {
///   @override
///   Widget build(BuildContext context) {
///     return MaterialApp(
///       title: 'Amount Validation Demo',
///       home: Scaffold(
///         appBar: AppBar(title: Text('QR Amount Validation')),
///         body: Padding(
///           padding: EdgeInsets.all(16),
///           child: AmountValidationExample.buildAmountInputWidget(
///             onAmountChanged: (amount) {
///               print('Amount changed: $amount');
///             },
///             merchantCategory: 'RETAIL',
///             provider: 'MTN',
///           ),
///         ),
///       ),
///     );
///   }
/// }
/// ```
