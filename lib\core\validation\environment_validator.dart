// ENVIRONMENT VALIDATOR
// Production environment validation with comprehensive checks

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';

enum EnvironmentType {
  development,
  staging,
  production,
  unknown
}

class Environment {
  static EnvironmentType _currentEnvironment = EnvironmentType.unknown;
  static bool _isInitialized = false;
  static Map<String, dynamic> _environmentConfig = {};

  /// Initialize environment detection
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🔍 Initializing environment detection...');

      // Detect environment from multiple sources
      _currentEnvironment = await _detectEnvironment();
      _environmentConfig = await _loadEnvironmentConfig();
      _isInitialized = true;

      print('✅ Environment initialized: ${_currentEnvironment.toString()}');
      print('📋 Config loaded: ${_environmentConfig.keys.length} settings');

    } catch (e) {
      print('❌ Environment initialization failed: $e');
      _currentEnvironment = EnvironmentType.unknown;
    }
  }

  /// Check if current environment is production
  static bool get isProduction {
    _ensureInitialized();
    return _currentEnvironment == EnvironmentType.production;
  }

  /// Check if current environment is development
  static bool get isDevelopment {
    _ensureInitialized();
    return _currentEnvironment == EnvironmentType.development;
  }

  /// Check if current environment is staging
  static bool get isStaging {
    _ensureInitialized();
    return _currentEnvironment == EnvironmentType.staging;
  }

  /// Get current environment type
  static EnvironmentType get current {
    _ensureInitialized();
    return _currentEnvironment;
  }

  /// Get environment configuration
  static Map<String, dynamic> get config {
    _ensureInitialized();
    return Map.from(_environmentConfig);
  }

  /// Get environment name
  static String get name {
    switch (_currentEnvironment) {
      case EnvironmentType.production:
        return 'Production';
      case EnvironmentType.staging:
        return 'Staging';
      case EnvironmentType.development:
        return 'Development';
      case EnvironmentType.unknown:
        return 'Unknown';
    }
  }

  /// Detect environment from multiple sources
  static Future<EnvironmentType> _detectEnvironment() async {
    try {
      // Check build mode first
      if (kReleaseMode) {
        // In release mode, check additional indicators
        return await _detectProductionEnvironment();
      } else if (kProfileMode) {
        return EnvironmentType.staging;
      } else {
        return EnvironmentType.development;
      }
    } catch (e) {
      print('❌ Environment detection failed: $e');
      return EnvironmentType.unknown;
    }
  }

  /// Detect production environment with additional checks
  static Future<EnvironmentType> _detectProductionEnvironment() async {
    try {
      // Check environment variables
      final envVar = Platform.environment['FLUTTER_ENV'];
      if (envVar == 'production') {
        return EnvironmentType.production;
      }

      // Check dart defines
      const environment = String.fromEnvironment('ENV', defaultValue: 'unknown');
      if (environment == 'production') {
        return EnvironmentType.production;
      }

      // Check package name/bundle ID for production indicators
      if (await _hasProductionPackageName()) {
        return EnvironmentType.production;
      }

      // Check for production certificates/signing
      if (await _hasProductionSigning()) {
        return EnvironmentType.production;
      }

      // Default to staging for release builds without production indicators
      return EnvironmentType.staging;

    } catch (e) {
      print('❌ Production environment detection failed: $e');
      return EnvironmentType.unknown;
    }
  }

  /// Check if app has production package name
  static Future<bool> _hasProductionPackageName() async {
    try {
      // This would be implemented with platform-specific code
      // For now, check dart defines
      const bundleId = String.fromEnvironment('BUNDLE_ID', defaultValue: '');
      return bundleId.contains('.real') || bundleId.contains('.prod');
    } catch (e) {
      return false;
    }
  }

  /// Check if app has production signing
  static Future<bool> _hasProductionSigning() async {
    try {
      // This would check certificate/keystore information
      // For now, return true for release builds
      return kReleaseMode;
    } catch (e) {
      return false;
    }
  }

  /// Load environment-specific configuration
  static Future<Map<String, dynamic>> _loadEnvironmentConfig() async {
    try {
      final config = <String, dynamic>{};

      // Load configuration based on environment
      switch (_currentEnvironment) {
        case EnvironmentType.production:
          config.addAll(await _loadProductionConfig());
          break;
        case EnvironmentType.staging:
          config.addAll(await _loadStagingConfig());
          break;
        case EnvironmentType.development:
          config.addAll(await _loadDevelopmentConfig());
          break;
        case EnvironmentType.unknown:
          config.addAll(await _loadDefaultConfig());
          break;
      }

      return config;

    } catch (e) {
      print('❌ Failed to load environment config: $e');
      return {};
    }
  }

  /// Load production configuration
  static Future<Map<String, dynamic>> _loadProductionConfig() async {
    return {
      'apiBaseUrl': 'https://api.paymule.zm/v1',
      'mtnEndpoint': 'https://api.mtn.com/v1',
      'airtelEndpoint': 'https://api.airtel.africa/v1',
      'zamtelEndpoint': 'https://api.zamtel.zm/kwacha/v1',
      'enableLogging': false,
      'enableDebugMode': false,
      'enableDemoMode': false,
      'enableTestMode': false,
      'apiTimeout': 30000,
      'retryAttempts': 3,
      'enableCrashReporting': true,
      'enableAnalytics': true,
      'enablePushNotifications': true,
      'requireSSL': true,
      'certificatePinning': true,
      'environment': 'production',
    };
  }

  /// Load staging configuration
  static Future<Map<String, dynamic>> _loadStagingConfig() async {
    return {
      'apiBaseUrl': 'https://staging-api.paymule.zm/v1',
      'mtnEndpoint': 'https://staging-api.mtn.com/v1',
      'airtelEndpoint': 'https://staging-api.airtel.africa/v1',
      'zamtelEndpoint': 'https://staging-api.zamtel.zm/kwacha/v1',
      'enableLogging': true,
      'enableDebugMode': true,
      'enableDemoMode': false,
      'enableTestMode': true,
      'apiTimeout': 45000,
      'retryAttempts': 5,
      'enableCrashReporting': true,
      'enableAnalytics': false,
      'enablePushNotifications': false,
      'requireSSL': true,
      'certificatePinning': false,
      'environment': 'staging',
    };
  }

  /// Load development configuration
  static Future<Map<String, dynamic>> _loadDevelopmentConfig() async {
    return {
      'apiBaseUrl': 'http://localhost:3000/v1',
      'mtnEndpoint': 'http://localhost:3001/mtn/v1',
      'airtelEndpoint': 'http://localhost:3002/airtel/v1',
      'zamtelEndpoint': 'http://localhost:3003/zamtel/v1',
      'enableLogging': true,
      'enableDebugMode': true,
      'enableDemoMode': true,
      'enableTestMode': true,
      'apiTimeout': 60000,
      'retryAttempts': 10,
      'enableCrashReporting': false,
      'enableAnalytics': false,
      'enablePushNotifications': false,
      'requireSSL': false,
      'certificatePinning': false,
      'environment': 'development',
    };
  }

  /// Load default configuration
  static Future<Map<String, dynamic>> _loadDefaultConfig() async {
    return {
      'apiBaseUrl': '',
      'mtnEndpoint': '',
      'airtelEndpoint': '',
      'zamtelEndpoint': '',
      'enableLogging': false,
      'enableDebugMode': false,
      'enableDemoMode': false,
      'enableTestMode': false,
      'apiTimeout': 30000,
      'retryAttempts': 3,
      'enableCrashReporting': false,
      'enableAnalytics': false,
      'enablePushNotifications': false,
      'requireSSL': true,
      'certificatePinning': true,
      'environment': 'unknown',
    };
  }

  /// Ensure environment is initialized
  static void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('Environment not initialized. Call Environment.initialize() first.');
    }
  }

  /// Force set environment (for testing)
  static void setEnvironment(EnvironmentType environment, {Map<String, dynamic>? config}) {
    _currentEnvironment = environment;
    if (config != null) {
      _environmentConfig = config;
    }
    _isInitialized = true;
  }

  /// Get environment summary
  static Map<String, dynamic> getSummary() {
    _ensureInitialized();
    return {
      'environment': _currentEnvironment.toString(),
      'name': name,
      'isProduction': isProduction,
      'isDevelopment': isDevelopment,
      'isStaging': isStaging,
      'isRelease': kReleaseMode,
      'isProfile': kProfileMode,
      'isDebug': kDebugMode,
      'configKeys': _environmentConfig.keys.toList(),
    };
  }

  /// Validate environment requirements
  static bool validateRequirements({
    bool requireProduction = false,
    bool requireStaging = false,
    bool requireDevelopment = false,
  }) {
    _ensureInitialized();

    if (requireProduction && !isProduction) return false;
    if (requireStaging && !isStaging) return false;
    if (requireDevelopment && !isDevelopment) return false;

    return true;
  }
}
