// IN-APP VALIDATOR
// Comprehensive production validation with assertions

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'environment_validator.dart';
import 'demo_mode_validator.dart';
import 'mtn_service_validator.dart';

class ValidationException implements Exception {
  final String message;
  final String code;
  final Map<String, dynamic>? details;

  ValidationException(this.message, {this.code = 'VALIDATION_ERROR', this.details});

  @override
  String toString() => 'ValidationException: $message (Code: $code)';
}

class ValidationResult {
  final bool isValid;
  final String? message;
  final String? code;
  final Map<String, dynamic>? details;

  ValidationResult({
    required this.isValid,
    this.message,
    this.code,
    this.details,
  });

  ValidationResult.success() : this(isValid: true);
  
  ValidationResult.failure(String message, {String? code, Map<String, dynamic>? details})
      : this(isValid: false, message: message, code: code, details: details);
}

class InAppValidator {
  static bool _isInitialized = false;
  static Map<String, ValidationResult> _validationResults = {};

  /// Initialize all validation systems
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🔍 Initializing in-app validation system...');

      // Initialize all validators
      await Environment.initialize();
      await DemoMode.initialize();
      await MTNService.initialize();

      _isInitialized = true;
      print('✅ In-app validation system initialized');

    } catch (e) {
      print('❌ Validation system initialization failed: $e');
      throw ValidationException(
        'Failed to initialize validation system: ${e.toString()}',
        code: 'INIT_ERROR',
      );
    }
  }

  /// Perform comprehensive production validation
  static Future<ValidationResult> validateProduction() async {
    try {
      _ensureInitialized();
      print('🔍 Performing comprehensive production validation...');

      final validations = <String, ValidationResult>{};

      // Environment validation
      validations['environment'] = _validateEnvironment();

      // Demo mode validation
      validations['demo_mode'] = _validateDemoMode();

      // MTN service validation
      validations['mtn_service'] = _validateMTNService();

      // Additional production checks
      validations['build_mode'] = _validateBuildMode();
      validations['debug_flags'] = _validateDebugFlags();
      validations['security'] = await _validateSecurity();

      // Store results
      _validationResults = validations;

      // Check if all validations passed
      final allValid = validations.values.every((result) => result.isValid);

      if (allValid) {
        print('✅ All production validations passed');
        return ValidationResult.success();
      } else {
        final failures = validations.entries
            .where((entry) => !entry.value.isValid)
            .map((entry) => '${entry.key}: ${entry.value.message}')
            .join(', ');

        print('❌ Production validation failures: $failures');
        return ValidationResult.failure(
          'Production validation failed: $failures',
          code: 'PRODUCTION_VALIDATION_FAILED',
          details: validations,
        );
      }

    } catch (e) {
      print('❌ Production validation error: $e');
      return ValidationResult.failure(
        'Production validation error: ${e.toString()}',
        code: 'VALIDATION_ERROR',
      );
    }
  }

  /// Validate environment requirements
  static ValidationResult _validateEnvironment() {
    try {
      if (!Environment.isProduction) {
        return ValidationResult.failure(
          'Must be production environment, current: ${Environment.name}',
          code: 'NOT_PRODUCTION',
          details: Environment.getSummary(),
        );
      }

      return ValidationResult.success();
    } catch (e) {
      return ValidationResult.failure(
        'Environment validation failed: ${e.toString()}',
        code: 'ENV_ERROR',
      );
    }
  }

  /// Validate demo mode is disabled
  static ValidationResult _validateDemoMode() {
    try {
      if (DemoMode.active) {
        return ValidationResult.failure(
          'Demo mode must be disabled in production',
          code: 'DEMO_MODE_ACTIVE',
          details: DemoMode.getSummary(),
        );
      }

      return ValidationResult.success();
    } catch (e) {
      return ValidationResult.failure(
        'Demo mode validation failed: ${e.toString()}',
        code: 'DEMO_ERROR',
      );
    }
  }

  /// Validate MTN service is using real endpoint
  static ValidationResult _validateMTNService() {
    try {
      if (!MTNService.isRealEndpoint) {
        return ValidationResult.failure(
          'MTN service must use real production endpoint, current: ${MTNService.currentEndpoint}',
          code: 'MTN_NOT_REAL',
          details: MTNService.getSummary(),
        );
      }

      return ValidationResult.success();
    } catch (e) {
      return ValidationResult.failure(
        'MTN service validation failed: ${e.toString()}',
        code: 'MTN_ERROR',
      );
    }
  }

  /// Validate build mode
  static ValidationResult _validateBuildMode() {
    try {
      if (!kReleaseMode) {
        return ValidationResult.failure(
          'Must be release build mode for production',
          code: 'NOT_RELEASE_MODE',
          details: {
            'isRelease': kReleaseMode,
            'isProfile': kProfileMode,
            'isDebug': kDebugMode,
          },
        );
      }

      return ValidationResult.success();
    } catch (e) {
      return ValidationResult.failure(
        'Build mode validation failed: ${e.toString()}',
        code: 'BUILD_ERROR',
      );
    }
  }

  /// Validate debug flags are disabled
  static ValidationResult _validateDebugFlags() {
    try {
      const debugFlags = {
        'DEBUG_MODE': String.fromEnvironment('DEBUG_MODE', defaultValue: 'false'),
        'TEST_MODE': String.fromEnvironment('TEST_MODE', defaultValue: 'false'),
        'VERBOSE_LOGGING': String.fromEnvironment('VERBOSE_LOGGING', defaultValue: 'false'),
      };

      final activeFlags = debugFlags.entries
          .where((entry) => entry.value.toLowerCase() == 'true')
          .map((entry) => entry.key)
          .toList();

      if (activeFlags.isNotEmpty) {
        return ValidationResult.failure(
          'Debug flags must be disabled in production: ${activeFlags.join(', ')}',
          code: 'DEBUG_FLAGS_ACTIVE',
          details: {'activeFlags': activeFlags, 'allFlags': debugFlags},
        );
      }

      return ValidationResult.success();
    } catch (e) {
      return ValidationResult.failure(
        'Debug flags validation failed: ${e.toString()}',
        code: 'DEBUG_ERROR',
      );
    }
  }

  /// Validate security requirements
  static Future<ValidationResult> _validateSecurity() async {
    try {
      final securityChecks = <String, bool>{};

      // Check SSL/HTTPS requirements
      securityChecks['https_required'] = MTNService.currentEndpoint.startsWith('https://');

      // Check certificate pinning (if available)
      securityChecks['cert_pinning'] = Environment.config['certificatePinning'] ?? false;

      // Check encryption settings
      securityChecks['encryption_enabled'] = Environment.config['requireSSL'] ?? true;

      final failedChecks = securityChecks.entries
          .where((entry) => !entry.value)
          .map((entry) => entry.key)
          .toList();

      if (failedChecks.isNotEmpty) {
        return ValidationResult.failure(
          'Security validation failed: ${failedChecks.join(', ')}',
          code: 'SECURITY_FAILED',
          details: {'failedChecks': failedChecks, 'allChecks': securityChecks},
        );
      }

      return ValidationResult.success();
    } catch (e) {
      return ValidationResult.failure(
        'Security validation failed: ${e.toString()}',
        code: 'SECURITY_ERROR',
      );
    }
  }

  /// Assert production environment (throws exception if not valid)
  static void assertProduction() {
    _ensureInitialized();
    
    if (!Environment.isProduction) {
      throw ValidationException(
        'Must be production mode, current: ${Environment.name}',
        code: 'NOT_PRODUCTION',
        details: Environment.getSummary(),
      );
    }
  }

  /// Assert demo mode is disabled (throws exception if active)
  static void assertDemoModeDisabled() {
    _ensureInitialized();
    
    if (DemoMode.active) {
      throw ValidationException(
        'Demo mode must be disabled',
        code: 'DEMO_MODE_ACTIVE',
        details: DemoMode.getSummary(),
      );
    }
  }

  /// Assert MTN service is using real endpoint (throws exception if not)
  static void assertMTNRealEndpoint() {
    _ensureInitialized();
    
    if (!MTNService.isRealEndpoint) {
      throw ValidationException(
        'MTN service must use real production endpoint',
        code: 'MTN_NOT_REAL',
        details: MTNService.getSummary(),
      );
    }
  }

  /// Perform all production assertions
  static void assertAllProduction() {
    print('🔍 Performing production assertions...');
    
    // Assert production environment
    assertProduction();
    print('✅ Environment.isProduction: true');

    // Assert demo mode disabled
    assertDemoModeDisabled();
    print('✅ !DemoMode.active: true');

    // Assert MTN real endpoint
    assertMTNRealEndpoint();
    print('✅ MTNService.isRealEndpoint: true');

    print('🎉 All production assertions passed!');
  }

  /// Get validation summary
  static Map<String, dynamic> getValidationSummary() {
    _ensureInitialized();
    
    return {
      'environment': Environment.getSummary(),
      'demoMode': DemoMode.getSummary(),
      'mtnService': MTNService.getSummary(),
      'validationResults': _validationResults.map(
        (key, value) => MapEntry(key, {
          'isValid': value.isValid,
          'message': value.message,
          'code': value.code,
        }),
      ),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Get validation results
  static Map<String, ValidationResult> getValidationResults() {
    return Map.from(_validationResults);
  }

  /// Check if all validations are passing
  static bool get isValid {
    return _validationResults.values.every((result) => result.isValid);
  }

  /// Ensure validator is initialized
  static void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('InAppValidator not initialized. Call InAppValidator.initialize() first.');
    }
  }

  /// Reset validation state (for testing)
  static void reset() {
    _isInitialized = false;
    _validationResults.clear();
  }
}
