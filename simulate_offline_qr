#!/usr/bin/env python3

"""
Offline QR Payment Simulation for Zambian Mobile Money
Simulates network outages and validates offline transaction handling
Tests receipt generation and transaction queuing
"""

import argparse
import json
import time
import random
import sqlite3
import hashlib
from datetime import datetime, timedelta
from pathlib import Path
import subprocess
import sys

# Colors for terminal output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'  # No Color

def print_status(message):
    print(f"{Colors.BLUE}[INFO]{Colors.NC} {message}")

def print_success(message):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {message}")

def print_warning(message):
    print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {message}")

def print_error(message):
    print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")

def print_header(message):
    print(f"\n{Colors.PURPLE}{'='*60}{Colors.NC}")
    print(f"{Colors.PURPLE}{message.center(60)}{Colors.NC}")
    print(f"{Colors.PURPLE}{'='*60}{Colors.NC}\n")

class OfflineQRSimulator:
    def __init__(self, output_dir="test_results/offline_qr"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.db_path = self.output_dir / "offline_simulation.db"
        self.results_file = self.output_dir / "simulation_results.json"
        
        # Zambian mobile money providers
        self.providers = ["MTN", "AIRTEL", "ZAMTEL"]
        
        # Typical Zambian transaction amounts
        self.typical_amounts = [5, 10, 15, 20, 25, 30, 50, 75, 100, 150, 200, 250, 500, 1000]
        
        # Merchant categories common in Zambia
        self.merchant_categories = [
            "RETAIL", "TRANSPORT", "FOOD", "MARKET", "SERVICES", "UTILITIES"
        ]
        
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database for simulation"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables for simulation
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS offline_transactions (
                id TEXT PRIMARY KEY,
                merchant_id TEXT NOT NULL,
                amount REAL NOT NULL,
                currency TEXT DEFAULT 'ZMW',
                provider TEXT NOT NULL,
                category TEXT NOT NULL,
                created_at INTEGER NOT NULL,
                status TEXT DEFAULT 'OFFLINE_PENDING',
                receipt_generated INTEGER DEFAULT 0,
                synced_at INTEGER,
                network_outage_duration INTEGER
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS receipts (
                id TEXT PRIMARY KEY,
                transaction_id TEXT NOT NULL,
                receipt_type TEXT NOT NULL,
                content TEXT NOT NULL,
                created_at INTEGER NOT NULL,
                FOREIGN KEY (transaction_id) REFERENCES offline_transactions (id)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS network_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                event_type TEXT NOT NULL,
                timestamp INTEGER NOT NULL,
                duration_seconds INTEGER,
                description TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        
        print_success("Database initialized")
    
    def generate_transaction_id(self):
        """Generate unique transaction ID"""
        timestamp = str(int(time.time() * 1000))
        random_part = str(random.randint(1000, 9999))
        return f"zm_txn_{timestamp}_{random_part}"
    
    def generate_merchant_id(self, category):
        """Generate merchant ID based on category"""
        category_prefix = category[:3].lower()
        random_id = random.randint(1000, 9999)
        return f"zm_{category_prefix}_merchant_{random_id}"
    
    def create_offline_transaction(self):
        """Create a simulated offline transaction"""
        transaction_id = self.generate_transaction_id()
        category = random.choice(self.merchant_categories)
        merchant_id = self.generate_merchant_id(category)
        amount = random.choice(self.typical_amounts)
        provider = random.choice(self.providers)
        
        transaction = {
            'id': transaction_id,
            'merchant_id': merchant_id,
            'amount': amount,
            'currency': 'ZMW',
            'provider': provider,
            'category': category,
            'created_at': int(time.time()),
            'status': 'OFFLINE_PENDING',
            'receipt_generated': 0
        }
        
        return transaction
    
    def store_transaction(self, transaction, outage_duration):
        """Store transaction in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO offline_transactions 
            (id, merchant_id, amount, currency, provider, category, created_at, status, network_outage_duration)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            transaction['id'],
            transaction['merchant_id'],
            transaction['amount'],
            transaction['currency'],
            transaction['provider'],
            transaction['category'],
            transaction['created_at'],
            transaction['status'],
            outage_duration
        ))
        
        conn.commit()
        conn.close()
    
    def generate_receipt(self, transaction):
        """Generate receipt for offline transaction"""
        receipt_id = f"receipt_{transaction['id']}"
        
        # Generate SMS receipt
        sms_receipt = self.generate_sms_receipt(transaction)
        
        # Generate visual receipt data
        visual_receipt = self.generate_visual_receipt(transaction)
        
        # Store receipts
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO receipts (id, transaction_id, receipt_type, content, created_at)
            VALUES (?, ?, ?, ?, ?)
        ''', (f"{receipt_id}_sms", transaction['id'], 'SMS', sms_receipt, int(time.time())))
        
        cursor.execute('''
            INSERT INTO receipts (id, transaction_id, receipt_type, content, created_at)
            VALUES (?, ?, ?, ?, ?)
        ''', (f"{receipt_id}_visual", transaction['id'], 'VISUAL', visual_receipt, int(time.time())))
        
        # Mark transaction as having receipt generated
        cursor.execute('''
            UPDATE offline_transactions 
            SET receipt_generated = 1 
            WHERE id = ?
        ''', (transaction['id'],))
        
        conn.commit()
        conn.close()
        
        return receipt_id
    
    def generate_sms_receipt(self, transaction):
        """Generate SMS receipt content"""
        timestamp = datetime.fromtimestamp(transaction['created_at'])
        
        sms_content = f"""PAY MULE RECEIPT
================
ID: {transaction['id'][:8]}
Amount: K{transaction['amount']:.2f}
Provider: {transaction['provider']}
Date: {timestamp.strftime('%d/%m/%y %H:%M')}
Status: OFFLINE - Will process when online
================
Keep this SMS for your records"""
        
        return sms_content
    
    def generate_visual_receipt(self, transaction):
        """Generate visual receipt data (JSON format)"""
        timestamp = datetime.fromtimestamp(transaction['created_at'])
        
        visual_data = {
            'header': 'PAY MULE ZAMBIA',
            'subtitle': 'Mobile Money Receipt',
            'transaction_id': transaction['id'],
            'amount': f"K{transaction['amount']:.2f} {transaction['currency']}",
            'merchant_id': transaction['merchant_id'],
            'provider': transaction['provider'],
            'category': transaction['category'],
            'timestamp': timestamp.isoformat(),
            'status': 'OFFLINE TRANSACTION',
            'footer': 'Keep this receipt for your records'
        }
        
        return json.dumps(visual_data, indent=2)
    
    def simulate_network_outage(self, duration_hours):
        """Simulate network outage"""
        duration_seconds = duration_hours * 3600
        
        print_status(f"🌐 Simulating network outage for {duration_hours} hours")
        
        # Log network outage start
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO network_events (event_type, timestamp, duration_seconds, description)
            VALUES (?, ?, ?, ?)
        ''', ('OUTAGE_START', int(time.time()), duration_seconds, f'Network outage simulation for {duration_hours} hours'))
        conn.commit()
        conn.close()
        
        return duration_seconds
    
    def simulate_network_restoration(self):
        """Simulate network restoration"""
        print_status("🌐 Simulating network restoration")
        
        # Log network restoration
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO network_events (event_type, timestamp, description)
            VALUES (?, ?, ?)
        ''', ('OUTAGE_END', int(time.time()), 'Network restored - beginning sync'))
        conn.commit()
        conn.close()
    
    def sync_offline_transactions(self):
        """Simulate syncing offline transactions when network is restored"""
        print_status("🔄 Syncing offline transactions...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get all pending offline transactions
        cursor.execute('''
            SELECT * FROM offline_transactions 
            WHERE status = 'OFFLINE_PENDING'
        ''')
        
        transactions = cursor.fetchall()
        synced_count = 0
        failed_count = 0
        
        for transaction in transactions:
            transaction_id = transaction[0]
            
            # Simulate sync success/failure (95% success rate)
            if random.random() < 0.95:
                # Successful sync
                cursor.execute('''
                    UPDATE offline_transactions 
                    SET status = 'SYNCED', synced_at = ? 
                    WHERE id = ?
                ''', (int(time.time()), transaction_id))
                synced_count += 1
                
                if synced_count % 5 == 0:
                    print_status(f"  Synced {synced_count} transactions...")
            else:
                # Failed sync
                cursor.execute('''
                    UPDATE offline_transactions 
                    SET status = 'SYNC_FAILED' 
                    WHERE id = ?
                ''', (transaction_id,))
                failed_count += 1
        
        conn.commit()
        conn.close()
        
        print_success(f"Sync completed: {synced_count} successful, {failed_count} failed")
        return synced_count, failed_count
    
    def validate_receipts(self):
        """Validate that receipts were generated correctly"""
        print_status("🧾 Validating receipts...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Check receipt generation
        cursor.execute('''
            SELECT 
                COUNT(*) as total_transactions,
                SUM(receipt_generated) as transactions_with_receipts
            FROM offline_transactions
        ''')
        
        result = cursor.fetchone()
        total_transactions = result[0]
        transactions_with_receipts = result[1]
        
        # Check receipt types
        cursor.execute('''
            SELECT receipt_type, COUNT(*) 
            FROM receipts 
            GROUP BY receipt_type
        ''')
        
        receipt_types = dict(cursor.fetchall())
        
        conn.close()
        
        receipt_coverage = (transactions_with_receipts / total_transactions * 100) if total_transactions > 0 else 0
        
        print_success(f"Receipt validation completed:")
        print(f"  Total transactions: {total_transactions}")
        print(f"  Transactions with receipts: {transactions_with_receipts}")
        print(f"  Receipt coverage: {receipt_coverage:.1f}%")
        print(f"  SMS receipts: {receipt_types.get('SMS', 0)}")
        print(f"  Visual receipts: {receipt_types.get('VISUAL', 0)}")
        
        return {
            'total_transactions': total_transactions,
            'transactions_with_receipts': transactions_with_receipts,
            'receipt_coverage': receipt_coverage,
            'receipt_types': receipt_types
        }
    
    def generate_report(self, simulation_params, results):
        """Generate comprehensive simulation report"""
        report = {
            'simulation_metadata': {
                'timestamp': datetime.now().isoformat(),
                'parameters': simulation_params,
                'database_path': str(self.db_path),
                'output_directory': str(self.output_dir)
            },
            'results': results,
            'summary': {
                'total_transactions': results['transaction_stats']['total'],
                'successful_syncs': results['sync_stats']['synced'],
                'failed_syncs': results['sync_stats']['failed'],
                'receipt_coverage': results['receipt_validation']['receipt_coverage'],
                'success_rate': (results['sync_stats']['synced'] / results['transaction_stats']['total'] * 100) if results['transaction_stats']['total'] > 0 else 0
            }
        }
        
        # Save report
        with open(self.results_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Generate markdown report
        markdown_report = self.generate_markdown_report(report)
        with open(self.output_dir / "simulation_report.md", 'w') as f:
            f.write(markdown_report)
        
        print_success(f"Reports saved to {self.output_dir}")
        
        return report
    
    def generate_markdown_report(self, report):
        """Generate markdown report"""
        summary = report['summary']
        
        markdown = f"""# Offline QR Payment Simulation Report

## Test Configuration
- **Transactions**: {report['simulation_metadata']['parameters']['transactions']}
- **Network Outage**: {report['simulation_metadata']['parameters']['network_outage']}
- **Test Date**: {report['simulation_metadata']['timestamp']}

## Summary
- **Total Transactions**: {summary['total_transactions']}
- **Successful Syncs**: {summary['successful_syncs']}
- **Failed Syncs**: {summary['failed_syncs']}
- **Success Rate**: {summary['success_rate']:.1f}%
- **Receipt Coverage**: {summary['receipt_coverage']:.1f}%

## Transaction Distribution
"""
        
        # Add provider distribution
        provider_stats = report['results']['transaction_stats']['by_provider']
        markdown += "\n### By Provider\n"
        for provider, count in provider_stats.items():
            percentage = (count / summary['total_transactions'] * 100)
            markdown += f"- **{provider}**: {count} transactions ({percentage:.1f}%)\n"
        
        # Add category distribution
        category_stats = report['results']['transaction_stats']['by_category']
        markdown += "\n### By Category\n"
        for category, count in category_stats.items():
            percentage = (count / summary['total_transactions'] * 100)
            markdown += f"- **{category}**: {count} transactions ({percentage:.1f}%)\n"
        
        markdown += f"""
## Performance Metrics
- **Average Transaction Amount**: K{report['results']['transaction_stats']['avg_amount']:.2f}
- **Total Transaction Value**: K{report['results']['transaction_stats']['total_amount']:.2f}
- **Sync Success Rate**: {summary['success_rate']:.1f}%

## Recommendations
"""
        
        if summary['success_rate'] < 95:
            markdown += "- Sync success rate below 95%. Consider improving error handling.\n"
        
        if summary['receipt_coverage'] < 100:
            markdown += "- Receipt coverage incomplete. Ensure all transactions generate receipts.\n"
        
        if summary['success_rate'] >= 95 and summary['receipt_coverage'] >= 95:
            markdown += "- All metrics within acceptable ranges. System performing well.\n"
        
        return markdown
    
    def run_simulation(self, num_transactions, network_outage_hours, validate_receipts_flag):
        """Run the complete offline QR simulation"""
        print_header("🇿🇲 OFFLINE QR PAYMENT SIMULATION")
        
        simulation_params = {
            'transactions': num_transactions,
            'network_outage': f"{network_outage_hours}h",
            'validate_receipts': validate_receipts_flag
        }
        
        print_status(f"Configuration:")
        print(f"  Transactions: {num_transactions}")
        print(f"  Network outage: {network_outage_hours} hours")
        print(f"  Validate receipts: {validate_receipts_flag}")
        
        # Step 1: Simulate network outage
        outage_duration = self.simulate_network_outage(network_outage_hours)
        
        # Step 2: Generate offline transactions
        print_status(f"💳 Generating {num_transactions} offline transactions...")
        
        transactions_created = 0
        for i in range(num_transactions):
            transaction = self.create_offline_transaction()
            self.store_transaction(transaction, outage_duration)
            
            # Generate receipt
            receipt_id = self.generate_receipt(transaction)
            
            transactions_created += 1
            
            if (i + 1) % 5 == 0:
                print_status(f"  Created {i + 1} transactions...")
            
            # Small delay to simulate real-world timing
            time.sleep(0.1)
        
        print_success(f"Created {transactions_created} offline transactions")
        
        # Step 3: Simulate network restoration and sync
        self.simulate_network_restoration()
        synced_count, failed_count = self.sync_offline_transactions()
        
        # Step 4: Validate receipts if requested
        receipt_validation = {}
        if validate_receipts_flag:
            receipt_validation = self.validate_receipts()
        
        # Step 5: Generate statistics
        results = self.generate_statistics()
        results['sync_stats'] = {
            'synced': synced_count,
            'failed': failed_count
        }
        if validate_receipts_flag:
            results['receipt_validation'] = receipt_validation
        
        # Step 6: Generate report
        report = self.generate_report(simulation_params, results)
        
        # Display summary
        print_header("SIMULATION SUMMARY")
        print_success(f"✅ Simulation completed successfully!")
        print(f"  📊 Transactions: {transactions_created}")
        print(f"  🔄 Synced: {synced_count}")
        print(f"  ❌ Failed: {failed_count}")
        print(f"  📈 Success Rate: {(synced_count / transactions_created * 100):.1f}%")
        
        if validate_receipts_flag:
            print(f"  🧾 Receipt Coverage: {receipt_validation['receipt_coverage']:.1f}%")
        
        return report
    
    def generate_statistics(self):
        """Generate detailed statistics from simulation"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Overall stats
        cursor.execute('SELECT COUNT(*), AVG(amount), SUM(amount) FROM offline_transactions')
        total, avg_amount, total_amount = cursor.fetchone()
        
        # By provider
        cursor.execute('SELECT provider, COUNT(*) FROM offline_transactions GROUP BY provider')
        by_provider = dict(cursor.fetchall())
        
        # By category
        cursor.execute('SELECT category, COUNT(*) FROM offline_transactions GROUP BY category')
        by_category = dict(cursor.fetchall())
        
        # By status
        cursor.execute('SELECT status, COUNT(*) FROM offline_transactions GROUP BY status')
        by_status = dict(cursor.fetchall())
        
        conn.close()
        
        return {
            'transaction_stats': {
                'total': total,
                'avg_amount': avg_amount or 0,
                'total_amount': total_amount or 0,
                'by_provider': by_provider,
                'by_category': by_category,
                'by_status': by_status
            }
        }

def parse_duration(duration_str):
    """Parse duration string (e.g., '48h', '2d', '30m')"""
    duration_str = duration_str.lower().strip()
    
    if duration_str.endswith('h'):
        return float(duration_str[:-1])
    elif duration_str.endswith('d'):
        return float(duration_str[:-1]) * 24
    elif duration_str.endswith('m'):
        return float(duration_str[:-1]) / 60
    else:
        # Assume hours if no unit specified
        return float(duration_str)

def main():
    parser = argparse.ArgumentParser(
        description='🇿🇲 Offline QR Payment Simulation for Zambian Mobile Money',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --transactions=15 --network-outage=48h --validate-receipts
  %(prog)s --transactions=50 --network-outage=2d
  %(prog)s --transactions=100 --network-outage=30m --output-dir=custom_results
        """
    )
    
    parser.add_argument('--transactions', type=int, default=15,
                       help='Number of offline transactions to simulate (default: 15)')
    
    parser.add_argument('--network-outage', type=str, default='48h',
                       help='Duration of network outage (e.g., 48h, 2d, 30m) (default: 48h)')
    
    parser.add_argument('--validate-receipts', action='store_true',
                       help='Validate receipt generation')
    
    parser.add_argument('--output-dir', type=str, default='test_results/offline_qr',
                       help='Output directory for results (default: test_results/offline_qr)')
    
    args = parser.parse_args()
    
    # Parse network outage duration
    try:
        outage_hours = parse_duration(args.network_outage)
    except ValueError:
        print_error(f"Invalid network outage duration: {args.network_outage}")
        sys.exit(1)
    
    # Run simulation
    simulator = OfflineQRSimulator(args.output_dir)
    report = simulator.run_simulation(
        num_transactions=args.transactions,
        network_outage_hours=outage_hours,
        validate_receipts_flag=args.validate_receipts
    )
    
    print_status(f"📁 Results saved to: {args.output_dir}")

if __name__ == '__main__':
    main()
