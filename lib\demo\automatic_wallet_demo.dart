// AUTOMATIC WALLET DETECTION DEMO
// Demonstrates: final dominantWallet = MobileMoney.detectDominantProvider(); WalletService.activate(dominantWallet);

import 'package:flutter/material.dart';
import 'dart:async';
import '../core/wallet/mobile_money_detector.dart';
import '../core/wallet/wallet_service.dart';
import '../core/wallet/automatic_wallet_flow.dart';

class AutomaticWalletDemo extends StatefulWidget {
  @override
  _AutomaticWalletDemoState createState() => _AutomaticWalletDemoState();
}

class _AutomaticWalletDemoState extends State<AutomaticWalletDemo> {
  bool isDetecting = false;
  bool isActivating = false;
  String statusMessage = 'Ready to detect mobile money providers';
  
  MobileMoneyProvider? detectedProvider;
  WalletDetectionResult? detectionResult;
  WalletActivationResult? activationResult;
  ActiveWallet? activeWallet;
  
  List<String> progressMessages = [];
  StreamSubscription? progressSubscription;

  @override
  void initState() {
    super.initState();
    _setupProgressListener();
    _checkExistingWallet();
  }

  @override
  void dispose() {
    progressSubscription?.cancel();
    super.dispose();
  }

  /// Setup progress listener
  void _setupProgressListener() {
    progressSubscription = AutomaticWalletFlow.progressStream.listen((message) {
      setState(() {
        progressMessages.add('${DateTime.now().toLocal().toString().substring(11, 19)}: $message');
        if (progressMessages.length > 10) {
          progressMessages.removeAt(0);
        }
      });
    });
  }

  /// Check for existing active wallet
  Future<void> _checkExistingWallet() async {
    final wallet = WalletService.getActiveWallet();
    if (wallet != null) {
      setState(() {
        activeWallet = wallet;
        statusMessage = '${wallet.displayName} is already active';
      });
    }
  }

  /// Execute automatic wallet detection and activation
  Future<void> _executeAutomaticFlow() async {
    setState(() {
      isDetecting = true;
      isActivating = true;
      statusMessage = 'Starting automatic wallet detection...';
      progressMessages.clear();
    });

    try {
      // EXPECTED FLOW IMPLEMENTATION
      print('🤖 Executing: final dominantWallet = MobileMoney.detectDominantProvider();');
      print('🤖 Executing: WalletService.activate(dominantWallet);');

      final result = await AutomaticWalletFlow.executeWithProgress(
        onProgress: (message) {
          setState(() {
            statusMessage = message;
          });
        },
        onDetectionComplete: (result) {
          setState(() {
            detectionResult = result;
            detectedProvider = result.dominantProvider;
            isDetecting = false;
          });
        },
        onActivationComplete: (result) {
          setState(() {
            activationResult = result;
            isActivating = false;
          });
        },
      );

      if (result.success) {
        setState(() {
          activeWallet = WalletService.getActiveWallet();
          statusMessage = '✅ Automatic wallet activation completed!';
        });
      } else {
        setState(() {
          statusMessage = '❌ ${result.message}';
        });
      }

    } catch (e) {
      setState(() {
        isDetecting = false;
        isActivating = false;
        statusMessage = '❌ Error: ${e.toString()}';
      });
    }
  }

  /// Manual provider detection only
  Future<void> _detectProvidersOnly() async {
    setState(() {
      isDetecting = true;
      statusMessage = 'Detecting mobile money providers...';
    });

    try {
      // final dominantWallet = MobileMoney.detectDominantProvider();
      final dominantWallet = await MobileMoney.detectDominantProvider();
      final allProviders = await MobileMoney.detectAllProviders();

      setState(() {
        detectedProvider = dominantWallet;
        detectionResult = allProviders;
        isDetecting = false;
        statusMessage = dominantWallet != MobileMoneyProvider.unknown
            ? 'Detected: ${_getProviderDisplayName(dominantWallet)}'
            : 'No mobile money provider detected';
      });

    } catch (e) {
      setState(() {
        isDetecting = false;
        statusMessage = 'Detection failed: ${e.toString()}';
      });
    }
  }

  /// Manual wallet activation
  Future<void> _activateWallet(MobileMoneyProvider provider) async {
    setState(() {
      isActivating = true;
      statusMessage = 'Activating ${_getProviderDisplayName(provider)}...';
    });

    try {
      // WalletService.activate(dominantWallet);
      final result = await WalletService.activate(provider);

      setState(() {
        activationResult = result;
        isActivating = false;
        
        if (result.success) {
          activeWallet = WalletService.getActiveWallet();
          statusMessage = '✅ ${_getProviderDisplayName(provider)} activated successfully!';
        } else {
          statusMessage = '❌ Activation failed: ${result.message}';
        }
      });

    } catch (e) {
      setState(() {
        isActivating = false;
        statusMessage = 'Activation error: ${e.toString()}';
      });
    }
  }

  /// Switch to different provider
  Future<void> _switchProvider(MobileMoneyProvider provider) async {
    final result = await AutomaticWalletFlow.switchProvider(provider);
    
    if (result.success) {
      setState(() {
        activeWallet = WalletService.getActiveWallet();
        statusMessage = 'Switched to ${_getProviderDisplayName(provider)}';
      });
    } else {
      setState(() {
        statusMessage = 'Switch failed: ${result.message}';
      });
    }
  }

  /// Get provider display name
  String _getProviderDisplayName(MobileMoneyProvider provider) {
    switch (provider) {
      case MobileMoneyProvider.mtnMobileMoney:
        return 'MTN Mobile Money';
      case MobileMoneyProvider.airtelMoney:
        return 'Airtel Money';
      case MobileMoneyProvider.zamtelKwacha:
        return 'Zamtel Kwacha';
      case MobileMoneyProvider.unknown:
        return 'Unknown Provider';
    }
  }

  /// Get provider color
  Color _getProviderColor(MobileMoneyProvider provider) {
    switch (provider) {
      case MobileMoneyProvider.mtnMobileMoney:
        return Colors.yellow[700]!;
      case MobileMoneyProvider.airtelMoney:
        return Colors.red[600]!;
      case MobileMoneyProvider.zamtelKwacha:
        return Colors.green[600]!;
      case MobileMoneyProvider.unknown:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('🤖 Automatic Wallet Detection'),
        backgroundColor: Colors.blue[800],
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status card
            Card(
              color: Colors.blue[50],
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(
                      'Automatic Wallet Status',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text(
                      statusMessage,
                      style: TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                    if (isDetecting || isActivating) ...[
                      SizedBox(height: 16),
                      LinearProgressIndicator(),
                    ],
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: (isDetecting || isActivating) ? null : _executeAutomaticFlow,
                    icon: Icon(Icons.auto_awesome),
                    label: Text('Auto Detect & Activate'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green[600],
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: (isDetecting || isActivating) ? null : _detectProvidersOnly,
                    icon: Icon(Icons.search),
                    label: Text('Detect Only'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),

            // Active wallet display
            if (activeWallet != null) ...[
              Card(
                color: Colors.green[50],
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(Icons.account_balance_wallet, color: Colors.green[600]),
                          SizedBox(width: 8),
                          Text(
                            'Active Wallet',
                            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      SizedBox(height: 12),
                      _buildWalletInfo(activeWallet!),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 16),
            ],

            // Detection results
            if (detectionResult != null) ...[
              Text(
                'Detection Results',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Expanded(
                child: ListView(
                  children: [
                    // Dominant provider
                    Card(
                      color: Colors.orange[50],
                      child: ListTile(
                        leading: Icon(
                          Icons.star,
                          color: _getProviderColor(detectionResult!.dominantProvider),
                        ),
                        title: Text('Dominant Provider'),
                        subtitle: Text(
                          '${_getProviderDisplayName(detectionResult!.dominantProvider)} '
                          '(${(detectionResult!.confidence * 100).toStringAsFixed(1)}% confidence)',
                        ),
                        trailing: detectedProvider != null && activeWallet?.provider != detectedProvider
                            ? ElevatedButton(
                                onPressed: () => _activateWallet(detectedProvider!),
                                child: Text('Activate'),
                              )
                            : null,
                      ),
                    ),

                    // Available providers
                    ...detectionResult!.availableProviders.map((provider) {
                      final walletInfo = detectionResult!.walletDetails[provider];
                      return Card(
                        child: ListTile(
                          leading: Icon(
                            Icons.account_balance_wallet,
                            color: _getProviderColor(provider),
                          ),
                          title: Text(_getProviderDisplayName(provider)),
                          subtitle: walletInfo != null
                              ? Text('Usage Score: ${(walletInfo.usageScore * 100).toStringAsFixed(0)}%')
                              : null,
                          trailing: activeWallet?.provider != provider
                              ? TextButton(
                                  onPressed: () => _switchProvider(provider),
                                  child: Text('Switch'),
                                )
                              : Icon(Icons.check_circle, color: Colors.green),
                        ),
                      );
                    }).toList(),

                    // Progress messages
                    if (progressMessages.isNotEmpty) ...[
                      SizedBox(height: 16),
                      Text(
                        'Progress Log',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Container(
                        height: 150,
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ListView.builder(
                          itemCount: progressMessages.length,
                          itemBuilder: (context, index) {
                            return Text(
                              progressMessages[index],
                              style: TextStyle(fontSize: 12, fontFamily: 'monospace'),
                            );
                          },
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ] else ...[
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.account_balance_wallet, size: 80, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'No Detection Results',
                        style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Tap "Auto Detect & Activate" to start',
                        style: TextStyle(color: Colors.grey[500]),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildWalletInfo(ActiveWallet wallet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: _getProviderColor(wallet.provider),
                shape: BoxShape.circle,
              ),
            ),
            SizedBox(width: 8),
            Text(
              wallet.displayName,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        SizedBox(height: 8),
        Text('Service Code: ${wallet.serviceCode}'),
        Text('Status: ${wallet.status.toString().split('.').last}'),
        Text('Activated: ${wallet.activatedAt.toLocal().toString().substring(0, 19)}'),
        if (wallet.phoneNumber != null)
          Text('Phone: ${wallet.phoneNumber}'),
      ],
    );
  }
}
