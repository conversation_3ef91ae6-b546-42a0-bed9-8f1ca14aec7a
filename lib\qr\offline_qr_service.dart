/// Offline QR Payment Service for Zambian Rural Areas
/// Handles QR payments without internet connectivity
/// Syncs transactions when connection is restored

import 'dart:convert';
import 'dart:typed_data';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../core/constants/app_constants.dart';
import '../core/security/encryption_service.dart';
import '../data/database/database_helper.dart';
import '../features/offline_sync/data/offline_sync_manager.dart';
import '../features/rural_resilience/sms_token_service.dart';
import 'zambia_qr_format.dart';
import 'merchant_qr_service.dart';

/// Offline QR Payment Service
class OfflineQRService {
  static final OfflineQRService _instance = OfflineQRService._internal();
  factory OfflineQRService() => _instance;
  OfflineQRService._internal();

  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final EncryptionService _encryption = EncryptionService();
  final OfflineSyncManager _syncManager = OfflineSyncManager();
  final SMSTokenService _smsTokenService = SMSTokenService();
  final MerchantQRService _merchantService = MerchantQRService();

  bool _isInitialized = false;

  /// Initialize offline QR service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _encryption.initialize();
      await _createOfflineQRTables();
      await _setupOfflineSync();
      
      _isInitialized = true;
      _logger.i('📱 Offline QR service initialized');
    } catch (e) {
      _logger.e('Failed to initialize offline QR service: $e');
      rethrow;
    }
  }

  /// Process QR payment offline
  Future<OfflineQRResult> processOfflinePayment({
    required String qrData,
    required String payerUserId,
    required String pin,
    String? customAmount,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      // Decode QR data
      final decodedData = await ZambiaQRFormat.decode(qrData);
      
      // Validate offline payment
      final validation = await _validateOfflinePayment(
        decodedData: decodedData,
        payerUserId: payerUserId,
      );

      if (!validation.isValid) {
        return OfflineQRResult(
          success: false,
          error: validation.error,
          message: validation.message,
        );
      }

      // Create offline transaction
      final transactionId = _uuid.v4();
      final amount = customAmount != null 
          ? double.tryParse(customAmount) ?? decodedData.amount
          : decodedData.amount;

      final offlineTransaction = {
        'id': transactionId,
        'qr_data': qrData,
        'payer_user_id': payerUserId,
        'merchant_id': decodedData.merchantId,
        'amount': amount,
        'currency': decodedData.currency,
        'description': decodedData.description ?? 'Offline QR Payment',
        'provider': decodedData.provider,
        'pin_hash': await _hashPin(pin),
        'status': 'OFFLINE_PENDING',
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'network_type': await _detectNetworkType(),
        'location_data': await _getCachedLocationData(),
      };

      await _dbHelper.insert('offline_qr_transactions', offlineTransaction);

      // Generate offline confirmation token
      final confirmationToken = await _generateOfflineToken(transactionId);
      
      // Store for SMS fallback if available
      await _storeForSMSFallback(transactionId, decodedData, amount);

      // Queue for sync when online
      await _queueForSync(transactionId);

      _logger.i('📱 Offline QR payment created: $transactionId');

      return OfflineQRResult(
        success: true,
        transactionId: transactionId,
        amount: amount,
        merchantId: decodedData.merchantId,
        confirmationToken: confirmationToken,
        message: 'Payment saved offline. Will process when connection is available.',
      );
    } catch (e) {
      _logger.e('Offline QR payment failed: $e');
      return OfflineQRResult(
        success: false,
        error: 'OFFLINE_PAYMENT_FAILED',
        message: 'Failed to process offline payment: ${e.toString()}',
      );
    }
  }

  /// Generate offline QR code for merchant
  Future<OfflineQRGenerationResult> generateOfflineQR({
    required String merchantId,
    double? amount,
    String? description,
    Duration? validityDuration,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      // Get merchant info from local cache
      final merchant = await _getCachedMerchantInfo(merchantId);
      if (merchant == null) {
        return OfflineQRGenerationResult(
          success: false,
          error: 'MERCHANT_NOT_FOUND',
          message: 'Merchant not found in offline cache',
        );
      }

      // Create offline QR data with extended validity
      final qrId = _uuid.v4();
      final validity = validityDuration ?? const Duration(hours: 24);
      
      // Generate compact QR for offline use
      final qrPayload = await _createOfflineQRPayload(
        qrId: qrId,
        merchantId: merchantId,
        amount: amount,
        description: description,
        validity: validity,
      );

      // Create QR image
      final qrImage = await _generateQRImage(qrPayload);

      // Store offline QR record
      final offlineQR = {
        'id': qrId,
        'merchant_id': merchantId,
        'qr_payload': qrPayload,
        'amount': amount,
        'description': description,
        'validity_duration_seconds': validity.inSeconds,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'expires_at': DateTime.now().add(validity).millisecondsSinceEpoch,
        'scan_count': 0,
      };

      await _dbHelper.insert('offline_qr_codes', offlineQR);

      _logger.i('📱 Offline QR generated: $qrId');

      return OfflineQRGenerationResult(
        success: true,
        qrId: qrId,
        qrImage: qrImage,
        qrPayload: qrPayload,
        expiresAt: DateTime.now().add(validity),
        message: 'Offline QR code generated successfully',
      );
    } catch (e) {
      _logger.e('Offline QR generation failed: $e');
      return OfflineQRGenerationResult(
        success: false,
        error: 'GENERATION_FAILED',
        message: 'Failed to generate offline QR: ${e.toString()}',
      );
    }
  }

  /// Sync offline transactions when online
  Future<OfflineSyncResult> syncOfflineTransactions() async {
    try {
      if (!_isInitialized) await initialize();

      // Check connectivity
      final connectivity = await Connectivity().checkConnectivity();
      if (connectivity == ConnectivityResult.none) {
        return OfflineSyncResult(
          success: false,
          error: 'NO_CONNECTIVITY',
          message: 'No internet connection available',
        );
      }

      // Get pending offline transactions
      final pendingTransactions = await _getPendingOfflineTransactions();
      
      if (pendingTransactions.isEmpty) {
        return OfflineSyncResult(
          success: true,
          syncedCount: 0,
          message: 'No offline transactions to sync',
        );
      }

      int syncedCount = 0;
      int failedCount = 0;

      for (final transaction in pendingTransactions) {
        try {
          final result = await _syncSingleTransaction(transaction);
          if (result) {
            syncedCount++;
            await _markTransactionSynced(transaction['id'] as String);
          } else {
            failedCount++;
            await _markTransactionFailed(transaction['id'] as String);
          }
        } catch (e) {
          _logger.e('Failed to sync transaction ${transaction['id']}: $e');
          failedCount++;
        }
      }

      _logger.i('📱 Offline sync completed: $syncedCount synced, $failedCount failed');

      return OfflineSyncResult(
        success: true,
        syncedCount: syncedCount,
        failedCount: failedCount,
        message: 'Synced $syncedCount transactions successfully',
      );
    } catch (e) {
      _logger.e('Offline sync failed: $e');
      return OfflineSyncResult(
        success: false,
        error: 'SYNC_FAILED',
        message: 'Failed to sync offline transactions: ${e.toString()}',
      );
    }
  }

  /// Get offline transaction status
  Future<OfflineTransactionStatus?> getOfflineTransactionStatus(String transactionId) async {
    try {
      final transactions = await _dbHelper.query(
        'offline_qr_transactions',
        where: 'id = ?',
        whereArgs: [transactionId],
        limit: 1,
      );

      if (transactions.isEmpty) return null;

      final transaction = transactions.first;
      return OfflineTransactionStatus(
        transactionId: transactionId,
        status: transaction['status'] as String,
        amount: transaction['amount'] as double,
        merchantId: transaction['merchant_id'] as String,
        createdAt: DateTime.fromMillisecondsSinceEpoch(transaction['created_at'] as int),
        syncedAt: transaction['synced_at'] != null 
            ? DateTime.fromMillisecondsSinceEpoch(transaction['synced_at'] as int)
            : null,
      );
    } catch (e) {
      _logger.e('Failed to get offline transaction status: $e');
      return null;
    }
  }

  /// Get all pending offline transactions
  Future<List<Map<String, dynamic>>> getPendingOfflineTransactions() async {
    return await _getPendingOfflineTransactions();
  }

  /// Create offline QR tables
  Future<void> _createOfflineQRTables() async {
    final db = await _dbHelper.database;

    // Offline QR transactions table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS offline_qr_transactions (
        id TEXT PRIMARY KEY,
        qr_data TEXT NOT NULL,
        payer_user_id TEXT NOT NULL,
        merchant_id TEXT NOT NULL,
        amount REAL NOT NULL,
        currency TEXT NOT NULL,
        description TEXT,
        provider TEXT,
        pin_hash TEXT NOT NULL,
        status TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        synced_at INTEGER,
        network_type TEXT,
        location_data TEXT
      )
    ''');

    // Offline QR codes table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS offline_qr_codes (
        id TEXT PRIMARY KEY,
        merchant_id TEXT NOT NULL,
        qr_payload TEXT NOT NULL,
        amount REAL,
        description TEXT,
        validity_duration_seconds INTEGER NOT NULL,
        created_at INTEGER NOT NULL,
        expires_at INTEGER NOT NULL,
        scan_count INTEGER DEFAULT 0
      )
    ''');

    // Offline sync queue table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS offline_sync_queue (
        id TEXT PRIMARY KEY,
        transaction_id TEXT NOT NULL,
        sync_type TEXT NOT NULL,
        priority INTEGER DEFAULT 1,
        retry_count INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 3,
        created_at INTEGER NOT NULL,
        next_retry_at INTEGER NOT NULL,
        FOREIGN KEY (transaction_id) REFERENCES offline_qr_transactions (id)
      )
    ''');
  }

  /// Setup offline sync mechanisms
  Future<void> _setupOfflineSync() async {
    // Register with offline sync manager
    await _syncManager.initialize();
  }

  /// Validate offline payment
  Future<OfflineValidation> _validateOfflinePayment({
    required ZambiaQRData decodedData,
    required String payerUserId,
  }) async {
    // Check if QR has expired
    if (decodedData.isExpired) {
      return OfflineValidation(
        isValid: false,
        error: 'QR_EXPIRED',
        message: 'QR code has expired',
      );
    }

    // Check amount limits
    if (decodedData.amount < AppConstants.minTransactionAmount) {
      return OfflineValidation(
        isValid: false,
        error: 'AMOUNT_TOO_LOW',
        message: 'Amount below minimum limit',
      );
    }

    if (decodedData.amount > AppConstants.maxTransactionAmount) {
      return OfflineValidation(
        isValid: false,
        error: 'AMOUNT_TOO_HIGH',
        message: 'Amount exceeds maximum limit',
      );
    }

    // Check if merchant exists in offline cache
    final merchant = await _getCachedMerchantInfo(decodedData.merchantId);
    if (merchant == null) {
      return OfflineValidation(
        isValid: false,
        error: 'MERCHANT_NOT_CACHED',
        message: 'Merchant not available offline',
      );
    }

    return OfflineValidation(
      isValid: true,
      message: 'Offline validation successful',
    );
  }

  /// Hash PIN for offline storage
  Future<String> _hashPin(String pin) async {
    return await _encryption.hashPIN(pin);
  }

  /// Generate offline confirmation token
  Future<String> _generateOfflineToken(String transactionId) async {
    final tokenData = {
      'transaction_id': transactionId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'type': 'OFFLINE_QR',
    };

    return await _encryption.encryptData(jsonEncode(tokenData));
  }

  /// Store transaction for SMS fallback
  Future<void> _storeForSMSFallback(
    String transactionId,
    ZambiaQRData qrData,
    double amount,
  ) async {
    try {
      // Create SMS token for fallback
      await _smsTokenService.createOfflinePaymentToken(
        transactionId: transactionId,
        merchantId: qrData.merchantId,
        amount: amount,
        description: qrData.description,
      );
    } catch (e) {
      _logger.e('Failed to store SMS fallback: $e');
    }
  }

  /// Queue transaction for sync
  Future<void> _queueForSync(String transactionId) async {
    final queueItem = {
      'id': _uuid.v4(),
      'transaction_id': transactionId,
      'sync_type': 'QR_PAYMENT',
      'priority': 1,
      'retry_count': 0,
      'max_retries': 3,
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'next_retry_at': DateTime.now().millisecondsSinceEpoch,
    };

    await _dbHelper.insert('offline_sync_queue', queueItem);
  }

  /// Get cached merchant info
  Future<Map<String, dynamic>?> _getCachedMerchantInfo(String merchantId) async {
    return await _merchantService.getMerchantInfo(merchantId);
  }

  /// Detect network type
  Future<String> _detectNetworkType() async {
    final connectivity = await Connectivity().checkConnectivity();
    switch (connectivity) {
      case ConnectivityResult.wifi:
        return 'WIFI';
      case ConnectivityResult.mobile:
        return 'MOBILE';
      case ConnectivityResult.ethernet:
        return 'ETHERNET';
      default:
        return 'NONE';
    }
  }

  /// Get cached location data
  Future<String?> _getCachedLocationData() async {
    // In a real implementation, this would get cached GPS coordinates
    return null;
  }

  /// Create offline QR payload
  Future<String> _createOfflineQRPayload({
    required String qrId,
    required String merchantId,
    double? amount,
    String? description,
    required Duration validity,
  }) async {
    return await ZambiaQRFormat.encode(
      merchantId: merchantId,
      amount: amount ?? 0.0,
      currency: 'ZMW',
      provider: 'OFFLINE',
      description: description,
      expiryDuration: validity,
    );
  }

  /// Generate QR image
  Future<Uint8List> _generateQRImage(String payload) async {
    // This would use the QR generation from PayMuleQR
    // For now, return empty bytes
    return Uint8List(0);
  }

  /// Get pending offline transactions
  Future<List<Map<String, dynamic>>> _getPendingOfflineTransactions() async {
    return await _dbHelper.query(
      'offline_qr_transactions',
      where: 'status IN (?, ?)',
      whereArgs: ['OFFLINE_PENDING', 'SYNC_FAILED'],
      orderBy: 'created_at ASC',
    );
  }

  /// Sync single transaction
  Future<bool> _syncSingleTransaction(Map<String, dynamic> transaction) async {
    try {
      // Process the transaction through merchant service
      final result = await _merchantService.processPayment(
        merchantId: transaction['merchant_id'] as String,
        payerUserId: transaction['payer_user_id'] as String,
        amount: transaction['amount'] as double,
        pin: '', // PIN already verified offline
        description: transaction['description'] as String?,
      );

      return result.success;
    } catch (e) {
      _logger.e('Failed to sync transaction: $e');
      return false;
    }
  }

  /// Mark transaction as synced
  Future<void> _markTransactionSynced(String transactionId) async {
    await _dbHelper.update(
      'offline_qr_transactions',
      {
        'status': 'SYNCED',
        'synced_at': DateTime.now().millisecondsSinceEpoch,
      },
      where: 'id = ?',
      whereArgs: [transactionId],
    );
  }

  /// Mark transaction as failed
  Future<void> _markTransactionFailed(String transactionId) async {
    await _dbHelper.update(
      'offline_qr_transactions',
      {'status': 'SYNC_FAILED'},
      where: 'id = ?',
      whereArgs: [transactionId],
    );
  }
}

/// Offline QR result
class OfflineQRResult {
  final bool success;
  final String? transactionId;
  final double? amount;
  final String? merchantId;
  final String? confirmationToken;
  final String? error;
  final String message;

  OfflineQRResult({
    required this.success,
    this.transactionId,
    this.amount,
    this.merchantId,
    this.confirmationToken,
    this.error,
    required this.message,
  });
}

/// Offline QR generation result
class OfflineQRGenerationResult {
  final bool success;
  final String? qrId;
  final Uint8List? qrImage;
  final String? qrPayload;
  final DateTime? expiresAt;
  final String? error;
  final String message;

  OfflineQRGenerationResult({
    required this.success,
    this.qrId,
    this.qrImage,
    this.qrPayload,
    this.expiresAt,
    this.error,
    required this.message,
  });
}

/// Offline sync result
class OfflineSyncResult {
  final bool success;
  final int syncedCount;
  final int failedCount;
  final String? error;
  final String message;

  OfflineSyncResult({
    required this.success,
    this.syncedCount = 0,
    this.failedCount = 0,
    this.error,
    required this.message,
  });
}

/// Offline validation result
class OfflineValidation {
  final bool isValid;
  final String? error;
  final String message;

  OfflineValidation({
    required this.isValid,
    this.error,
    required this.message,
  });
}

/// Offline transaction status
class OfflineTransactionStatus {
  final String transactionId;
  final String status;
  final double amount;
  final String merchantId;
  final DateTime createdAt;
  final DateTime? syncedAt;

  OfflineTransactionStatus({
    required this.transactionId,
    required this.status,
    required this.amount,
    required this.merchantId,
    required this.createdAt,
    this.syncedAt,
  });
}
