// AUTOMATIC WALLET DETECTION SERVICE
// Detects dominant mobile money provider and activates appropriate wallet

import 'dart:async';
import 'dart:io';
import 'package:flutter/services.dart';
import '../auth/zambia_sim_service.dart';

enum MobileMoneyProvider {
  mtnMobileMoney,
  airtelMoney,
  zamtelKwacha,
  unknown
}

class WalletDetectionResult {
  final MobileMoneyProvider dominantProvider;
  final List<MobileMoneyProvider> availableProviders;
  final double confidence;
  final String? primaryPhoneNumber;
  final Map<MobileMoneyProvider, WalletInfo> walletDetails;

  WalletDetectionResult({
    required this.dominantProvider,
    required this.availableProviders,
    required this.confidence,
    this.primaryPhoneNumber,
    required this.walletDetails,
  });

  Map<String, dynamic> toJson() => {
    'dominantProvider': dominantProvider.toString(),
    'availableProviders': availableProviders.map((p) => p.toString()).toList(),
    'confidence': confidence,
    'primaryPhoneNumber': primaryPhoneNumber,
    'walletDetails': walletDetails.map((k, v) => MapEntry(k.toString(), v.toJson())),
  };
}

class WalletInfo {
  final MobileMoneyProvider provider;
  final String displayName;
  final String serviceCode;
  final String? phoneNumber;
  final bool isActive;
  final bool isRegistered;
  final double usageScore;
  final String apiEndpoint;
  final String color;

  WalletInfo({
    required this.provider,
    required this.displayName,
    required this.serviceCode,
    this.phoneNumber,
    required this.isActive,
    required this.isRegistered,
    required this.usageScore,
    required this.apiEndpoint,
    required this.color,
  });

  Map<String, dynamic> toJson() => {
    'provider': provider.toString(),
    'displayName': displayName,
    'serviceCode': serviceCode,
    'phoneNumber': phoneNumber,
    'isActive': isActive,
    'isRegistered': isRegistered,
    'usageScore': usageScore,
    'apiEndpoint': apiEndpoint,
    'color': color,
  };
}

class MobileMoney {
  static const MethodChannel _channel = MethodChannel('mobile_money_detector');
  
  // Provider configurations
  static const Map<MobileMoneyProvider, Map<String, dynamic>> _providerConfigs = {
    MobileMoneyProvider.mtnMobileMoney: {
      'displayName': 'MTN Mobile Money',
      'serviceCode': '*303#',
      'apiEndpoint': 'https://api.mtn.com/v1',
      'color': '#FFCC00',
      'prefixes': ['96'],
      'keywords': ['mtn', 'mobile money'],
    },
    MobileMoneyProvider.airtelMoney: {
      'displayName': 'Airtel Money',
      'serviceCode': '*432#',
      'apiEndpoint': 'https://api.airtel.africa/v1',
      'color': '#FF0000',
      'prefixes': ['97'],
      'keywords': ['airtel', 'money'],
    },
    MobileMoneyProvider.zamtelKwacha: {
      'displayName': 'Zamtel Kwacha',
      'serviceCode': '*327#',
      'apiEndpoint': 'https://api.zamtel.zm/kwacha/v1',
      'color': '#00AA00',
      'prefixes': ['95'],
      'keywords': ['zamtel', 'kwacha'],
    },
  };

  /// Detect dominant mobile money provider automatically
  static Future<MobileMoneyProvider> detectDominantProvider() async {
    try {
      print('🔍 Detecting dominant mobile money provider...');

      // Get comprehensive wallet detection
      final detectionResult = await detectAllProviders();
      
      print('✅ Dominant provider detected: ${detectionResult.dominantProvider}');
      print('📊 Confidence: ${(detectionResult.confidence * 100).toStringAsFixed(1)}%');
      
      return detectionResult.dominantProvider;

    } catch (e) {
      print('❌ Provider detection failed: $e');
      return MobileMoneyProvider.unknown;
    }
  }

  /// Comprehensive detection of all available providers
  static Future<WalletDetectionResult> detectAllProviders() async {
    try {
      print('🔍 Performing comprehensive wallet detection...');

      // Step 1: SIM-based detection
      final simDetection = await _detectFromSIM();
      
      // Step 2: App-based detection
      final appDetection = await _detectFromInstalledApps();
      
      // Step 3: Usage pattern detection
      final usageDetection = await _detectFromUsagePatterns();
      
      // Step 4: Network signal strength detection
      final signalDetection = await _detectFromSignalStrength();
      
      // Combine all detection methods
      final combinedResults = _combineDetectionResults([
        simDetection,
        appDetection,
        usageDetection,
        signalDetection,
      ]);

      // Determine dominant provider
      final dominantProvider = _determineDominantProvider(combinedResults);
      
      // Calculate confidence score
      final confidence = _calculateConfidence(combinedResults, dominantProvider);
      
      // Get available providers
      final availableProviders = _getAvailableProviders(combinedResults);
      
      // Build wallet details
      final walletDetails = await _buildWalletDetails(combinedResults);
      
      // Get primary phone number
      final primaryPhoneNumber = _getPrimaryPhoneNumber(combinedResults, dominantProvider);

      final result = WalletDetectionResult(
        dominantProvider: dominantProvider,
        availableProviders: availableProviders,
        confidence: confidence,
        primaryPhoneNumber: primaryPhoneNumber,
        walletDetails: walletDetails,
      );

      print('🎯 Detection complete:');
      print('   Dominant: ${_getProviderDisplayName(dominantProvider)}');
      print('   Available: ${availableProviders.length} providers');
      print('   Confidence: ${(confidence * 100).toStringAsFixed(1)}%');

      return result;

    } catch (e) {
      print('❌ Comprehensive detection failed: $e');
      
      // Return fallback result
      return WalletDetectionResult(
        dominantProvider: MobileMoneyProvider.unknown,
        availableProviders: [],
        confidence: 0.0,
        walletDetails: {},
      );
    }
  }

  /// Detect providers from SIM cards
  static Future<Map<MobileMoneyProvider, double>> _detectFromSIM() async {
    final results = <MobileMoneyProvider, double>{};
    
    try {
      print('📱 Detecting from SIM cards...');
      
      // Get SIM information
      final simInfo = await ZambiaSIM.verify();
      
      if (simInfo.isZambianSIM) {
        final provider = _networkToProvider(simInfo.network);
        if (provider != MobileMoneyProvider.unknown) {
          results[provider] = 0.9; // High confidence for SIM detection
          print('   SIM detected: ${_getProviderDisplayName(provider)} (90%)');
        }
      }

      // Check for dual SIM
      final dualSimInfo = await _getDualSIMInfo();
      for (final sim in dualSimInfo) {
        final network = ZambiaSIM.detectNetworkFromNumber(sim['phoneNumber'] ?? '');
        final provider = _networkToProvider(network);
        if (provider != MobileMoneyProvider.unknown) {
          results[provider] = (results[provider] ?? 0.0) + 0.7; // Additional SIM
          print('   Additional SIM: ${_getProviderDisplayName(provider)} (70%)');
        }
      }

    } catch (e) {
      print('   SIM detection failed: $e');
    }
    
    return results;
  }

  /// Detect providers from installed mobile money apps
  static Future<Map<MobileMoneyProvider, double>> _detectFromInstalledApps() async {
    final results = <MobileMoneyProvider, double>{};
    
    try {
      print('📱 Detecting from installed apps...');
      
      final installedApps = await _getInstalledApps();
      
      for (final app in installedApps) {
        final appName = app['appName']?.toString().toLowerCase() ?? '';
        final packageName = app['packageName']?.toString().toLowerCase() ?? '';
        
        // Check for MTN Mobile Money app
        if (appName.contains('mtn') && appName.contains('money') ||
            packageName.contains('mtn') && packageName.contains('money')) {
          results[MobileMoneyProvider.mtnMobileMoney] = 0.8;
          print('   MTN app found (80%)');
        }
        
        // Check for Airtel Money app
        if (appName.contains('airtel') && appName.contains('money') ||
            packageName.contains('airtel') && packageName.contains('money')) {
          results[MobileMoneyProvider.airtelMoney] = 0.8;
          print('   Airtel app found (80%)');
        }
        
        // Check for Zamtel Kwacha app
        if (appName.contains('zamtel') && appName.contains('kwacha') ||
            packageName.contains('zamtel') && packageName.contains('kwacha')) {
          results[MobileMoneyProvider.zamtelKwacha] = 0.8;
          print('   Zamtel app found (80%)');
        }
      }

    } catch (e) {
      print('   App detection failed: $e');
    }
    
    return results;
  }

  /// Detect providers from usage patterns
  static Future<Map<MobileMoneyProvider, double>> _detectFromUsagePatterns() async {
    final results = <MobileMoneyProvider, double>{};
    
    try {
      print('📊 Detecting from usage patterns...');
      
      // Check SMS history for mobile money transactions
      final smsHistory = await _getSMSHistory();
      
      for (final sms in smsHistory) {
        final sender = sms['sender']?.toString().toLowerCase() ?? '';
        final content = sms['content']?.toString().toLowerCase() ?? '';
        
        // MTN Mobile Money patterns
        if (sender.contains('mtn') || content.contains('mtn money') || content.contains('*303#')) {
          results[MobileMoneyProvider.mtnMobileMoney] = (results[MobileMoneyProvider.mtnMobileMoney] ?? 0.0) + 0.1;
        }
        
        // Airtel Money patterns
        if (sender.contains('airtel') || content.contains('airtel money') || content.contains('*432#')) {
          results[MobileMoneyProvider.airtelMoney] = (results[MobileMoneyProvider.airtelMoney] ?? 0.0) + 0.1;
        }
        
        // Zamtel Kwacha patterns
        if (sender.contains('zamtel') || content.contains('kwacha') || content.contains('*327#')) {
          results[MobileMoneyProvider.zamtelKwacha] = (results[MobileMoneyProvider.zamtelKwacha] ?? 0.0) + 0.1;
        }
      }

      // Cap usage scores at 0.6
      results.updateAll((key, value) => value > 0.6 ? 0.6 : value);
      
      results.forEach((provider, score) {
        if (score > 0) {
          print('   ${_getProviderDisplayName(provider)} usage: ${(score * 100).toStringAsFixed(0)}%');
        }
      });

    } catch (e) {
      print('   Usage pattern detection failed: $e');
    }
    
    return results;
  }

  /// Detect providers from network signal strength
  static Future<Map<MobileMoneyProvider, double>> _detectFromSignalStrength() async {
    final results = <MobileMoneyProvider, double>{};
    
    try {
      print('📶 Detecting from signal strength...');
      
      final signalInfo = await _getSignalStrength();
      
      for (final signal in signalInfo) {
        final operatorName = signal['operatorName']?.toString().toLowerCase() ?? '';
        final signalStrength = signal['signalStrength'] as int? ?? 0;
        
        if (signalStrength > 70) { // Strong signal
          if (operatorName.contains('mtn')) {
            results[MobileMoneyProvider.mtnMobileMoney] = 0.3;
            print('   MTN strong signal (30%)');
          } else if (operatorName.contains('airtel')) {
            results[MobileMoneyProvider.airtelMoney] = 0.3;
            print('   Airtel strong signal (30%)');
          } else if (operatorName.contains('zamtel')) {
            results[MobileMoneyProvider.zamtelKwacha] = 0.3;
            print('   Zamtel strong signal (30%)');
          }
        }
      }

    } catch (e) {
      print('   Signal strength detection failed: $e');
    }
    
    return results;
  }

  /// Combine detection results from all methods
  static Map<MobileMoneyProvider, double> _combineDetectionResults(
    List<Map<MobileMoneyProvider, double>> detectionResults,
  ) {
    final combined = <MobileMoneyProvider, double>{};
    
    for (final results in detectionResults) {
      for (final entry in results.entries) {
        combined[entry.key] = (combined[entry.key] ?? 0.0) + entry.value;
      }
    }
    
    return combined;
  }

  /// Determine the dominant provider from combined results
  static MobileMoneyProvider _determineDominantProvider(
    Map<MobileMoneyProvider, double> combinedResults,
  ) {
    if (combinedResults.isEmpty) {
      return MobileMoneyProvider.unknown;
    }
    
    var maxScore = 0.0;
    var dominantProvider = MobileMoneyProvider.unknown;
    
    for (final entry in combinedResults.entries) {
      if (entry.value > maxScore) {
        maxScore = entry.value;
        dominantProvider = entry.key;
      }
    }
    
    return dominantProvider;
  }

  /// Calculate confidence score for the dominant provider
  static double _calculateConfidence(
    Map<MobileMoneyProvider, double> combinedResults,
    MobileMoneyProvider dominantProvider,
  ) {
    if (dominantProvider == MobileMoneyProvider.unknown) {
      return 0.0;
    }
    
    final dominantScore = combinedResults[dominantProvider] ?? 0.0;
    final totalScore = combinedResults.values.fold(0.0, (sum, score) => sum + score);
    
    if (totalScore == 0.0) return 0.0;
    
    // Normalize confidence to 0-1 range
    final confidence = dominantScore / (dominantScore + 1.0);
    return confidence.clamp(0.0, 1.0);
  }

  /// Get list of available providers
  static List<MobileMoneyProvider> _getAvailableProviders(
    Map<MobileMoneyProvider, double> combinedResults,
  ) {
    return combinedResults.entries
        .where((entry) => entry.value > 0.1) // Minimum threshold
        .map((entry) => entry.key)
        .toList();
  }

  /// Build detailed wallet information
  static Future<Map<MobileMoneyProvider, WalletInfo>> _buildWalletDetails(
    Map<MobileMoneyProvider, double> combinedResults,
  ) async {
    final walletDetails = <MobileMoneyProvider, WalletInfo>{};
    
    for (final provider in MobileMoneyProvider.values) {
      if (provider == MobileMoneyProvider.unknown) continue;
      
      final config = _providerConfigs[provider]!;
      final usageScore = combinedResults[provider] ?? 0.0;
      
      walletDetails[provider] = WalletInfo(
        provider: provider,
        displayName: config['displayName'],
        serviceCode: config['serviceCode'],
        phoneNumber: await _getProviderPhoneNumber(provider),
        isActive: usageScore > 0.5,
        isRegistered: usageScore > 0.3,
        usageScore: usageScore,
        apiEndpoint: config['apiEndpoint'],
        color: config['color'],
      );
    }
    
    return walletDetails;
  }

  /// Get primary phone number for the dominant provider
  static String? _getPrimaryPhoneNumber(
    Map<MobileMoneyProvider, double> combinedResults,
    MobileMoneyProvider dominantProvider,
  ) {
    // This would be implemented to return the phone number
    // associated with the dominant provider
    return null; // Placeholder
  }

  /// Convert ZambianNetwork to MobileMoneyProvider
  static MobileMoneyProvider _networkToProvider(ZambianNetwork network) {
    switch (network) {
      case ZambianNetwork.mtn:
        return MobileMoneyProvider.mtnMobileMoney;
      case ZambianNetwork.airtel:
        return MobileMoneyProvider.airtelMoney;
      case ZambianNetwork.zamtel:
        return MobileMoneyProvider.zamtelKwacha;
      case ZambianNetwork.unknown:
        return MobileMoneyProvider.unknown;
    }
  }

  /// Get provider display name
  static String _getProviderDisplayName(MobileMoneyProvider provider) {
    return _providerConfigs[provider]?['displayName'] ?? 'Unknown Provider';
  }

  /// Get provider phone number
  static Future<String?> _getProviderPhoneNumber(MobileMoneyProvider provider) async {
    // Implementation would retrieve phone number for specific provider
    return null; // Placeholder
  }

  // Placeholder methods for native implementations
  static Future<List<Map<String, dynamic>>> _getDualSIMInfo() async {
    try {
      final result = await _channel.invokeMethod('getDualSIMInfo');
      return List<Map<String, dynamic>>.from(result);
    } catch (e) {
      return [];
    }
  }

  static Future<List<Map<String, dynamic>>> _getInstalledApps() async {
    try {
      final result = await _channel.invokeMethod('getInstalledApps');
      return List<Map<String, dynamic>>.from(result);
    } catch (e) {
      return [];
    }
  }

  static Future<List<Map<String, dynamic>>> _getSMSHistory() async {
    try {
      final result = await _channel.invokeMethod('getSMSHistory');
      return List<Map<String, dynamic>>.from(result);
    } catch (e) {
      return [];
    }
  }

  static Future<List<Map<String, dynamic>>> _getSignalStrength() async {
    try {
      final result = await _channel.invokeMethod('getSignalStrength');
      return List<Map<String, dynamic>>.from(result);
    } catch (e) {
      return [];
    }
  }
}
