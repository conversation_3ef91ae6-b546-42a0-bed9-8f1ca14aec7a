// lib/core/production_validator.dart
// CRITICAL PRODUCTION VALIDATOR FOR PAY MULE ZAMBIA DEPLOYMENT
// Ensures no demo elements, real endpoints, and production-ready configuration

import 'package:flutter/foundation.dart';

class ProductionValidator {
  static bool _isValidated = false;
  
  /// CRITICAL: Ensure this is a real production app with no demo elements
  static void ensureRealApp() {
    if (_isValidated) return;
    
    // REMOVE DEMO ELEMENTS
    DemoMode.disable();
    TestAccounts.purge();
    
    // VERIFY REAL ENDPOINTS
    assert(MTNService.isProduction, "MTN not production");
    assert(!AirtelService.isSandbox, "Airtel in sandbox");
    assert(ZamtelService.isLive, "Zamtel not live");
    
    // FORCE PRODUCTION FLAGS
    Environment.forceProduction();
    
    // VALIDATE ZAMBIAN CONFIGURATION
    assert(RegionConfig.current == Region.zambia, "Not configured for Zambia");
    assert(!kDebugMode || Environment.allowDebugInProduction, "Debug mode in production");
    
    _isValidated = true;
    
    if (kDebugMode) {
      print("🇿🇲 PRODUCTION VALIDATOR: All checks passed - Ready for Zambian deployment");
    }
  }
  
  /// Validate that no test/demo data exists
  static void validateNoTestData() {
    assert(!TestAccounts.hasAnyTestAccounts(), "Test accounts still exist");
    assert(!DemoTransactions.hasAnyDemoData(), "Demo transactions still exist");
    assert(!MockServices.isAnyServiceMocked(), "Mock services still active");
  }
  
  /// Ensure all mobile money providers are in production mode
  static void validateMobileMoneyProviders() {
    // MTN Mobile Money - Production validation
    assert(MTNService.baseUrl.contains('api.mtn.com'), "MTN not using production URL");
    assert(MTNService.isProduction, "MTN service not in production mode");
    
    // Airtel Money - Production validation  
    assert(!AirtelService.isSandbox, "Airtel still in sandbox mode");
    assert(AirtelService.baseUrl.contains('airtel.africa'), "Airtel not using production URL");
    
    // Zamtel Kwacha - Production validation
    assert(ZamtelService.isLive, "Zamtel not in live mode");
    assert(ZamtelService.environment == 'production', "Zamtel not in production environment");
  }
  
  /// Validate Zambian-specific configuration
  static void validateZambianConfig() {
    assert(RegionConfig.current == Region.zambia, "Region not set to Zambia");
    assert(CurrencyConfig.primary == Currency.zmw, "Primary currency not ZMW");
    assert(LanguageConfig.includes('en'), "English not included");
    assert(NetworkConfig.optimizedFor2G, "Not optimized for 2G networks");
  }
}

/// Demo mode controller - MUST BE DISABLED IN PRODUCTION
class DemoMode {
  static bool _isEnabled = false;
  
  static bool get isEnabled => _isEnabled;
  
  static void disable() {
    _isEnabled = false;
    if (kDebugMode) {
      print("🚫 DEMO MODE: Disabled for production");
    }
  }
  
  static void enable() {
    assert(kDebugMode, "Demo mode can only be enabled in debug builds");
    _isEnabled = true;
  }
}

/// Test accounts management - MUST BE PURGED IN PRODUCTION
class TestAccounts {
  static final List<String> _testAccountIds = [];
  
  static void purge() {
    _testAccountIds.clear();
    if (kDebugMode) {
      print("🗑️ TEST ACCOUNTS: Purged for production");
    }
  }
  
  static bool hasAnyTestAccounts() => _testAccountIds.isNotEmpty;
}

/// Demo transactions - MUST BE CLEARED IN PRODUCTION
class DemoTransactions {
  static final List<Map<String, dynamic>> _demoData = [];
  
  static void clear() {
    _demoData.clear();
    if (kDebugMode) {
      print("🗑️ DEMO TRANSACTIONS: Cleared for production");
    }
  }
  
  static bool hasAnyDemoData() => _demoData.isNotEmpty;
}

/// Mock services controller - MUST BE DISABLED IN PRODUCTION
class MockServices {
  static bool _anyMocked = false;
  
  static void disableAll() {
    _anyMocked = false;
    if (kDebugMode) {
      print("🚫 MOCK SERVICES: All disabled for production");
    }
  }
  
  static bool isAnyServiceMocked() => _anyMocked;
}

/// Environment configuration
class Environment {
  static bool _isProduction = false;
  static bool _allowDebugInProduction = false;
  
  static bool get isProduction => _isProduction;
  static bool get allowDebugInProduction => _allowDebugInProduction;
  
  static void forceProduction() {
    _isProduction = true;
    if (kDebugMode) {
      print("🏭 ENVIRONMENT: Forced to production mode");
    }
  }
}

/// Mobile Money Service Stubs (replace with actual implementations)
class MTNService {
  static bool get isProduction => true; // Replace with actual check
  static String get baseUrl => 'https://api.mtn.com'; // Replace with actual URL
}

class AirtelService {
  static bool get isSandbox => false; // Replace with actual check
  static String get baseUrl => 'https://api.airtel.africa'; // Replace with actual URL
}

class ZamtelService {
  static bool get isLive => true; // Replace with actual check
  static String get environment => 'production'; // Replace with actual check
}

/// Configuration stubs (replace with actual implementations)
enum Region { zambia, other }
enum Currency { zmw, other }

class RegionConfig {
  static Region current = Region.zambia;
}

class CurrencyConfig {
  static Currency primary = Currency.zmw;
}

class LanguageConfig {
  static bool includes(String lang) => true; // Replace with actual check
}

class NetworkConfig {
  static bool optimizedFor2G = true; // Replace with actual check
}
