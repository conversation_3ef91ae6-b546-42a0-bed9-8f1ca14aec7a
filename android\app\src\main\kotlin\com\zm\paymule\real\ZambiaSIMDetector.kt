// ZAMBIAN SIM DETECTOR - ANDROID NATIVE IMPLEMENTATION
// Detects MTN, Airtel, and Zamtel SIM cards on Android devices

package com.zm.paymule.real

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.telephony.TelephonyManager
import android.telephony.SubscriptionManager
import android.telephony.SubscriptionInfo
import androidx.core.app.ActivityCompat
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class ZambiaSIMDetector(private val context: Context) {
    
    companion object {
        private const val CHANNEL = "zambia_sim_detector"
        
        // Zambian Mobile Country Code (MCC)
        private const val ZAMBIA_MCC = "645"
        
        // Zambian Mobile Network Codes (MNC)
        private const val MTN_MNC = "02"
        private const val AIRTEL_MNC = "01"
        private const val ZAMTEL_MNC_1 = "03"
        private const val ZAMTEL_MNC_2 = "04"
        
        // Network operator codes (MCC + MNC)
        private val ZAMBIAN_OPERATORS = mapOf(
            "64501" to "Airtel",
            "64502" to "MTN",
            "64503" to "Zamtel",
            "64504" to "Zamtel"
        )
    }
    
    fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "getSIMInfo" -> {
                    try {
                        val simInfo = getSIMInfo()
                        result.success(simInfo)
                    } catch (e: Exception) {
                        result.error("SIM_ERROR", "Failed to get SIM info: ${e.message}", null)
                    }
                }
                "getCarrierInfo" -> {
                    try {
                        val carrierInfo = getCarrierInfo()
                        result.success(carrierInfo)
                    } catch (e: Exception) {
                        result.error("CARRIER_ERROR", "Failed to get carrier info: ${e.message}", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
    
    private fun getSIMInfo(): Map<String, Any?> {
        val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        val simInfo = mutableMapOf<String, Any?>()
        
        try {
            // Check permissions
            if (!hasPhonePermission()) {
                return mapOf("error" to "Phone permission not granted")
            }
            
            // Get basic SIM information
            simInfo["networkOperator"] = telephonyManager.networkOperator
            simInfo["networkOperatorName"] = telephonyManager.networkOperatorName
            simInfo["simOperator"] = telephonyManager.simOperator
            simInfo["simOperatorName"] = telephonyManager.simOperatorName
            simInfo["simCountryIso"] = telephonyManager.simCountryIso
            simInfo["networkCountryIso"] = telephonyManager.networkCountryIso
            simInfo["simSerialNumber"] = getSimSerialNumber(telephonyManager)
            simInfo["phoneNumber"] = getPhoneNumber(telephonyManager)
            simInfo["carrierName"] = telephonyManager.simOperatorName
            simInfo["countryCode"] = telephonyManager.simCountryIso?.uppercase()
            
            // Detect Zambian network
            val networkOperator = telephonyManager.simOperator
            simInfo["isZambianSIM"] = isZambianOperator(networkOperator)
            simInfo["zambianNetwork"] = detectZambianNetwork(networkOperator, telephonyManager.simOperatorName)
            
            // Get subscription information (for dual SIM support)
            val subscriptionInfo = getSubscriptionInfo()
            if (subscriptionInfo.isNotEmpty()) {
                simInfo["subscriptions"] = subscriptionInfo
            }
            
        } catch (e: SecurityException) {
            simInfo["error"] = "Security exception: ${e.message}"
        } catch (e: Exception) {
            simInfo["error"] = "Exception: ${e.message}"
        }
        
        return simInfo
    }
    
    private fun getCarrierInfo(): Map<String, Any?> {
        val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        val carrierInfo = mutableMapOf<String, Any?>()
        
        try {
            carrierInfo["carrierName"] = telephonyManager.networkOperatorName
            carrierInfo["networkOperator"] = telephonyManager.networkOperator
            carrierInfo["countryCode"] = telephonyManager.networkCountryIso?.uppercase()
            carrierInfo["isNetworkRoaming"] = telephonyManager.isNetworkRoaming
            carrierInfo["networkType"] = getNetworkTypeString(telephonyManager.networkType)
            
        } catch (e: Exception) {
            carrierInfo["error"] = "Failed to get carrier info: ${e.message}"
        }
        
        return carrierInfo
    }
    
    private fun getSubscriptionInfo(): List<Map<String, Any?>> {
        val subscriptions = mutableListOf<Map<String, Any?>>()
        
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP_MR1) {
                val subscriptionManager = context.getSystemService(Context.TELEPHONY_SUBSCRIPTION_SERVICE) as SubscriptionManager
                
                if (hasPhonePermission()) {
                    val activeSubscriptions = subscriptionManager.activeSubscriptionInfoList
                    
                    activeSubscriptions?.forEach { subscription ->
                        val subInfo = mutableMapOf<String, Any?>()
                        subInfo["subscriptionId"] = subscription.subscriptionId
                        subInfo["displayName"] = subscription.displayName?.toString()
                        subInfo["carrierName"] = subscription.carrierName?.toString()
                        subInfo["countryIso"] = subscription.countryIso
                        subInfo["mcc"] = subscription.mcc
                        subInfo["mnc"] = subscription.mnc
                        subInfo["simSlotIndex"] = subscription.simSlotIndex
                        subInfo["phoneNumber"] = subscription.number
                        
                        // Check if it's a Zambian SIM
                        val operatorCode = "${subscription.mcc}${subscription.mnc.toString().padStart(2, '0')}"
                        subInfo["isZambianSIM"] = isZambianOperator(operatorCode)
                        subInfo["zambianNetwork"] = detectZambianNetwork(operatorCode, subscription.carrierName?.toString())
                        
                        subscriptions.add(subInfo)
                    }
                }
            }
        } catch (e: Exception) {
            // Ignore subscription errors for older devices
        }
        
        return subscriptions
    }
    
    private fun getSimSerialNumber(telephonyManager: TelephonyManager): String? {
        return try {
            if (hasPhonePermission()) {
                telephonyManager.simSerialNumber
            } else null
        } catch (e: Exception) {
            null
        }
    }
    
    private fun getPhoneNumber(telephonyManager: TelephonyManager): String? {
        return try {
            if (hasPhonePermission()) {
                val number = telephonyManager.line1Number
                if (number?.isNotEmpty() == true) {
                    formatZambianPhoneNumber(number)
                } else null
            } else null
        } catch (e: Exception) {
            null
        }
    }
    
    private fun formatZambianPhoneNumber(phoneNumber: String): String {
        val cleaned = phoneNumber.replace(Regex("[^\\d]"), "")
        
        return when {
            cleaned.startsWith("260") -> "+$cleaned"
            cleaned.startsWith("0") && cleaned.length == 10 -> "+260${cleaned.substring(1)}"
            cleaned.length == 9 -> "+260$cleaned"
            else -> "+$cleaned"
        }
    }
    
    private fun isZambianOperator(operatorCode: String?): Boolean {
        if (operatorCode == null) return false
        
        // Check if operator code starts with Zambian MCC (645)
        if (operatorCode.startsWith(ZAMBIA_MCC)) {
            return true
        }
        
        // Check against known Zambian operators
        return ZAMBIAN_OPERATORS.containsKey(operatorCode)
    }
    
    private fun detectZambianNetwork(operatorCode: String?, carrierName: String?): String {
        // Check by operator code first
        operatorCode?.let { code ->
            when (code) {
                "64501" -> return "Airtel"
                "64502" -> return "MTN"
                "64503", "64504" -> return "Zamtel"
            }
            
            // Check by MCC+MNC pattern
            if (code.startsWith(ZAMBIA_MCC) && code.length >= 5) {
                val mnc = code.substring(3)
                when (mnc) {
                    MTN_MNC -> return "MTN"
                    AIRTEL_MNC -> return "Airtel"
                    ZAMTEL_MNC_1, ZAMTEL_MNC_2 -> return "Zamtel"
                }
            }
        }
        
        // Check by carrier name
        carrierName?.let { name ->
            val lowerName = name.lowercase()
            when {
                lowerName.contains("mtn") -> return "MTN"
                lowerName.contains("airtel") -> return "Airtel"
                lowerName.contains("zamtel") -> return "Zamtel"
            }
        }
        
        return "Unknown"
    }
    
    private fun getNetworkTypeString(networkType: Int): String {
        return when (networkType) {
            TelephonyManager.NETWORK_TYPE_GPRS -> "GPRS"
            TelephonyManager.NETWORK_TYPE_EDGE -> "EDGE"
            TelephonyManager.NETWORK_TYPE_UMTS -> "UMTS"
            TelephonyManager.NETWORK_TYPE_HSDPA -> "HSDPA"
            TelephonyManager.NETWORK_TYPE_HSUPA -> "HSUPA"
            TelephonyManager.NETWORK_TYPE_HSPA -> "HSPA"
            TelephonyManager.NETWORK_TYPE_LTE -> "LTE"
            TelephonyManager.NETWORK_TYPE_NR -> "5G"
            else -> "Unknown"
        }
    }
    
    private fun hasPhonePermission(): Boolean {
        return ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.READ_PHONE_STATE
        ) == PackageManager.PERMISSION_GRANTED
    }
}
