/// Zambian QR Code Format for Mobile Money Integration
/// Optimized for MTN, Airtel, and Zamtel networks
/// Supports offline validation and 2G network compatibility

import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:logger/logger.dart';

import '../core/constants/app_constants.dart';
import '../core/security/encryption_service.dart';

/// Zambian QR Format Handler
class ZambiaQRFormat {
  static final Logger _logger = Logger();
  static final EncryptionService _encryption = EncryptionService();

  // QR Format Version for compatibility
  static const String QR_VERSION = '2.0';
  static const String QR_PREFIX = 'ZPAY';
  
  // Mobile Money Provider Codes
  static const Map<String, String> PROVIDER_CODES = {
    'MTN': 'M',
    'AIRTEL': 'A', 
    'ZAMTEL': 'Z',
    'UNKNOWN': 'U',
  };

  // Currency codes
  static const Map<String, String> CURRENCY_CODES = {
    'ZMW': 'K',
    'USD': 'D',
    'EUR': 'E',
  };

  /// Encode payment data into Zambian QR format
  static Future<String> encode({
    required String merchantId,
    required double amount,
    required String currency,
    required String provider,
    String? description,
    Duration? expiryDuration,
  }) async {
    try {
      await _encryption.initialize();

      // Create timestamp and expiry
      final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final expiry = expiryDuration != null 
          ? timestamp + expiryDuration.inSeconds
          : timestamp + (24 * 60 * 60); // Default 24 hours

      // Create compact payload for 2G optimization
      final payload = {
        'v': QR_VERSION,                                    // Version
        'p': PROVIDER_CODES[provider] ?? 'U',              // Provider code
        'm': _compressMerchantId(merchantId),               // Compressed merchant ID
        'a': _formatAmount(amount),                         // Formatted amount
        'c': CURRENCY_CODES[currency] ?? 'K',              // Currency code
        'd': _compressDescription(description),             // Compressed description
        't': timestamp,                                     // Timestamp
        'e': expiry,                                        // Expiry timestamp
        'h': '',                                            // Hash placeholder
      };

      // Remove null values
      payload.removeWhere((key, value) => value == null || value == '');

      // Create hash for integrity
      final payloadString = jsonEncode(payload);
      final hash = _createHash(payloadString);
      payload['h'] = hash.substring(0, 8); // Short hash for QR size

      // Create final QR string
      final qrData = '$QR_PREFIX:${base64Encode(utf8.encode(jsonEncode(payload)))}';
      
      _logger.i('QR encoded for merchant: ${merchantId.substring(0, 8)}...');
      return qrData;
    } catch (e) {
      _logger.e('QR encoding failed: $e');
      rethrow;
    }
  }

  /// Decode Zambian QR format
  static Future<ZambiaQRData> decode(String qrData) async {
    try {
      // Validate QR prefix
      if (!qrData.startsWith('$QR_PREFIX:')) {
        throw Exception('Invalid QR format: Missing ZPAY prefix');
      }

      // Extract and decode payload
      final encodedPayload = qrData.substring(QR_PREFIX.length + 1);
      final decodedBytes = base64Decode(encodedPayload);
      final payloadString = utf8.decode(decodedBytes);
      final payload = jsonDecode(payloadString) as Map<String, dynamic>;

      // Validate version
      if (payload['v'] != QR_VERSION) {
        throw Exception('Unsupported QR version: ${payload['v']}');
      }

      // Verify hash integrity
      final receivedHash = payload['h'] as String?;
      if (receivedHash != null) {
        final payloadCopy = Map<String, dynamic>.from(payload);
        payloadCopy.remove('h');
        final expectedHash = _createHash(jsonEncode(payloadCopy));
        
        if (!expectedHash.startsWith(receivedHash)) {
          throw Exception('QR data integrity check failed');
        }
      }

      // Parse and expand data
      final provider = _expandProviderCode(payload['p'] as String?);
      final merchantId = _expandMerchantId(payload['m'] as String?);
      final amount = _parseAmount(payload['a']);
      final currency = _expandCurrencyCode(payload['c'] as String?);
      final description = _expandDescription(payload['d'] as String?);
      final timestamp = payload['t'] as int?;
      final expiry = payload['e'] as int?;

      // Validate required fields
      if (merchantId == null || amount == null) {
        throw Exception('Missing required QR data fields');
      }

      // Check expiry
      DateTime? expiryTime;
      if (expiry != null) {
        expiryTime = DateTime.fromMillisecondsSinceEpoch(expiry * 1000);
        if (DateTime.now().isAfter(expiryTime)) {
          throw Exception('QR code has expired');
        }
      }

      _logger.i('QR decoded successfully for merchant: ${merchantId.substring(0, 8)}...');
      
      return ZambiaQRData(
        merchantId: merchantId,
        amount: amount,
        currency: currency,
        provider: provider,
        description: description,
        timestamp: timestamp != null 
            ? DateTime.fromMillisecondsSinceEpoch(timestamp * 1000)
            : null,
        expiryTime: expiryTime,
      );
    } catch (e) {
      _logger.e('QR decoding failed: $e');
      rethrow;
    }
  }

  /// Compress merchant ID for QR size optimization
  static String _compressMerchantId(String merchantId) {
    // Use first 12 characters for 2G optimization
    return merchantId.length > 12 ? merchantId.substring(0, 12) : merchantId;
  }

  /// Expand compressed merchant ID
  static String? _expandMerchantId(String? compressed) {
    // In production, this would lookup full merchant ID from database
    return compressed;
  }

  /// Format amount for compact representation
  static String _formatAmount(double amount) {
    // Remove unnecessary decimal places
    if (amount == amount.roundToDouble()) {
      return amount.round().toString();
    }
    return amount.toStringAsFixed(2);
  }

  /// Parse amount from string
  static double? _parseAmount(dynamic amountData) {
    if (amountData == null) return null;
    if (amountData is double) return amountData;
    if (amountData is int) return amountData.toDouble();
    if (amountData is String) return double.tryParse(amountData);
    return null;
  }

  /// Compress description for QR size
  static String? _compressDescription(String? description) {
    if (description == null || description.isEmpty) return null;
    // Limit to 30 characters for 2G optimization
    return description.length > 30 ? description.substring(0, 30) : description;
  }

  /// Expand compressed description
  static String? _expandDescription(String? compressed) {
    return compressed;
  }

  /// Expand provider code to full name
  static String _expandProviderCode(String? code) {
    switch (code) {
      case 'M': return 'MTN';
      case 'A': return 'AIRTEL';
      case 'Z': return 'ZAMTEL';
      default: return 'UNKNOWN';
    }
  }

  /// Expand currency code
  static String _expandCurrencyCode(String? code) {
    switch (code) {
      case 'K': return 'ZMW';
      case 'D': return 'USD';
      case 'E': return 'EUR';
      default: return 'ZMW';
    }
  }

  /// Create hash for data integrity
  static String _createHash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Validate QR data format
  static bool isValidFormat(String qrData) {
    try {
      return qrData.startsWith('$QR_PREFIX:') && 
             qrData.length > QR_PREFIX.length + 1;
    } catch (e) {
      return false;
    }
  }

  /// Get QR data size in bytes
  static int getDataSize(String qrData) {
    return utf8.encode(qrData).length;
  }

  /// Check if QR is optimized for 2G networks
  static bool is2GOptimized(String qrData) {
    return getDataSize(qrData) <= 200; // 200 bytes limit for 2G
  }

  /// Validate amount for Zambian QR payments
  /// Supports BoZ regulations and mobile money limits
  static bool validateAmount(double amount) {
    // Allow open amount (0) for customer to enter
    if (amount == 0) return true;

    // Standard Zambian mobile money limits
    // Minimum: K5 (to cover transaction fees)
    // Maximum: K5000 (daily transaction limit for most providers)
    return amount >= 5.0 && amount <= 5000.0;
  }

  /// Validate amount with detailed result
  static AmountValidationResult validateAmountDetailed(double amount) {
    if (amount < 0) {
      return AmountValidationResult(
        isValid: false,
        error: 'NEGATIVE_AMOUNT',
        message: 'Amount cannot be negative',
        suggestedAmount: 0.0,
      );
    }

    if (amount == 0) {
      return AmountValidationResult(
        isValid: true,
        message: 'Open amount - customer will enter amount',
        isOpenAmount: true,
      );
    }

    if (amount < 5.0) {
      return AmountValidationResult(
        isValid: false,
        error: 'AMOUNT_TOO_LOW',
        message: 'Minimum amount is K5.00',
        suggestedAmount: 5.0,
      );
    }

    if (amount > 5000.0) {
      return AmountValidationResult(
        isValid: false,
        error: 'AMOUNT_TOO_HIGH',
        message: 'Maximum amount is K5,000.00',
        suggestedAmount: 5000.0,
      );
    }

    // Check for reasonable decimal places (max 2)
    final decimalPlaces = _getDecimalPlaces(amount);
    if (decimalPlaces > 2) {
      return AmountValidationResult(
        isValid: false,
        error: 'TOO_MANY_DECIMALS',
        message: 'Amount can have maximum 2 decimal places',
        suggestedAmount: double.parse(amount.toStringAsFixed(2)),
      );
    }

    return AmountValidationResult(
      isValid: true,
      message: 'Amount is valid',
    );
  }

  /// Validate amount for specific provider
  static bool validateAmountForProvider(double amount, String provider) {
    if (!validateAmount(amount)) return false;

    // Provider-specific limits (based on Zambian mobile money operators)
    switch (provider.toUpperCase()) {
      case 'MTN':
        // MTN Mobile Money limits
        return amount <= 3000.0; // K3,000 daily limit for basic accounts

      case 'AIRTEL':
        // Airtel Money limits
        return amount <= 2500.0; // K2,500 daily limit for basic accounts

      case 'ZAMTEL':
        // Zamtel Kwacha limits
        return amount <= 2000.0; // K2,000 daily limit for basic accounts

      default:
        // Use general validation for unknown providers
        return true;
    }
  }

  /// Get recommended amount ranges for different transaction types
  static Map<String, AmountRange> getRecommendedAmountRanges() {
    return {
      'RETAIL': AmountRange(min: 5.0, max: 500.0, typical: [10.0, 20.0, 50.0, 100.0]),
      'TRANSPORT': AmountRange(min: 5.0, max: 200.0, typical: [10.0, 15.0, 25.0, 50.0]),
      'UTILITIES': AmountRange(min: 20.0, max: 1000.0, typical: [50.0, 100.0, 200.0, 500.0]),
      'FOOD': AmountRange(min: 5.0, max: 300.0, typical: [15.0, 25.0, 50.0, 100.0]),
      'SERVICES': AmountRange(min: 10.0, max: 2000.0, typical: [50.0, 100.0, 200.0, 500.0]),
      'MARKET': AmountRange(min: 5.0, max: 200.0, typical: [10.0, 20.0, 30.0, 50.0]),
    };
  }

  /// Format amount for display in Zambian context
  static String formatAmountForDisplay(double amount, {bool includeSymbol = true}) {
    if (amount == 0) {
      return includeSymbol ? 'K 0.00 (Open Amount)' : '0.00 (Open Amount)';
    }

    final formatted = amount.toStringAsFixed(2);
    final parts = formatted.split('.');

    // Add thousand separators for readability
    final wholePart = parts[0];
    final decimalPart = parts[1];

    String formattedWhole = '';
    for (int i = 0; i < wholePart.length; i++) {
      if (i > 0 && (wholePart.length - i) % 3 == 0) {
        formattedWhole += ',';
      }
      formattedWhole += wholePart[i];
    }

    final result = '$formattedWhole.$decimalPart';
    return includeSymbol ? 'K $result' : result;
  }

  /// Parse amount from string input
  static double? parseAmountFromString(String input) {
    if (input.trim().isEmpty) return null;

    // Remove currency symbols and spaces
    String cleaned = input
        .replaceAll('K', '')
        .replaceAll('ZMW', '')
        .replaceAll(',', '')
        .replaceAll(' ', '')
        .trim();

    return double.tryParse(cleaned);
  }

  /// Check if amount is a common/typical amount
  static bool isTypicalAmount(double amount) {
    const typicalAmounts = [
      5.0, 10.0, 15.0, 20.0, 25.0, 30.0, 50.0,
      100.0, 150.0, 200.0, 250.0, 300.0, 500.0,
      1000.0, 1500.0, 2000.0, 2500.0, 3000.0
    ];

    return typicalAmounts.contains(amount);
  }

  /// Get suggested amounts based on merchant category
  static List<double> getSuggestedAmounts(String merchantCategory) {
    final ranges = getRecommendedAmountRanges();
    final range = ranges[merchantCategory.toUpperCase()];

    if (range != null) {
      return range.typical;
    }

    // Default suggestions
    return [10.0, 20.0, 50.0, 100.0, 200.0, 500.0];
  }

  /// Get number of decimal places in a double
  static int _getDecimalPlaces(double value) {
    final str = value.toString();
    if (!str.contains('.')) return 0;

    final parts = str.split('.');
    return parts[1].length;
  }
}

/// Zambian QR Data Model
class ZambiaQRData {
  final String merchantId;
  final double amount;
  final String currency;
  final String provider;
  final String? description;
  final DateTime? timestamp;
  final DateTime? expiryTime;

  ZambiaQRData({
    required this.merchantId,
    required this.amount,
    required this.currency,
    required this.provider,
    this.description,
    this.timestamp,
    this.expiryTime,
  });

  Map<String, dynamic> toJson() {
    return {
      'merchantId': merchantId,
      'amount': amount,
      'currency': currency,
      'provider': provider,
      'description': description,
      'timestamp': timestamp?.millisecondsSinceEpoch,
      'expiryTime': expiryTime?.millisecondsSinceEpoch,
    };
  }

  factory ZambiaQRData.fromJson(Map<String, dynamic> json) {
    return ZambiaQRData(
      merchantId: json['merchantId'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      provider: json['provider'] as String,
      description: json['description'] as String?,
      timestamp: json['timestamp'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int)
          : null,
      expiryTime: json['expiryTime'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['expiryTime'] as int)
          : null,
    );
  }

  bool get isExpired => expiryTime != null && DateTime.now().isAfter(expiryTime!);
  
  bool get isValid => merchantId.isNotEmpty && amount > 0 && currency.isNotEmpty;
}

/// Transaction Validation Result
class TransactionValidation {
  final bool isValid;
  final String? error;
  final String message;

  TransactionValidation({
    required this.isValid,
    this.error,
    required this.message,
  });
}

/// Amount validation result
class AmountValidationResult {
  final bool isValid;
  final String? error;
  final String message;
  final double? suggestedAmount;
  final bool isOpenAmount;

  AmountValidationResult({
    required this.isValid,
    this.error,
    required this.message,
    this.suggestedAmount,
    this.isOpenAmount = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'isValid': isValid,
      'error': error,
      'message': message,
      'suggestedAmount': suggestedAmount,
      'isOpenAmount': isOpenAmount,
    };
  }
}

/// Amount range for different merchant categories
class AmountRange {
  final double min;
  final double max;
  final List<double> typical;

  AmountRange({
    required this.min,
    required this.max,
    required this.typical,
  });

  bool contains(double amount) {
    return amount >= min && amount <= max;
  }

  Map<String, dynamic> toJson() {
    return {
      'min': min,
      'max': max,
      'typical': typical,
    };
  }
}

/// QR Payment Result
class QRPaymentResult {
  final bool success;
  final String? transactionId;
  final double? amount;
  final String? merchantId;
  final String? error;
  final String message;
  final bool isOffline;

  QRPaymentResult({
    required this.success,
    this.transactionId,
    this.amount,
    this.merchantId,
    this.error,
    required this.message,
    this.isOffline = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'transactionId': transactionId,
      'amount': amount,
      'merchantId': merchantId,
      'error': error,
      'message': message,
      'isOffline': isOffline,
    };
  }
}
