/// Zambian QR Code Format for Mobile Money Integration
/// Optimized for MTN, Airtel, and Zamtel networks
/// Supports offline validation and 2G network compatibility

import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:logger/logger.dart';

import '../core/constants/app_constants.dart';
import '../core/security/encryption_service.dart';

/// Zambian QR Format Handler
class ZambiaQRFormat {
  static final Logger _logger = Logger();
  static final EncryptionService _encryption = EncryptionService();

  // QR Format Version for compatibility
  static const String QR_VERSION = '2.0';
  static const String QR_PREFIX = 'ZPAY';
  
  // Mobile Money Provider Codes
  static const Map<String, String> PROVIDER_CODES = {
    'MTN': 'M',
    'AIRTEL': 'A', 
    'ZAMTEL': 'Z',
    'UNKNOWN': 'U',
  };

  // Currency codes
  static const Map<String, String> CURRENCY_CODES = {
    'ZMW': 'K',
    'USD': 'D',
    'EUR': 'E',
  };

  /// Encode payment data into Zambian QR format
  static Future<String> encode({
    required String merchantId,
    required double amount,
    required String currency,
    required String provider,
    String? description,
    Duration? expiryDuration,
  }) async {
    try {
      await _encryption.initialize();

      // Create timestamp and expiry
      final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final expiry = expiryDuration != null 
          ? timestamp + expiryDuration.inSeconds
          : timestamp + (24 * 60 * 60); // Default 24 hours

      // Create compact payload for 2G optimization
      final payload = {
        'v': QR_VERSION,                                    // Version
        'p': PROVIDER_CODES[provider] ?? 'U',              // Provider code
        'm': _compressMerchantId(merchantId),               // Compressed merchant ID
        'a': _formatAmount(amount),                         // Formatted amount
        'c': CURRENCY_CODES[currency] ?? 'K',              // Currency code
        'd': _compressDescription(description),             // Compressed description
        't': timestamp,                                     // Timestamp
        'e': expiry,                                        // Expiry timestamp
        'h': '',                                            // Hash placeholder
      };

      // Remove null values
      payload.removeWhere((key, value) => value == null || value == '');

      // Create hash for integrity
      final payloadString = jsonEncode(payload);
      final hash = _createHash(payloadString);
      payload['h'] = hash.substring(0, 8); // Short hash for QR size

      // Create final QR string
      final qrData = '$QR_PREFIX:${base64Encode(utf8.encode(jsonEncode(payload)))}';
      
      _logger.i('QR encoded for merchant: ${merchantId.substring(0, 8)}...');
      return qrData;
    } catch (e) {
      _logger.e('QR encoding failed: $e');
      rethrow;
    }
  }

  /// Decode Zambian QR format
  static Future<ZambiaQRData> decode(String qrData) async {
    try {
      // Validate QR prefix
      if (!qrData.startsWith('$QR_PREFIX:')) {
        throw Exception('Invalid QR format: Missing ZPAY prefix');
      }

      // Extract and decode payload
      final encodedPayload = qrData.substring(QR_PREFIX.length + 1);
      final decodedBytes = base64Decode(encodedPayload);
      final payloadString = utf8.decode(decodedBytes);
      final payload = jsonDecode(payloadString) as Map<String, dynamic>;

      // Validate version
      if (payload['v'] != QR_VERSION) {
        throw Exception('Unsupported QR version: ${payload['v']}');
      }

      // Verify hash integrity
      final receivedHash = payload['h'] as String?;
      if (receivedHash != null) {
        final payloadCopy = Map<String, dynamic>.from(payload);
        payloadCopy.remove('h');
        final expectedHash = _createHash(jsonEncode(payloadCopy));
        
        if (!expectedHash.startsWith(receivedHash)) {
          throw Exception('QR data integrity check failed');
        }
      }

      // Parse and expand data
      final provider = _expandProviderCode(payload['p'] as String?);
      final merchantId = _expandMerchantId(payload['m'] as String?);
      final amount = _parseAmount(payload['a']);
      final currency = _expandCurrencyCode(payload['c'] as String?);
      final description = _expandDescription(payload['d'] as String?);
      final timestamp = payload['t'] as int?;
      final expiry = payload['e'] as int?;

      // Validate required fields
      if (merchantId == null || amount == null) {
        throw Exception('Missing required QR data fields');
      }

      // Check expiry
      DateTime? expiryTime;
      if (expiry != null) {
        expiryTime = DateTime.fromMillisecondsSinceEpoch(expiry * 1000);
        if (DateTime.now().isAfter(expiryTime)) {
          throw Exception('QR code has expired');
        }
      }

      _logger.i('QR decoded successfully for merchant: ${merchantId.substring(0, 8)}...');
      
      return ZambiaQRData(
        merchantId: merchantId,
        amount: amount,
        currency: currency,
        provider: provider,
        description: description,
        timestamp: timestamp != null 
            ? DateTime.fromMillisecondsSinceEpoch(timestamp * 1000)
            : null,
        expiryTime: expiryTime,
      );
    } catch (e) {
      _logger.e('QR decoding failed: $e');
      rethrow;
    }
  }

  /// Compress merchant ID for QR size optimization
  static String _compressMerchantId(String merchantId) {
    // Use first 12 characters for 2G optimization
    return merchantId.length > 12 ? merchantId.substring(0, 12) : merchantId;
  }

  /// Expand compressed merchant ID
  static String? _expandMerchantId(String? compressed) {
    // In production, this would lookup full merchant ID from database
    return compressed;
  }

  /// Format amount for compact representation
  static String _formatAmount(double amount) {
    // Remove unnecessary decimal places
    if (amount == amount.roundToDouble()) {
      return amount.round().toString();
    }
    return amount.toStringAsFixed(2);
  }

  /// Parse amount from string
  static double? _parseAmount(dynamic amountData) {
    if (amountData == null) return null;
    if (amountData is double) return amountData;
    if (amountData is int) return amountData.toDouble();
    if (amountData is String) return double.tryParse(amountData);
    return null;
  }

  /// Compress description for QR size
  static String? _compressDescription(String? description) {
    if (description == null || description.isEmpty) return null;
    // Limit to 30 characters for 2G optimization
    return description.length > 30 ? description.substring(0, 30) : description;
  }

  /// Expand compressed description
  static String? _expandDescription(String? compressed) {
    return compressed;
  }

  /// Expand provider code to full name
  static String _expandProviderCode(String? code) {
    switch (code) {
      case 'M': return 'MTN';
      case 'A': return 'AIRTEL';
      case 'Z': return 'ZAMTEL';
      default: return 'UNKNOWN';
    }
  }

  /// Expand currency code
  static String _expandCurrencyCode(String? code) {
    switch (code) {
      case 'K': return 'ZMW';
      case 'D': return 'USD';
      case 'E': return 'EUR';
      default: return 'ZMW';
    }
  }

  /// Create hash for data integrity
  static String _createHash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Validate QR data format
  static bool isValidFormat(String qrData) {
    try {
      return qrData.startsWith('$QR_PREFIX:') && 
             qrData.length > QR_PREFIX.length + 1;
    } catch (e) {
      return false;
    }
  }

  /// Get QR data size in bytes
  static int getDataSize(String qrData) {
    return utf8.encode(qrData).length;
  }

  /// Check if QR is optimized for 2G networks
  static bool is2GOptimized(String qrData) {
    return getDataSize(qrData) <= 200; // 200 bytes limit for 2G
  }
}

/// Zambian QR Data Model
class ZambiaQRData {
  final String merchantId;
  final double amount;
  final String currency;
  final String provider;
  final String? description;
  final DateTime? timestamp;
  final DateTime? expiryTime;

  ZambiaQRData({
    required this.merchantId,
    required this.amount,
    required this.currency,
    required this.provider,
    this.description,
    this.timestamp,
    this.expiryTime,
  });

  Map<String, dynamic> toJson() {
    return {
      'merchantId': merchantId,
      'amount': amount,
      'currency': currency,
      'provider': provider,
      'description': description,
      'timestamp': timestamp?.millisecondsSinceEpoch,
      'expiryTime': expiryTime?.millisecondsSinceEpoch,
    };
  }

  factory ZambiaQRData.fromJson(Map<String, dynamic> json) {
    return ZambiaQRData(
      merchantId: json['merchantId'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      provider: json['provider'] as String,
      description: json['description'] as String?,
      timestamp: json['timestamp'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int)
          : null,
      expiryTime: json['expiryTime'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['expiryTime'] as int)
          : null,
    );
  }

  bool get isExpired => expiryTime != null && DateTime.now().isAfter(expiryTime!);
  
  bool get isValid => merchantId.isNotEmpty && amount > 0 && currency.isNotEmpty;
}

/// Transaction Validation Result
class TransactionValidation {
  final bool isValid;
  final String? error;
  final String message;

  TransactionValidation({
    required this.isValid,
    this.error,
    required this.message,
  });
}

/// QR Payment Result
class QRPaymentResult {
  final bool success;
  final String? transactionId;
  final double? amount;
  final String? merchantId;
  final String? error;
  final String message;
  final bool isOffline;

  QRPaymentResult({
    required this.success,
    this.transactionId,
    this.amount,
    this.merchantId,
    this.error,
    required this.message,
    this.isOffline = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'transactionId': transactionId,
      'amount': amount,
      'merchantId': merchantId,
      'error': error,
      'message': message,
      'isOffline': isOffline,
    };
  }
}
