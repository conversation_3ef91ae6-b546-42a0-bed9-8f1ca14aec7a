/// QR Payment System Example Usage for Zambian Market
/// Demonstrates complete QR payment flow with offline support
/// Includes merchant registration, QR generation, scanning, and payment processing

import 'dart:typed_data';
import 'package:flutter/material.dart';

import '../zambia_qr.dart';
import '../zambia_qr_format.dart';
import '../merchant_qr_service.dart';
import '../offline_qr_service.dart';
import '../qr_scanner_service.dart';
import '../security/qr_security_service.dart';
import '../widgets/qr_scanner_widget.dart';
import '../widgets/qr_generator_widget.dart';

/// Complete QR Payment Example
class QRPaymentExample {
  static final PayMuleQR _qrSystem = PayMuleQR();
  static final MerchantQRService _merchantService = MerchantQRService();
  static final OfflineQRService _offlineService = OfflineQRService();
  static final QRScannerService _scannerService = QRScannerService();
  static final QRSecurityService _securityService = QRSecurityService();

  /// Example 1: Merchant Registration and QR Generation
  static Future<void> merchantExample() async {
    print('🏪 === MERCHANT QR EXAMPLE ===');

    try {
      // Initialize services
      await _qrSystem.initialize();
      await _merchantService.initialize();

      // Register a new merchant
      final registrationResult = await _merchantService.registerMerchant(
        userId: 'user_12345',
        businessName: 'Lusaka Market Stall',
        phoneNumber: '+260961234567', // MTN number
        category: 'RETAIL',
        location: 'Lusaka Central Market, Stall 45',
        description: 'Fresh vegetables and fruits',
        typicalAmounts: [5.0, 10.0, 20.0, 50.0],
        businessDetails: {
          'operating_hours': '06:00-18:00',
          'payment_methods': ['MTN', 'AIRTEL'],
          'languages': ['English', 'Bemba', 'Nyanja'],
        },
      );

      if (registrationResult.success) {
        print('✅ Merchant registered: ${registrationResult.merchantId}');

        // Generate QR codes for different amounts
        await _generateMerchantQRCodes(registrationResult.merchantId!);
      } else {
        print('❌ Merchant registration failed: ${registrationResult.message}');
      }
    } catch (e) {
      print('❌ Merchant example failed: $e');
    }
  }

  /// Example 2: Customer QR Payment Flow
  static Future<void> customerPaymentExample() async {
    print('💳 === CUSTOMER PAYMENT EXAMPLE ===');

    try {
      // Initialize services
      await _qrSystem.initialize();
      await _securityService.initialize();

      // Simulate scanning a QR code
      const qrData = 'ZPAY:********************************************************************************************************************************************************************';
      
      // Validate QR code
      final qrValidation = await _securityService.validateQRCode(qrData);
      if (!qrValidation.isValid) {
        print('❌ Invalid QR code: ${qrValidation.message}');
        return;
      }

      print('✅ QR code validated successfully');
      final decodedData = qrValidation.decodedData!;
      print('   Merchant: ${decodedData.merchantId}');
      print('   Amount: K${decodedData.amount}');
      print('   Provider: ${decodedData.provider}');

      // Verify transaction PIN
      const userId = 'customer_67890';
      const pin = '123456';
      
      final pinVerification = await _securityService.verifyTransactionPIN(
        userId: userId,
        pin: pin,
        amount: decodedData.amount,
        merchantId: decodedData.merchantId,
      );

      if (!pinVerification.success) {
        print('❌ PIN verification failed: ${pinVerification.message}');
        return;
      }

      print('✅ PIN verified successfully');

      // Check transaction limits
      final limitCheck = await _securityService.validateTransactionLimits(
        userId: userId,
        amount: decodedData.amount,
        transactionType: 'QR_PAYMENT',
      );

      if (!limitCheck.isValid) {
        print('❌ Transaction limit exceeded: ${limitCheck.message}');
        return;
      }

      print('✅ Transaction limits validated');

      // Fraud detection
      final fraudCheck = await _securityService.detectFraud(
        userId: userId,
        qrData: decodedData,
        deviceInfo: 'Android 12, Samsung Galaxy A54',
        locationData: 'Lusaka, Zambia',
      );

      if (fraudCheck.isBlocked) {
        print('❌ Transaction blocked due to fraud risk: ${fraudCheck.reason}');
        return;
      }

      print('✅ Fraud check passed (Risk: ${fraudCheck.riskLevel})');

      // Process payment
      final paymentResult = await PayMuleQR.scanAndPay(
        qrData: qrData,
        payerUserId: userId,
        customPin: pin,
      );

      if (paymentResult.success) {
        print('✅ Payment successful!');
        print('   Transaction ID: ${paymentResult.transactionId}');
        print('   Amount: K${paymentResult.amount}');
        print('   Offline: ${paymentResult.isOffline}');
      } else {
        print('❌ Payment failed: ${paymentResult.message}');
      }
    } catch (e) {
      print('❌ Customer payment example failed: $e');
    }
  }

  /// Example 3: Offline QR Payment
  static Future<void> offlinePaymentExample() async {
    print('📱 === OFFLINE PAYMENT EXAMPLE ===');

    try {
      // Initialize offline service
      await _offlineService.initialize();

      const merchantId = 'merchant_12345';
      const userId = 'customer_67890';
      const pin = '123456';

      // Generate offline QR
      final offlineQR = await _offlineService.generateOfflineQR(
        merchantId: merchantId,
        amount: 15.0,
        description: 'Offline payment for groceries',
        validityDuration: const Duration(hours: 48),
      );

      if (offlineQR.success) {
        print('✅ Offline QR generated');
        print('   QR ID: ${offlineQR.qrId}');
        print('   Expires: ${offlineQR.expiresAt}');

        // Simulate offline payment
        final offlinePayment = await _offlineService.processOfflinePayment(
          qrData: offlineQR.qrPayload!,
          payerUserId: userId,
          pin: pin,
        );

        if (offlinePayment.success) {
          print('✅ Offline payment processed');
          print('   Transaction ID: ${offlinePayment.transactionId}');
          print('   Confirmation Token: ${offlinePayment.confirmationToken}');

          // Check transaction status
          final status = await _offlineService.getOfflineTransactionStatus(
            offlinePayment.transactionId!,
          );

          if (status != null) {
            print('   Status: ${status.status}');
            print('   Created: ${status.createdAt}');
          }

          // Simulate sync when online
          print('\n📡 Syncing offline transactions...');
          final syncResult = await _offlineService.syncOfflineTransactions();
          
          if (syncResult.success) {
            print('✅ Sync completed');
            print('   Synced: ${syncResult.syncedCount} transactions');
            print('   Failed: ${syncResult.failedCount} transactions');
          }
        } else {
          print('❌ Offline payment failed: ${offlinePayment.message}');
        }
      } else {
        print('❌ Offline QR generation failed: ${offlineQR.message}');
      }
    } catch (e) {
      print('❌ Offline payment example failed: $e');
    }
  }

  /// Example 4: QR Scanner Integration
  static Widget buildQRScannerExample() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan QR Payment'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
      ),
      body: QRScannerWidget(
        onScan: (result) {
          if (result.success && result.decodedData != null) {
            print('✅ QR Scanned Successfully');
            print('   Merchant: ${result.decodedData!.merchantId}');
            print('   Amount: K${result.decodedData!.amount}');
            print('   Description: ${result.decodedData!.description}');
            
            // Show payment confirmation dialog
            _showPaymentConfirmation(result.decodedData!);
          } else {
            print('❌ QR Scan Failed: ${result.message}');
          }
        },
        onError: (error) {
          print('❌ Scanner Error: $error');
        },
        instructionText: 'Point camera at Pay Mule QR code\nEnsure good lighting for best results',
        overlayColor: const Color(0xFF2E7D32),
      ),
    );
  }

  /// Example 5: QR Generator Integration
  static Widget buildQRGeneratorExample(String merchantId) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Generate Payment QR'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: QRGeneratorWidget(
          merchantId: merchantId,
          allowCustomAmount: true,
          enableOfflineMode: true,
          onQRGenerated: (qrImage, qrData) {
            print('✅ QR Generated');
            print('   Data: ${qrData.substring(0, 50)}...');
            print('   Size: ${qrImage.length} bytes');
          },
          onError: (error) {
            print('❌ QR Generation Error: $error');
          },
        ),
      ),
    );
  }

  /// Generate multiple QR codes for merchant
  static Future<void> _generateMerchantQRCodes(String merchantId) async {
    print('\n🔄 Generating QR codes...');

    // Static QR (no amount)
    final staticQR = await _merchantService.generateMerchantQR(
      merchantId: merchantId,
      qrType: QRType.static,
      description: 'Pay to merchant',
    );

    if (staticQR.success) {
      print('✅ Static QR generated: ${staticQR.qrId}');
    }

    // Dynamic QRs with specific amounts
    final amounts = [10.0, 25.0, 50.0];
    for (final amount in amounts) {
      final dynamicQR = await _merchantService.generateMerchantQR(
        merchantId: merchantId,
        amount: amount,
        qrType: QRType.dynamic,
        description: 'K${amount.toStringAsFixed(0)} payment',
        expiryDuration: const Duration(hours: 24),
      );

      if (dynamicQR.success) {
        print('✅ Dynamic QR (K$amount) generated: ${dynamicQR.qrId}');
      }
    }
  }

  /// Show payment confirmation dialog
  static void _showPaymentConfirmation(ZambiaQRData qrData) {
    // This would show a confirmation dialog in a real app
    print('\n💳 Payment Confirmation Required:');
    print('   Merchant: ${qrData.merchantId}');
    print('   Amount: K${qrData.amount} ${qrData.currency}');
    print('   Description: ${qrData.description ?? 'Payment'}');
    print('   Provider: ${qrData.provider}');
    
    if (qrData.expiryTime != null) {
      print('   Expires: ${qrData.expiryTime}');
    }
  }

  /// Run all examples
  static Future<void> runAllExamples() async {
    print('🇿🇲 === ZAMBIAN QR PAYMENT SYSTEM EXAMPLES ===\n');

    await merchantExample();
    print('\n');
    
    await customerPaymentExample();
    print('\n');
    
    await offlinePaymentExample();
    print('\n');

    print('✅ All examples completed successfully!');
  }
}

/// Example usage in main.dart
/// 
/// ```dart
/// void main() async {
///   WidgetsFlutterBinding.ensureInitialized();
///   
///   // Run QR payment examples
///   await QRPaymentExample.runAllExamples();
///   
///   runApp(MyApp());
/// }
/// 
/// class MyApp extends StatelessWidget {
///   @override
///   Widget build(BuildContext context) {
///     return MaterialApp(
///       title: 'Pay Mule QR Payments',
///       theme: ThemeData(
///         primarySwatch: Colors.green,
///       ),
///       home: QRPaymentExample.buildQRScannerExample(),
///     );
///   }
/// }
/// ```
