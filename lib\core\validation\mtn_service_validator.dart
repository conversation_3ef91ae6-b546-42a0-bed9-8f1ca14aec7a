// MTN SERVICE VALIDATOR
// Validates MTN Mobile Money service endpoints and production status

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'environment_validator.dart';

class MTNService {
  static bool _isInitialized = false;
  static bool _isRealEndpoint = false;
  static String _currentEndpoint = '';
  static Map<String, dynamic> _serviceConfig = {};
  static Map<String, dynamic> _endpointStatus = {};

  // MTN endpoint configurations
  static const Map<String, String> _endpoints = {
    'production': 'https://api.mtn.com/v1',
    'staging': 'https://staging-api.mtn.com/v1',
    'sandbox': 'https://sandbox.mtnmomo.mtn.com/v1_0',
    'development': 'http://localhost:3001/mtn/v1',
  };

  /// Initialize MTN service validation
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🔍 Initializing MTN service validation...');

      // Detect current endpoint
      _currentEndpoint = await _detectCurrentEndpoint();
      _isRealEndpoint = await _validateRealEndpoint();
      _serviceConfig = await _loadServiceConfig();
      _endpointStatus = await _checkEndpointStatus();
      _isInitialized = true;

      print('✅ MTN service validation complete');
      print('🌐 Current endpoint: $_currentEndpoint');
      print('🔒 Real endpoint: $_isRealEndpoint');

    } catch (e) {
      print('❌ MTN service validation failed: $e');
      _isRealEndpoint = false;
    }
  }

  /// Check if MTN service is using real production endpoint
  static bool get isRealEndpoint {
    _ensureInitialized();
    return _isRealEndpoint;
  }

  /// Check if MTN service is using sandbox/test endpoint
  static bool get isSandboxEndpoint {
    _ensureInitialized();
    return !_isRealEndpoint;
  }

  /// Get current MTN endpoint
  static String get currentEndpoint {
    _ensureInitialized();
    return _currentEndpoint;
  }

  /// Get MTN service configuration
  static Map<String, dynamic> get config {
    _ensureInitialized();
    return Map.from(_serviceConfig);
  }

  /// Get endpoint status
  static Map<String, dynamic> get endpointStatus {
    _ensureInitialized();
    return Map.from(_endpointStatus);
  }

  /// Detect current MTN endpoint
  static Future<String> _detectCurrentEndpoint() async {
    try {
      // Check environment-based endpoint selection
      if (Environment.isProduction) {
        return _endpoints['production']!;
      } else if (Environment.isStaging) {
        return _endpoints['staging']!;
      } else if (Environment.isDevelopment) {
        return _endpoints['development']!;
      }

      // Check dart defines
      const endpoint = String.fromEnvironment('MTN_ENDPOINT', defaultValue: '');
      if (endpoint.isNotEmpty) {
        return endpoint;
      }

      // Check environment variables
      final envEndpoint = Platform.environment['MTN_ENDPOINT'];
      if (envEndpoint != null && envEndpoint.isNotEmpty) {
        return envEndpoint;
      }

      // Default to sandbox for non-production
      return _endpoints['sandbox']!;

    } catch (e) {
      print('❌ Failed to detect MTN endpoint: $e');
      return _endpoints['sandbox']!;
    }
  }

  /// Validate if endpoint is real production endpoint
  static Future<bool> _validateRealEndpoint() async {
    try {
      // Check if endpoint is production URL
      if (_currentEndpoint == _endpoints['production']) {
        // Additional validation for production endpoint
        return await _validateProductionEndpoint();
      }

      // Check for production indicators in URL
      if (_currentEndpoint.contains('api.mtn.com') && 
          !_currentEndpoint.contains('sandbox') && 
          !_currentEndpoint.contains('staging') &&
          !_currentEndpoint.contains('test')) {
        return await _validateProductionEndpoint();
      }

      // Not a real endpoint
      return false;

    } catch (e) {
      print('❌ Endpoint validation failed: $e');
      return false;
    }
  }

  /// Validate production endpoint with additional checks
  static Future<bool> _validateProductionEndpoint() async {
    try {
      print('🔍 Validating MTN production endpoint...');

      // Check SSL certificate
      final sslValid = await _validateSSLCertificate();
      if (!sslValid) {
        print('❌ SSL certificate validation failed');
        return false;
      }

      // Check endpoint accessibility
      final accessible = await _checkEndpointAccessibility();
      if (!accessible) {
        print('❌ Endpoint accessibility check failed');
        return false;
      }

      // Check API version and authentication
      final apiValid = await _validateAPIVersion();
      if (!apiValid) {
        print('❌ API version validation failed');
        return false;
      }

      print('✅ MTN production endpoint validated');
      return true;

    } catch (e) {
      print('❌ Production endpoint validation error: $e');
      return false;
    }
  }

  /// Validate SSL certificate
  static Future<bool> _validateSSLCertificate() async {
    try {
      final uri = Uri.parse(_currentEndpoint);
      
      if (uri.scheme != 'https') {
        print('❌ Endpoint is not HTTPS: ${uri.scheme}');
        return false;
      }

      // Create secure HTTP client
      final client = HttpClient();
      client.badCertificateCallback = (cert, host, port) {
        print('❌ Bad certificate for $host:$port');
        return false;
      };

      try {
        final request = await client.getUrl(uri);
        final response = await request.close();
        await response.drain();
        client.close();
        
        print('✅ SSL certificate valid');
        return true;
      } catch (e) {
        client.close();
        print('❌ SSL validation error: $e');
        return false;
      }

    } catch (e) {
      print('❌ SSL certificate check failed: $e');
      return false;
    }
  }

  /// Check endpoint accessibility
  static Future<bool> _checkEndpointAccessibility() async {
    try {
      final client = http.Client();
      
      try {
        final response = await client.head(
          Uri.parse(_currentEndpoint),
          headers: {
            'User-Agent': 'PayMule-Zambia/1.0',
            'Accept': 'application/json',
          },
        ).timeout(Duration(seconds: 10));

        client.close();

        // Check for valid response codes
        if (response.statusCode >= 200 && response.statusCode < 500) {
          print('✅ Endpoint accessible: ${response.statusCode}');
          return true;
        } else {
          print('❌ Endpoint returned: ${response.statusCode}');
          return false;
        }

      } catch (e) {
        client.close();
        print('❌ Endpoint accessibility error: $e');
        return false;
      }

    } catch (e) {
      print('❌ Accessibility check failed: $e');
      return false;
    }
  }

  /// Validate API version
  static Future<bool> _validateAPIVersion() async {
    try {
      final client = http.Client();
      
      try {
        final response = await client.get(
          Uri.parse('$_currentEndpoint/version'),
          headers: {
            'User-Agent': 'PayMule-Zambia/1.0',
            'Accept': 'application/json',
          },
        ).timeout(Duration(seconds: 10));

        client.close();

        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          final version = data['version'] ?? data['apiVersion'] ?? 'unknown';
          print('✅ API version: $version');
          return true;
        } else if (response.statusCode == 404) {
          // Version endpoint might not exist, but API is accessible
          print('✅ API accessible (version endpoint not found)');
          return true;
        } else {
          print('❌ API version check failed: ${response.statusCode}');
          return false;
        }

      } catch (e) {
        client.close();
        print('❌ API version error: $e');
        // Don't fail validation just because version endpoint is unavailable
        return true;
      }

    } catch (e) {
      print('❌ API validation failed: $e');
      return false;
    }
  }

  /// Load MTN service configuration
  static Future<Map<String, dynamic>> _loadServiceConfig() async {
    try {
      return {
        'endpoint': _currentEndpoint,
        'isProduction': _isRealEndpoint,
        'apiVersion': 'v1',
        'timeout': 30000,
        'retryAttempts': 3,
        'enableSSL': _currentEndpoint.startsWith('https'),
        'enableCertificatePinning': _isRealEndpoint,
        'subscriptionKey': Environment.isProduction ? 'PROD_KEY' : 'TEST_KEY',
        'environment': Environment.name,
        'features': {
          'sendMoney': true,
          'requestToPay': true,
          'getBalance': true,
          'getTransactionStatus': true,
          'validateAccount': true,
        },
        'limits': {
          'minAmount': 1.0,
          'maxAmount': 50000.0,
          'dailyLimit': 100000.0,
        },
      };
    } catch (e) {
      print('❌ Failed to load MTN service config: $e');
      return {};
    }
  }

  /// Check endpoint status
  static Future<Map<String, dynamic>> _checkEndpointStatus() async {
    try {
      final status = <String, dynamic>{};

      // Check all known endpoints
      for (final entry in _endpoints.entries) {
        final endpointType = entry.key;
        final endpointUrl = entry.value;

        try {
          final client = http.Client();
          final stopwatch = Stopwatch()..start();

          final response = await client.head(
            Uri.parse(endpointUrl),
            headers: {'User-Agent': 'PayMule-Health-Check/1.0'},
          ).timeout(Duration(seconds: 5));

          stopwatch.stop();
          client.close();

          status[endpointType] = {
            'url': endpointUrl,
            'accessible': response.statusCode < 500,
            'statusCode': response.statusCode,
            'responseTime': stopwatch.elapsedMilliseconds,
            'ssl': endpointUrl.startsWith('https'),
          };

        } catch (e) {
          status[endpointType] = {
            'url': endpointUrl,
            'accessible': false,
            'error': e.toString(),
            'ssl': endpointUrl.startsWith('https'),
          };
        }
      }

      return status;

    } catch (e) {
      print('❌ Failed to check endpoint status: $e');
      return {};
    }
  }

  /// Ensure MTN service is initialized
  static void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('MTN service not initialized. Call MTNService.initialize() first.');
    }
  }

  /// Force set MTN endpoint (for testing)
  static void setEndpoint(String endpoint, {bool? isReal}) {
    _currentEndpoint = endpoint;
    _isRealEndpoint = isReal ?? endpoint.contains('api.mtn.com');
    _isInitialized = true;
  }

  /// Get MTN service summary
  static Map<String, dynamic> getSummary() {
    _ensureInitialized();
    return {
      'currentEndpoint': _currentEndpoint,
      'isRealEndpoint': _isRealEndpoint,
      'isSandboxEndpoint': isSandboxEndpoint,
      'environment': Environment.name,
      'configKeys': _serviceConfig.keys.toList(),
      'endpointStatus': _endpointStatus,
    };
  }

  /// Validate MTN service requirements
  static bool validateRequirements({
    bool requireRealEndpoint = false,
    bool requireSandboxEndpoint = false,
    bool requireProduction = false,
  }) {
    _ensureInitialized();

    if (requireRealEndpoint && !isRealEndpoint) return false;
    if (requireSandboxEndpoint && isRealEndpoint) return false;
    if (requireProduction && !Environment.isProduction) return false;

    return true;
  }

  /// Test MTN service connectivity
  static Future<bool> testConnectivity() async {
    try {
      _ensureInitialized();
      
      final client = http.Client();
      final response = await client.head(
        Uri.parse(_currentEndpoint),
        headers: {'User-Agent': 'PayMule-Connectivity-Test/1.0'},
      ).timeout(Duration(seconds: 10));

      client.close();
      
      return response.statusCode < 500;
    } catch (e) {
      print('❌ MTN connectivity test failed: $e');
      return false;
    }
  }
}
