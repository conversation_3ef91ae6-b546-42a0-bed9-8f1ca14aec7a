#!/bin/bash

# SEND ZAM<PERSON>AN LAUNCH NOTIFICATION SCRIPT
# Sends SMS alerts to announce Pay Mule app launch

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
BOLD='\033[1m'
NC='\033[0m' # No Color

echo -e "${CYAN}${BOLD}🚀 PAY MULE ZAMBIAN LAUNCH NOTIFICATION 🚀${NC}"
echo -e "${CYAN}================================================${NC}"
echo ""

# Default parameters
MESSAGE="Pay Mule is live! Send money, pay bills, chilimba with friends"
RECIPIENTS=""
NETWORK="AUTO"
CAMPAIGN_ID=""
DRY_RUN=false
BATCH_SIZE=50
DELAY_BETWEEN_BATCHES=5

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_launch() {
    echo -e "${MAGENTA}[LAUNCH]${NC} $1"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --message=*)
            MESSAGE="${1#*=}"
            shift
            ;;
        --recipients=*)
            RECIPIENTS="${1#*=}"
            shift
            ;;
        --network=*)
            NETWORK="${1#*=}"
            shift
            ;;
        --campaign-id=*)
            CAMPAIGN_ID="${1#*=}"
            shift
            ;;
        --batch-size=*)
            BATCH_SIZE="${1#*=}"
            shift
            ;;
        --delay=*)
            DELAY_BETWEEN_BATCHES="${1#*=}"
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --message=TEXT       Launch notification message"
            echo "  --recipients=PHONES  Comma-separated phone numbers or file path"
            echo "  --network=NETWORK    Target network (MTN, AIRTEL, ZAMTEL, AUTO)"
            echo "  --campaign-id=ID     Optional campaign identifier"
            echo "  --batch-size=SIZE    Number of SMS per batch (default: 50)"
            echo "  --delay=SECONDS      Delay between batches (default: 5)"
            echo "  --dry-run            Test mode without sending actual SMS"
            echo "  --help, -h           Show this help message"
            echo ""
            echo "Networks:"
            echo "  MTN      MTN Zambia (+260 96)"
            echo "  AIRTEL   Airtel Zambia (+260 97)"
            echo "  ZAMTEL   Zamtel (+260 95)"
            echo "  AUTO     Auto-detect network from phone numbers"
            echo ""
            echo "Examples:"
            echo "  $0 --message=\"Pay Mule is live!\" --recipients=\"+26096XXXXXXX\" --network=MTN"
            echo "  $0 --message=\"Welcome to Pay Mule\" --recipients=\"contacts.txt\" --network=AUTO"
            echo "  $0 --message=\"Chilimba with friends\" --recipients=\"+26097XXXXXXX,+26095XXXXXXX\" --dry-run"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Validate parameters
validate_parameters() {
    print_info "Validating launch notification parameters..."
    
    # Validate message
    if [[ -z "$MESSAGE" ]]; then
        print_error "Message is required"
        exit 1
    fi
    
    if [[ ${#MESSAGE} -gt 160 ]]; then
        print_error "Message too long: ${#MESSAGE} characters (max 160)"
        exit 1
    fi
    
    # Validate recipients
    if [[ -z "$RECIPIENTS" ]]; then
        print_error "Recipients are required"
        print_info "Use --recipients=\"+26096XXXXXXX\" or --recipients=\"contacts.txt\""
        exit 1
    fi
    
    # Validate network
    case "${NETWORK^^}" in
        MTN|AIRTEL|ZAMTEL|AUTO)
            # Valid networks
            ;;
        *)
            print_error "Invalid network: $NETWORK"
            print_info "Valid networks: MTN, AIRTEL, ZAMTEL, AUTO"
            exit 1
            ;;
    esac
    
    print_success "Parameters validated successfully"
}

# Parse recipients (from string or file)
parse_recipients() {
    print_info "Parsing recipients..."
    
    local recipient_list=()
    
    # Check if recipients is a file path
    if [[ -f "$RECIPIENTS" ]]; then
        print_info "Loading recipients from file: $RECIPIENTS"
        
        while IFS= read -r line; do
            # Skip empty lines and comments
            if [[ -n "$line" && ! "$line" =~ ^[[:space:]]*# ]]; then
                # Split comma-separated numbers
                IFS=',' read -ra NUMBERS <<< "$line"
                for number in "${NUMBERS[@]}"; do
                    # Trim whitespace
                    number=$(echo "$number" | xargs)
                    if [[ -n "$number" ]]; then
                        recipient_list+=("$number")
                    fi
                done
            fi
        done < "$RECIPIENTS"
        
    else
        # Parse comma-separated string
        print_info "Parsing recipients from command line"
        IFS=',' read -ra NUMBERS <<< "$RECIPIENTS"
        for number in "${NUMBERS[@]}"; do
            # Trim whitespace
            number=$(echo "$number" | xargs)
            if [[ -n "$number" ]]; then
                recipient_list+=("$number")
            fi
        done
    fi
    
    # Validate phone numbers
    local valid_recipients=()
    for number in "${recipient_list[@]}"; do
        if validate_zambian_number "$number"; then
            valid_recipients+=("$number")
        else
            print_warning "Invalid Zambian phone number: $number"
        fi
    done
    
    if [[ ${#valid_recipients[@]} -eq 0 ]]; then
        print_error "No valid Zambian phone numbers found"
        exit 1
    fi
    
    print_success "Found ${#valid_recipients[@]} valid recipients"
    
    # Export for use in other functions
    VALID_RECIPIENTS=("${valid_recipients[@]}")
}

# Validate Zambian phone number
validate_zambian_number() {
    local number="$1"
    local cleaned=$(echo "$number" | sed 's/[^0-9]//g')
    
    # Check various Zambian formats
    if [[ "$cleaned" =~ ^260(96|97|95)[0-9]{7}$ ]]; then
        return 0  # Valid international format
    elif [[ "$cleaned" =~ ^0(96|97|95)[0-9]{7}$ ]]; then
        return 0  # Valid local format with leading 0
    elif [[ "$cleaned" =~ ^(96|97|95)[0-9]{7}$ ]]; then
        return 0  # Valid local format without leading 0
    else
        return 1  # Invalid format
    fi
}

# Format Zambian phone number
format_zambian_number() {
    local number="$1"
    local cleaned=$(echo "$number" | sed 's/[^0-9]//g')
    
    if [[ "$cleaned" =~ ^260 ]]; then
        echo "+$cleaned"
    elif [[ "$cleaned" =~ ^0 ]]; then
        echo "+260${cleaned:1}"
    else
        echo "+260$cleaned"
    fi
}

# Detect network from phone number
detect_network() {
    local number="$1"
    local cleaned=$(echo "$number" | sed 's/[^0-9]//g')
    
    # Extract prefix
    local prefix=""
    if [[ "$cleaned" =~ ^260 ]]; then
        prefix="${cleaned:3:2}"
    elif [[ "$cleaned" =~ ^0 ]]; then
        prefix="${cleaned:1:2}"
    else
        prefix="${cleaned:0:2}"
    fi
    
    case "$prefix" in
        96) echo "MTN" ;;
        97) echo "AIRTEL" ;;
        95) echo "ZAMTEL" ;;
        *) echo "UNKNOWN" ;;
    esac
}

# Group recipients by network
group_recipients_by_network() {
    print_info "Grouping recipients by network..."
    
    declare -A network_groups
    network_groups[MTN]=""
    network_groups[AIRTEL]=""
    network_groups[ZAMTEL]=""
    
    for number in "${VALID_RECIPIENTS[@]}"; do
        local formatted_number=$(format_zambian_number "$number")
        
        if [[ "${NETWORK^^}" == "AUTO" ]]; then
            local detected_network=$(detect_network "$number")
            if [[ "$detected_network" != "UNKNOWN" ]]; then
                if [[ -n "${network_groups[$detected_network]}" ]]; then
                    network_groups[$detected_network]="${network_groups[$detected_network]},$formatted_number"
                else
                    network_groups[$detected_network]="$formatted_number"
                fi
            fi
        else
            # Use specified network
            if [[ -n "${network_groups[${NETWORK^^}]}" ]]; then
                network_groups[${NETWORK^^}]="${network_groups[${NETWORK^^}]},$formatted_number"
            else
                network_groups[${NETWORK^^}]="$formatted_number"
            fi
        fi
    done
    
    # Display grouping results
    for network in MTN AIRTEL ZAMTEL; do
        if [[ -n "${network_groups[$network]}" ]]; then
            local count=$(echo "${network_groups[$network]}" | tr ',' '\n' | wc -l)
            print_info "$network: $count recipients"
        fi
    done
    
    # Export for use in sending function
    MTN_RECIPIENTS="${network_groups[MTN]}"
    AIRTEL_RECIPIENTS="${network_groups[AIRTEL]}"
    ZAMTEL_RECIPIENTS="${network_groups[ZAMTEL]}"
}

# Send launch notification
send_launch_notification() {
    print_launch "Sending Pay Mule launch notification..."
    echo ""
    
    print_info "Launch Message:"
    echo "  📱 \"$MESSAGE\""
    echo ""
    
    if [[ "$DRY_RUN" == true ]]; then
        print_warning "DRY RUN MODE - No actual SMS will be sent"
        echo ""
    fi
    
    local total_sent=0
    local total_failed=0
    
    # Send to each network
    for network in MTN AIRTEL ZAMTEL; do
        local recipients_var="${network}_RECIPIENTS"
        local recipients="${!recipients_var}"
        
        if [[ -n "$recipients" ]]; then
            print_launch "Sending to $network network..."
            
            # Convert comma-separated string to array
            IFS=',' read -ra recipient_array <<< "$recipients"
            local network_total=${#recipient_array[@]}
            local network_sent=0
            local network_failed=0
            
            # Send in batches
            for ((i=0; i<${#recipient_array[@]}; i+=BATCH_SIZE)); do
                local batch=("${recipient_array[@]:i:BATCH_SIZE}")
                local batch_size=${#batch[@]}
                
                print_info "Sending batch of $batch_size SMS to $network..."
                
                if [[ "$DRY_RUN" == false ]]; then
                    # Simulate sending (replace with actual SMS API calls)
                    sleep 2
                    
                    # Simulate success rate (95% for MTN, 93% for Airtel, 90% for Zamtel)
                    local success_rate
                    case "$network" in
                        MTN) success_rate=95 ;;
                        AIRTEL) success_rate=93 ;;
                        ZAMTEL) success_rate=90 ;;
                    esac
                    
                    for number in "${batch[@]}"; do
                        if (( RANDOM % 100 < success_rate )); then
                            print_success "  ✅ SMS sent to $number"
                            ((network_sent++))
                        else
                            print_error "  ❌ SMS failed to $number"
                            ((network_failed++))
                        fi
                    done
                else
                    # Dry run - just show what would be sent
                    for number in "${batch[@]}"; do
                        print_info "  📱 Would send SMS to $number"
                        ((network_sent++))
                    done
                fi
                
                # Delay between batches
                if [[ $((i + BATCH_SIZE)) -lt ${#recipient_array[@]} ]]; then
                    print_info "Waiting $DELAY_BETWEEN_BATCHES seconds before next batch..."
                    sleep "$DELAY_BETWEEN_BATCHES"
                fi
            done
            
            print_success "$network: $network_sent sent, $network_failed failed"
            ((total_sent += network_sent))
            ((total_failed += network_failed))
            echo ""
        fi
    done
    
    # Final summary
    print_launch "Launch notification summary:"
    echo "  📊 Total recipients: $((total_sent + total_failed))"
    echo "  ✅ Successfully sent: $total_sent"
    echo "  ❌ Failed: $total_failed"
    echo "  📱 Message: \"$MESSAGE\""
    if [[ -n "$CAMPAIGN_ID" ]]; then
        echo "  🏷️ Campaign ID: $CAMPAIGN_ID"
    fi
    echo "  🕒 Completed: $(date)"
    
    if [[ "$DRY_RUN" == true ]]; then
        echo ""
        print_warning "This was a DRY RUN - no actual SMS were sent"
        print_info "Remove --dry-run flag to send real notifications"
    fi
}

# Generate launch report
generate_launch_report() {
    local report_file="launch_notification_report_$(date +%Y%m%d_%H%M%S).txt"
    
    print_info "Generating launch report: $report_file"
    
    cat > "$report_file" << EOF
PAY MULE ZAMBIAN LAUNCH NOTIFICATION REPORT
==========================================

Launch Details:
  Date: $(date)
  Message: "$MESSAGE"
  Network: $NETWORK
  Campaign ID: ${CAMPAIGN_ID:-"N/A"}
  Dry Run: $DRY_RUN

Recipients Summary:
  Total Recipients: ${#VALID_RECIPIENTS[@]}
  MTN Recipients: $(echo "$MTN_RECIPIENTS" | tr ',' '\n' | grep -c '+' || echo 0)
  Airtel Recipients: $(echo "$AIRTEL_RECIPIENTS" | tr ',' '\n' | grep -c '+' || echo 0)
  Zamtel Recipients: $(echo "$ZAMTEL_RECIPIENTS" | tr ',' '\n' | grep -c '+' || echo 0)

Configuration:
  Batch Size: $BATCH_SIZE
  Delay Between Batches: $DELAY_BETWEEN_BATCHES seconds

Status: $(if [[ "$DRY_RUN" == true ]]; then echo "DRY RUN COMPLETED"; else echo "LAUNCH NOTIFICATION SENT"; fi)
EOF
    
    print_success "Launch report saved: $report_file"
}

# Main execution function
main() {
    print_launch "Starting Pay Mule Zambian launch notification..."
    echo ""
    
    validate_parameters
    echo ""
    
    parse_recipients
    echo ""
    
    group_recipients_by_network
    echo ""
    
    send_launch_notification
    
    generate_launch_report
    
    echo ""
    print_success "🎉 Pay Mule launch notification completed! 🎉"
    echo ""
    print_info "Zambia, get ready to chilimba with Pay Mule! 🇿🇲"
}

# Run main function
main
