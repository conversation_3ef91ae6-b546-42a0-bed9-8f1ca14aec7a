/// Connectivity Helper for Zambian QR Payment System
/// Provides reliable connectivity detection optimized for African networks
/// Handles 2G/3G/4G detection and network quality assessment

import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:logger/logger.dart';

/// Enhanced connectivity helper for Zambian networks
class Connectivity {
  static final Connectivity _instance = Connectivity._internal();
  factory Connectivity() => _instance;
  Connectivity._internal();

  static final Logger _logger = Logger();
  static final Connectivity _connectivity = Connectivity();
  static final InternetConnectionChecker _connectionChecker = InternetConnectionChecker();
  
  static StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  static StreamSubscription<InternetConnectionStatus>? _internetSubscription;
  
  static bool _isOnline = false;
  static ConnectivityResult _currentConnection = ConnectivityResult.none;
  static String _networkType = 'UNKNOWN';
  static double _networkQuality = 0.0;

  /// Initialize connectivity monitoring
  static Future<void> initialize() async {
    try {
      // Configure connection checker for African networks
      _connectionChecker.checkTimeout = const Duration(seconds: 10);
      _connectionChecker.checkInterval = const Duration(seconds: 30);
      
      // Add custom addresses for better African connectivity detection
      _connectionChecker.addresses = [
        AddressCheckOptions(
          address: InternetAddress('*******'),
          port: 53,
          timeout: const Duration(seconds: 5),
        ),
        AddressCheckOptions(
          address: InternetAddress('*******'),
          port: 53,
          timeout: const Duration(seconds: 5),
        ),
        // Add African DNS servers
        AddressCheckOptions(
          address: InternetAddress('************'), // Zambia DNS
          port: 53,
          timeout: const Duration(seconds: 3),
        ),
      ];

      // Check initial connectivity
      await _updateConnectivityStatus();
      
      // Start monitoring
      _startConnectivityMonitoring();
      _startInternetMonitoring();
      
      _logger.i('🌐 Connectivity monitoring initialized');
    } catch (e) {
      _logger.e('Failed to initialize connectivity monitoring: $e');
    }
  }

  /// Check if device is currently online
  static bool get isOnline => _isOnline;

  /// Get current connection type
  static ConnectivityResult get connectionType => _currentConnection;

  /// Get network type (2G, 3G, 4G, WIFI)
  static String get networkType => _networkType;

  /// Get network quality score (0.0 - 1.0)
  static double get networkQuality => _networkQuality;

  /// Check if connection is suitable for QR payments
  static bool get isSuitableForPayments {
    return _isOnline && _networkQuality > 0.3;
  }

  /// Check if should use offline mode
  static bool get shouldUseOfflineMode {
    return !_isOnline || _networkQuality < 0.2 || _networkType == '2G';
  }

  /// Perform comprehensive connectivity check
  static Future<bool> checkConnectivity() async {
    try {
      // Quick connectivity check
      final connectivityResult = await ConnectivityPlus().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        return false;
      }

      // Internet reachability check
      final hasInternet = await _connectionChecker.hasConnection;
      
      // Network quality assessment
      if (hasInternet) {
        await _assessNetworkQuality();
      }

      _isOnline = hasInternet;
      _currentConnection = connectivityResult;
      
      return hasInternet;
    } catch (e) {
      _logger.e('Connectivity check failed: $e');
      return false;
    }
  }

  /// Start monitoring connectivity changes
  static void _startConnectivityMonitoring() {
    _connectivitySubscription = ConnectivityPlus().onConnectivityChanged.listen(
      (ConnectivityResult result) async {
        _currentConnection = result;
        await _updateConnectivityStatus();
        
        _logger.i('🌐 Connectivity changed: $result');
      },
      onError: (error) {
        _logger.e('Connectivity monitoring error: $error');
      },
    );
  }

  /// Start monitoring internet connection status
  static void _startInternetMonitoring() {
    _internetSubscription = _connectionChecker.onStatusChange.listen(
      (InternetConnectionStatus status) async {
        final wasOnline = _isOnline;
        _isOnline = status == InternetConnectionStatus.connected;
        
        if (_isOnline && !wasOnline) {
          // Connection restored
          await _assessNetworkQuality();
          _logger.i('🌐 Internet connection restored');
          _notifyConnectionRestored();
        } else if (!_isOnline && wasOnline) {
          // Connection lost
          _logger.w('🌐 Internet connection lost');
          _notifyConnectionLost();
        }
      },
      onError: (error) {
        _logger.e('Internet monitoring error: $error');
      },
    );
  }

  /// Update connectivity status
  static Future<void> _updateConnectivityStatus() async {
    try {
      final connectivityResult = await ConnectivityPlus().checkConnectivity();
      _currentConnection = connectivityResult;
      
      // Determine network type
      _networkType = _determineNetworkType(connectivityResult);
      
      // Check internet connectivity
      if (connectivityResult != ConnectivityResult.none) {
        _isOnline = await _connectionChecker.hasConnection;
        
        if (_isOnline) {
          await _assessNetworkQuality();
        }
      } else {
        _isOnline = false;
        _networkQuality = 0.0;
      }
    } catch (e) {
      _logger.e('Failed to update connectivity status: $e');
    }
  }

  /// Assess network quality for payment processing
  static Future<void> _assessNetworkQuality() async {
    try {
      final stopwatch = Stopwatch()..start();
      
      // Perform speed test with small data
      final hasConnection = await _connectionChecker.hasConnection;
      stopwatch.stop();
      
      if (hasConnection) {
        final responseTime = stopwatch.elapsedMilliseconds;
        
        // Calculate quality score based on response time
        if (responseTime < 1000) {
          _networkQuality = 1.0; // Excellent
        } else if (responseTime < 3000) {
          _networkQuality = 0.8; // Good
        } else if (responseTime < 5000) {
          _networkQuality = 0.6; // Fair
        } else if (responseTime < 10000) {
          _networkQuality = 0.4; // Poor
        } else {
          _networkQuality = 0.2; // Very poor
        }

        // Adjust for network type
        switch (_networkType) {
          case '2G':
            _networkQuality = (_networkQuality * 0.5).clamp(0.0, 0.5);
            break;
          case '3G':
            _networkQuality = (_networkQuality * 0.8).clamp(0.0, 0.8);
            break;
          case '4G':
          case 'WIFI':
            // Keep full quality
            break;
        }
      } else {
        _networkQuality = 0.0;
      }
    } catch (e) {
      _logger.e('Network quality assessment failed: $e');
      _networkQuality = 0.3; // Default to moderate quality
    }
  }

  /// Determine network type from connectivity result
  static String _determineNetworkType(ConnectivityResult result) {
    switch (result) {
      case ConnectivityResult.wifi:
        return 'WIFI';
      case ConnectivityResult.ethernet:
        return 'ETHERNET';
      case ConnectivityResult.mobile:
        // For mobile, we'd need additional platform-specific code
        // to determine 2G/3G/4G. For now, assume 3G as default
        return '3G';
      case ConnectivityResult.none:
        return 'NONE';
      default:
        return 'UNKNOWN';
    }
  }

  /// Notify listeners of connection restoration
  static void _notifyConnectionRestored() {
    // Trigger offline sync
    _triggerOfflineSync();
  }

  /// Notify listeners of connection loss
  static void _notifyConnectionLost() {
    // Switch to offline mode
    _logger.i('🔄 Switching to offline mode');
  }

  /// Trigger offline transaction sync
  static void _triggerOfflineSync() {
    // This would trigger the offline sync process
    _logger.i('🔄 Triggering offline transaction sync');
  }

  /// Get connection status summary
  static Map<String, dynamic> getConnectionStatus() {
    return {
      'isOnline': _isOnline,
      'connectionType': _currentConnection.toString(),
      'networkType': _networkType,
      'networkQuality': _networkQuality,
      'suitableForPayments': isSuitableForPayments,
      'shouldUseOfflineMode': shouldUseOfflineMode,
    };
  }

  /// Test connection with specific timeout
  static Future<bool> testConnection({Duration? timeout}) async {
    try {
      final checker = InternetConnectionChecker();
      if (timeout != null) {
        checker.checkTimeout = timeout;
      }
      
      return await checker.hasConnection;
    } catch (e) {
      _logger.e('Connection test failed: $e');
      return false;
    }
  }

  /// Wait for connection to be restored
  static Future<bool> waitForConnection({
    Duration timeout = const Duration(minutes: 5),
    Duration checkInterval = const Duration(seconds: 5),
  }) async {
    final endTime = DateTime.now().add(timeout);
    
    while (DateTime.now().isBefore(endTime)) {
      if (await checkConnectivity()) {
        return true;
      }
      
      await Future.delayed(checkInterval);
    }
    
    return false;
  }

  /// Dispose connectivity monitoring
  static Future<void> dispose() async {
    await _connectivitySubscription?.cancel();
    await _internetSubscription?.cancel();
    _connectivitySubscription = null;
    _internetSubscription = null;
    
    _logger.i('🌐 Connectivity monitoring disposed');
  }

  /// Get network recommendations for QR payments
  static String getNetworkRecommendation() {
    if (!_isOnline) {
      return 'No internet connection. Using offline mode.';
    }
    
    if (_networkQuality >= 0.8) {
      return 'Excellent connection. All features available.';
    } else if (_networkQuality >= 0.6) {
      return 'Good connection. QR payments work well.';
    } else if (_networkQuality >= 0.4) {
      return 'Fair connection. QR payments may be slower.';
    } else if (_networkQuality >= 0.2) {
      return 'Poor connection. Consider offline mode.';
    } else {
      return 'Very poor connection. Offline mode recommended.';
    }
  }

  /// Check if specific operation should be performed online
  static bool shouldPerformOnline(String operationType) {
    switch (operationType) {
      case 'QR_GENERATION':
        return _isOnline && _networkQuality > 0.3;
      case 'QR_PAYMENT':
        return _isOnline && _networkQuality > 0.4;
      case 'MERCHANT_REGISTRATION':
        return _isOnline && _networkQuality > 0.5;
      case 'SYNC_TRANSACTIONS':
        return _isOnline && _networkQuality > 0.2;
      default:
        return _isOnline;
    }
  }
}
