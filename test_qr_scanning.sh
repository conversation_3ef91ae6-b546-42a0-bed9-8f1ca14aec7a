#!/bin/bash

# QR Scanning Test Script for Zambian Mobile Money
# Tests QR scanning on various devices and lighting conditions
# Optimized for low-end Android devices common in Zambia

set -e

# Default values
DEVICES="Tecno Spark 7,Itel P40"
LIGHT_CONDITIONS="low,normal"
AMOUNTS="10,250,5000"
TEST_DURATION=300  # 5 minutes
OUTPUT_DIR="test_results/qr_scanning"
VERBOSE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
🇿🇲 QR Scanning Test Script for Zambian Mobile Money

Usage: $0 [OPTIONS]

OPTIONS:
    --devices=DEVICE_LIST       Comma-separated list of devices to test
                               Default: "Tecno Spark 7,Itel P40"
    
    --light-conditions=CONDITIONS  Comma-separated list of lighting conditions
                                  Options: low, normal, bright, outdoor
                                  Default: "low,normal"
    
    --amounts=AMOUNT_LIST       Comma-separated list of amounts to test
                               Default: "10,250,5000"
    
    --duration=SECONDS          Test duration in seconds
                               Default: 300 (5 minutes)
    
    --output-dir=DIRECTORY      Output directory for test results
                               Default: "test_results/qr_scanning"
    
    --verbose                   Enable verbose output
    
    --help                      Show this help message

EXAMPLES:
    # Basic test with default settings
    $0
    
    # Test specific devices and conditions
    $0 --devices="Samsung Galaxy A12,Infinix Hot 10" --light-conditions="low,bright"
    
    # Extended test with custom amounts
    $0 --amounts="5,15,50,100,500,1000" --duration=600

DEVICE PROFILES:
    Tecno Spark 7    - Entry-level Android, 2GB RAM, basic camera
    Itel P40         - Ultra-budget Android, 1GB RAM, low-res camera
    Samsung Galaxy A12 - Mid-range Android, 4GB RAM, decent camera
    Infinix Hot 10   - Budget Android, 3GB RAM, average camera

LIGHTING CONDITIONS:
    low      - Simulates poor indoor lighting (common in rural areas)
    normal   - Standard indoor lighting
    bright   - Well-lit indoor environment
    outdoor  - Outdoor daylight conditions

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --devices=*)
            DEVICES="${1#*=}"
            shift
            ;;
        --light-conditions=*)
            LIGHT_CONDITIONS="${1#*=}"
            shift
            ;;
        --amounts=*)
            AMOUNTS="${1#*=}"
            shift
            ;;
        --duration=*)
            TEST_DURATION="${1#*=}"
            shift
            ;;
        --output-dir=*)
            OUTPUT_DIR="${1#*=}"
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Flutter is installed
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi
    
    # Check if ADB is available
    if ! command -v adb &> /dev/null; then
        print_error "ADB is not installed or not in PATH"
        exit 1
    fi
    
    # Check if devices are connected
    local connected_devices=$(adb devices | grep -v "List of devices" | grep "device$" | wc -l)
    if [ "$connected_devices" -eq 0 ]; then
        print_error "No Android devices connected"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to generate test QR codes
generate_test_qr_codes() {
    print_status "Generating test QR codes..."
    
    local qr_dir="$OUTPUT_DIR/qr_codes"
    mkdir -p "$qr_dir"
    
    IFS=',' read -ra AMOUNT_ARRAY <<< "$AMOUNTS"
    for amount in "${AMOUNT_ARRAY[@]}"; do
        # Generate QR code for each amount
        flutter test test/qr/generate_test_qr.dart \
            --dart-define=AMOUNT="$amount" \
            --dart-define=OUTPUT_FILE="$qr_dir/qr_${amount}.png" \
            > /dev/null 2>&1
        
        if [ $? -eq 0 ]; then
            print_success "Generated QR code for K$amount"
        else
            print_error "Failed to generate QR code for K$amount"
        fi
    done
}

# Function to setup device for testing
setup_device() {
    local device_id=$1
    local device_name=$2
    
    print_status "Setting up device: $device_name ($device_id)"
    
    # Install test app if not already installed
    adb -s "$device_id" install -r "build/app/outputs/flutter-apk/app-debug.apk" > /dev/null 2>&1
    
    # Clear app data
    adb -s "$device_id" shell pm clear com.zambiapay.app > /dev/null 2>&1
    
    # Grant camera permission
    adb -s "$device_id" shell pm grant com.zambiapay.app android.permission.CAMERA > /dev/null 2>&1
    
    # Set screen brightness for testing
    adb -s "$device_id" shell settings put system screen_brightness 128 > /dev/null 2>&1
    
    print_success "Device setup completed: $device_name"
}

# Function to simulate lighting conditions
simulate_lighting() {
    local device_id=$1
    local condition=$2
    
    case $condition in
        "low")
            # Reduce screen brightness to simulate low light
            adb -s "$device_id" shell settings put system screen_brightness 50
            ;;
        "normal")
            # Normal brightness
            adb -s "$device_id" shell settings put system screen_brightness 128
            ;;
        "bright")
            # High brightness
            adb -s "$device_id" shell settings put system screen_brightness 255
            ;;
        "outdoor")
            # Maximum brightness + outdoor mode if available
            adb -s "$device_id" shell settings put system screen_brightness 255
            ;;
    esac
}

# Function to run QR scanning test
run_scanning_test() {
    local device_id=$1
    local device_name=$2
    local light_condition=$3
    local amount=$4
    
    print_status "Testing QR scanning: $device_name, $light_condition light, K$amount"
    
    # Setup lighting condition
    simulate_lighting "$device_id" "$light_condition"
    
    # Start the app in QR scanning mode
    adb -s "$device_id" shell am start -n com.zambiapay.app/.MainActivity \
        --es "test_mode" "qr_scanning" \
        --es "test_amount" "$amount" \
        --es "light_condition" "$light_condition" > /dev/null 2>&1
    
    # Wait for app to start
    sleep 3
    
    # Simulate QR code display (in real testing, this would be a physical QR code)
    local qr_file="$OUTPUT_DIR/qr_codes/qr_${amount}.png"
    if [ -f "$qr_file" ]; then
        # Push QR code to device for testing
        adb -s "$device_id" push "$qr_file" "/sdcard/test_qr.png" > /dev/null 2>&1
        
        # Trigger QR scanning test
        adb -s "$device_id" shell am broadcast \
            -a com.zambiapay.app.TEST_QR_SCAN \
            --es "qr_file" "/sdcard/test_qr.png" > /dev/null 2>&1
    fi
    
    # Wait for scanning to complete
    sleep 5
    
    # Get test results
    local result=$(adb -s "$device_id" shell cat /sdcard/qr_test_result.txt 2>/dev/null || echo "FAILED")
    
    # Log results
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "$timestamp,$device_name,$light_condition,K$amount,$result" >> "$OUTPUT_DIR/test_results.csv"
    
    if [[ "$result" == *"SUCCESS"* ]]; then
        print_success "✅ Scan successful: $device_name, $light_condition, K$amount"
    else
        print_error "❌ Scan failed: $device_name, $light_condition, K$amount"
    fi
    
    # Clean up
    adb -s "$device_id" shell rm -f /sdcard/test_qr.png /sdcard/qr_test_result.txt > /dev/null 2>&1
}

# Function to analyze test results
analyze_results() {
    print_status "Analyzing test results..."
    
    local results_file="$OUTPUT_DIR/test_results.csv"
    if [ ! -f "$results_file" ]; then
        print_error "No test results found"
        return
    fi
    
    local total_tests=$(wc -l < "$results_file")
    local successful_tests=$(grep -c "SUCCESS" "$results_file" || echo "0")
    local failed_tests=$((total_tests - successful_tests))
    local success_rate=$((successful_tests * 100 / total_tests))
    
    print_status "📊 Test Results Summary:"
    echo "  Total Tests: $total_tests"
    echo "  Successful: $successful_tests"
    echo "  Failed: $failed_tests"
    echo "  Success Rate: $success_rate%"
    
    # Analyze by device
    print_status "📱 Results by Device:"
    IFS=',' read -ra DEVICE_ARRAY <<< "$DEVICES"
    for device in "${DEVICE_ARRAY[@]}"; do
        local device_tests=$(grep "$device" "$results_file" | wc -l)
        local device_success=$(grep "$device" "$results_file" | grep -c "SUCCESS" || echo "0")
        local device_rate=$((device_success * 100 / device_tests))
        echo "  $device: $device_success/$device_tests ($device_rate%)"
    done
    
    # Analyze by lighting condition
    print_status "💡 Results by Lighting Condition:"
    IFS=',' read -ra LIGHT_ARRAY <<< "$LIGHT_CONDITIONS"
    for condition in "${LIGHT_ARRAY[@]}"; do
        local condition_tests=$(grep "$condition" "$results_file" | wc -l)
        local condition_success=$(grep "$condition" "$results_file" | grep -c "SUCCESS" || echo "0")
        local condition_rate=$((condition_success * 100 / condition_tests))
        echo "  $condition: $condition_success/$condition_tests ($condition_rate%)"
    done
    
    # Generate detailed report
    cat > "$OUTPUT_DIR/test_report.md" << EOF
# QR Scanning Test Report

## Test Configuration
- **Devices**: $DEVICES
- **Lighting Conditions**: $LIGHT_CONDITIONS
- **Amounts**: $AMOUNTS
- **Test Duration**: $TEST_DURATION seconds
- **Test Date**: $(date)

## Summary
- **Total Tests**: $total_tests
- **Successful**: $successful_tests
- **Failed**: $failed_tests
- **Success Rate**: $success_rate%

## Detailed Results
$(cat "$results_file" | column -t -s ',')

## Recommendations
$(if [ $success_rate -lt 80 ]; then
    echo "- Success rate below 80%. Consider improving camera handling or QR generation."
fi)
$(if grep -q "low.*FAILED" "$results_file"; then
    echo "- Low light conditions showing failures. Implement better low-light scanning."
fi)
$(if grep -q "Itel P40.*FAILED" "$results_file"; then
    echo "- Budget devices showing issues. Optimize for low-end hardware."
fi)

EOF
    
    print_success "Detailed report saved to: $OUTPUT_DIR/test_report.md"
}

# Main execution
main() {
    print_status "🇿🇲 Starting QR Scanning Tests for Zambian Mobile Money"
    print_status "Devices: $DEVICES"
    print_status "Light Conditions: $LIGHT_CONDITIONS"
    print_status "Amounts: $AMOUNTS"
    print_status "Output Directory: $OUTPUT_DIR"
    
    # Check prerequisites
    check_prerequisites
    
    # Generate test QR codes
    generate_test_qr_codes
    
    # Initialize results file
    echo "timestamp,device,light_condition,amount,result" > "$OUTPUT_DIR/test_results.csv"
    
    # Get connected devices
    local connected_devices=($(adb devices | grep -v "List of devices" | grep "device$" | awk '{print $1}'))
    
    if [ ${#connected_devices[@]} -eq 0 ]; then
        print_error "No devices connected"
        exit 1
    fi
    
    # Run tests on each device
    IFS=',' read -ra DEVICE_ARRAY <<< "$DEVICES"
    for device_name in "${DEVICE_ARRAY[@]}"; do
        # For this example, we'll use the first connected device
        # In a real scenario, you'd match device names to actual devices
        local device_id="${connected_devices[0]}"
        
        print_status "Testing device: $device_name"
        setup_device "$device_id" "$device_name"
        
        # Test each lighting condition
        IFS=',' read -ra LIGHT_ARRAY <<< "$LIGHT_CONDITIONS"
        for light_condition in "${LIGHT_ARRAY[@]}"; do
            # Test each amount
            IFS=',' read -ra AMOUNT_ARRAY <<< "$AMOUNTS"
            for amount in "${AMOUNT_ARRAY[@]}"; do
                run_scanning_test "$device_id" "$device_name" "$light_condition" "$amount"
                sleep 2  # Brief pause between tests
            done
        done
    done
    
    # Analyze results
    analyze_results
    
    print_success "🏁 QR Scanning tests completed!"
    print_status "Results saved to: $OUTPUT_DIR"
}

# Run main function
main "$@"
