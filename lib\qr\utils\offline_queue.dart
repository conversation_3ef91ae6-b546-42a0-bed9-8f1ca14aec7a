/// Offline Queue System for Zambian QR Payments
/// Manages offline transactions with priority queuing and automatic sync
/// Optimized for rural connectivity and intermittent network access

import 'dart:convert';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

import '../../core/constants/app_constants.dart';
import '../../core/security/encryption_service.dart';
import '../../data/database/database_helper.dart';
import '../zambia_qr_format.dart';
import 'connectivity_helper.dart';

/// Transaction status enumeration
enum Status {
  PENDING,
  PROCESSING,
  COMPLETED,
  FAILED,
  CANCELLED,
  EXPIRED,
}

/// Priority levels for offline transactions
enum Priority {
  LOW(1),
  NORMAL(2),
  HIGH(3),
  URGENT(4);

  const Priority(this.value);
  final int value;
}

/// Offline Queue Manager
class OfflineQueue {
  static final OfflineQueue _instance = OfflineQueue._internal();
  factory OfflineQueue() => _instance;
  OfflineQueue._internal();

  static final Logger _logger = Logger();
  static final Uuid _uuid = Uuid();
  static final DatabaseHelper _dbHelper = DatabaseHelper();
  static final EncryptionService _encryption = EncryptionService();

  static bool _isInitialized = false;
  static bool _isSyncing = false;

  /// Initialize offline queue system
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _encryption.initialize();
      await _createQueueTables();
      await _startQueueMonitoring();
      
      _isInitialized = true;
      _logger.i('📦 Offline queue system initialized');
    } catch (e) {
      _logger.e('Failed to initialize offline queue: $e');
      rethrow;
    }
  }

  /// Add transaction to offline queue
  static Future<String> add({
    required ZambiaQRData transaction,
    required Status status,
    Priority priority = Priority.NORMAL,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      final queueId = _uuid.v4();
      final now = DateTime.now().millisecondsSinceEpoch;

      // Encrypt sensitive transaction data
      final encryptedData = await _encryption.encryptData(
        jsonEncode(transaction.toJson()),
      );

      // Create queue item
      final queueItem = {
        'id': queueId,
        'transaction_data': encryptedData,
        'status': status.name,
        'priority': priority.value,
        'created_at': now,
        'updated_at': now,
        'retry_count': 0,
        'max_retries': AppConstants.maxRetryAttempts,
        'next_retry_at': now,
        'expires_at': now + (24 * 60 * 60 * 1000), // 24 hours
        'metadata': metadata != null ? jsonEncode(metadata) : null,
      };

      await _dbHelper.insert('offline_queue', queueItem);

      _logger.i('📦 Transaction added to offline queue: $queueId');
      _logger.d('   Amount: K${transaction.amount}');
      _logger.d('   Merchant: ${transaction.merchantId.substring(0, 8)}...');
      _logger.d('   Status: ${status.name}');
      _logger.d('   Priority: ${priority.name}');

      // Trigger immediate sync if online
      if (Connectivity.isOnline && Connectivity.isSuitableForPayments) {
        _triggerSync();
      }

      return queueId;
    } catch (e) {
      _logger.e('Failed to add transaction to queue: $e');
      rethrow;
    }
  }

  /// Update transaction status in queue
  static Future<bool> updateStatus({
    required String queueId,
    required Status status,
    String? errorMessage,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      final updates = <String, dynamic>{
        'status': status.name,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      };

      if (errorMessage != null) {
        updates['error_message'] = errorMessage;
      }

      if (additionalData != null) {
        final existingMetadata = await _getQueueItemMetadata(queueId);
        final mergedMetadata = {...existingMetadata, ...additionalData};
        updates['metadata'] = jsonEncode(mergedMetadata);
      }

      final rowsAffected = await _dbHelper.update(
        'offline_queue',
        updates,
        where: 'id = ?',
        whereArgs: [queueId],
      );

      if (rowsAffected > 0) {
        _logger.i('📦 Queue item status updated: $queueId -> ${status.name}');
        return true;
      } else {
        _logger.w('📦 Queue item not found: $queueId');
        return false;
      }
    } catch (e) {
      _logger.e('Failed to update queue item status: $e');
      return false;
    }
  }

  /// Get pending transactions from queue
  static Future<List<QueueItem>> getPendingTransactions({
    int? limit,
    Priority? minPriority,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      String whereClause = 'status IN (?, ?) AND expires_at > ? AND next_retry_at <= ?';
      List<dynamic> whereArgs = [
        Status.PENDING.name,
        Status.FAILED.name,
        DateTime.now().millisecondsSinceEpoch,
        DateTime.now().millisecondsSinceEpoch,
      ];

      if (minPriority != null) {
        whereClause += ' AND priority >= ?';
        whereArgs.add(minPriority.value);
      }

      final results = await _dbHelper.query(
        'offline_queue',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'priority DESC, created_at ASC',
        limit: limit,
      );

      final queueItems = <QueueItem>[];
      for (final result in results) {
        try {
          final queueItem = await _parseQueueItem(result);
          queueItems.add(queueItem);
        } catch (e) {
          _logger.e('Failed to parse queue item ${result['id']}: $e');
        }
      }

      return queueItems;
    } catch (e) {
      _logger.e('Failed to get pending transactions: $e');
      return [];
    }
  }

  /// Process offline queue (sync with server)
  static Future<QueueSyncResult> processQueue() async {
    if (_isSyncing) {
      return QueueSyncResult(
        success: false,
        message: 'Sync already in progress',
        processedCount: 0,
        failedCount: 0,
      );
    }

    _isSyncing = true;
    _logger.i('📦 Starting offline queue processing');

    try {
      if (!Connectivity.isOnline) {
        return QueueSyncResult(
          success: false,
          message: 'No internet connection',
          processedCount: 0,
          failedCount: 0,
        );
      }

      final pendingItems = await getPendingTransactions(limit: 50);
      if (pendingItems.isEmpty) {
        return QueueSyncResult(
          success: true,
          message: 'No pending transactions to process',
          processedCount: 0,
          failedCount: 0,
        );
      }

      int processedCount = 0;
      int failedCount = 0;

      for (final item in pendingItems) {
        try {
          await updateStatus(
            queueId: item.id,
            status: Status.PROCESSING,
          );

          final success = await _processQueueItem(item);
          
          if (success) {
            await updateStatus(
              queueId: item.id,
              status: Status.COMPLETED,
            );
            processedCount++;
          } else {
            await _handleFailedItem(item);
            failedCount++;
          }
        } catch (e) {
          _logger.e('Failed to process queue item ${item.id}: $e');
          await _handleFailedItem(item);
          failedCount++;
        }
      }

      _logger.i('📦 Queue processing completed: $processedCount processed, $failedCount failed');

      return QueueSyncResult(
        success: true,
        message: 'Queue processing completed',
        processedCount: processedCount,
        failedCount: failedCount,
      );
    } catch (e) {
      _logger.e('Queue processing failed: $e');
      return QueueSyncResult(
        success: false,
        message: 'Queue processing failed: ${e.toString()}',
        processedCount: 0,
        failedCount: 0,
      );
    } finally {
      _isSyncing = false;
    }
  }

  /// Get queue statistics
  static Future<QueueStats> getStats() async {
    try {
      if (!_isInitialized) await initialize();

      final stats = await _dbHelper.rawQuery('''
        SELECT 
          status,
          COUNT(*) as count,
          AVG(priority) as avg_priority
        FROM offline_queue 
        WHERE expires_at > ?
        GROUP BY status
      ''', [DateTime.now().millisecondsSinceEpoch]);

      final queueStats = QueueStats();
      
      for (final stat in stats) {
        final status = stat['status'] as String;
        final count = stat['count'] as int;
        
        switch (status) {
          case 'PENDING':
            queueStats.pendingCount = count;
            break;
          case 'PROCESSING':
            queueStats.processingCount = count;
            break;
          case 'COMPLETED':
            queueStats.completedCount = count;
            break;
          case 'FAILED':
            queueStats.failedCount = count;
            break;
        }
      }

      // Get total size
      final totalResult = await _dbHelper.rawQuery(
        'SELECT COUNT(*) as total FROM offline_queue WHERE expires_at > ?',
        [DateTime.now().millisecondsSinceEpoch],
      );
      queueStats.totalCount = totalResult.first['total'] as int;

      return queueStats;
    } catch (e) {
      _logger.e('Failed to get queue stats: $e');
      return QueueStats();
    }
  }

  /// Clear completed transactions from queue
  static Future<int> clearCompleted({Duration? olderThan}) async {
    try {
      if (!_isInitialized) await initialize();

      final cutoffTime = olderThan != null
          ? DateTime.now().subtract(olderThan).millisecondsSinceEpoch
          : DateTime.now().subtract(const Duration(days: 7)).millisecondsSinceEpoch;

      final deletedCount = await _dbHelper.delete(
        'offline_queue',
        where: 'status = ? AND updated_at < ?',
        whereArgs: [Status.COMPLETED.name, cutoffTime],
      );

      _logger.i('📦 Cleared $deletedCount completed transactions from queue');
      return deletedCount;
    } catch (e) {
      _logger.e('Failed to clear completed transactions: $e');
      return 0;
    }
  }

  /// Remove expired transactions from queue
  static Future<int> removeExpired() async {
    try {
      if (!_isInitialized) await initialize();

      final now = DateTime.now().millisecondsSinceEpoch;
      
      final deletedCount = await _dbHelper.delete(
        'offline_queue',
        where: 'expires_at <= ?',
        whereArgs: [now],
      );

      if (deletedCount > 0) {
        _logger.i('📦 Removed $deletedCount expired transactions from queue');
      }

      return deletedCount;
    } catch (e) {
      _logger.e('Failed to remove expired transactions: $e');
      return 0;
    }
  }

  /// Create database tables for offline queue
  static Future<void> _createQueueTables() async {
    final db = await _dbHelper.database;

    await db.execute('''
      CREATE TABLE IF NOT EXISTS offline_queue (
        id TEXT PRIMARY KEY,
        transaction_data TEXT NOT NULL,
        status TEXT NOT NULL,
        priority INTEGER NOT NULL DEFAULT 2,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        retry_count INTEGER NOT NULL DEFAULT 0,
        max_retries INTEGER NOT NULL DEFAULT 3,
        next_retry_at INTEGER NOT NULL,
        expires_at INTEGER NOT NULL,
        error_message TEXT,
        metadata TEXT
      )
    ''');

    // Create indexes for performance
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_offline_queue_status 
      ON offline_queue(status)
    ''');

    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_offline_queue_priority 
      ON offline_queue(priority DESC, created_at ASC)
    ''');

    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_offline_queue_retry 
      ON offline_queue(next_retry_at)
    ''');
  }

  /// Start monitoring queue for automatic processing
  static Future<void> _startQueueMonitoring() async {
    // Monitor connectivity changes
    // In a real implementation, this would listen to connectivity events
    _logger.i('📦 Queue monitoring started');
  }

  /// Parse queue item from database result
  static Future<QueueItem> _parseQueueItem(Map<String, dynamic> result) async {
    final encryptedData = result['transaction_data'] as String;
    final decryptedData = await _encryption.decryptData(encryptedData);
    final transactionJson = jsonDecode(decryptedData) as Map<String, dynamic>;
    final transaction = ZambiaQRData.fromJson(transactionJson);

    return QueueItem(
      id: result['id'] as String,
      transaction: transaction,
      status: Status.values.firstWhere(
        (s) => s.name == result['status'],
        orElse: () => Status.PENDING,
      ),
      priority: Priority.values.firstWhere(
        (p) => p.value == result['priority'],
        orElse: () => Priority.NORMAL,
      ),
      createdAt: DateTime.fromMillisecondsSinceEpoch(result['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(result['updated_at'] as int),
      retryCount: result['retry_count'] as int,
      maxRetries: result['max_retries'] as int,
      nextRetryAt: DateTime.fromMillisecondsSinceEpoch(result['next_retry_at'] as int),
      expiresAt: DateTime.fromMillisecondsSinceEpoch(result['expires_at'] as int),
      errorMessage: result['error_message'] as String?,
      metadata: result['metadata'] != null 
          ? jsonDecode(result['metadata'] as String) as Map<String, dynamic>
          : {},
    );
  }

  /// Get queue item metadata
  static Future<Map<String, dynamic>> _getQueueItemMetadata(String queueId) async {
    try {
      final results = await _dbHelper.query(
        'offline_queue',
        columns: ['metadata'],
        where: 'id = ?',
        whereArgs: [queueId],
        limit: 1,
      );

      if (results.isNotEmpty && results.first['metadata'] != null) {
        return jsonDecode(results.first['metadata'] as String) as Map<String, dynamic>;
      }
      
      return {};
    } catch (e) {
      _logger.e('Failed to get queue item metadata: $e');
      return {};
    }
  }

  /// Process individual queue item
  static Future<bool> _processQueueItem(QueueItem item) async {
    try {
      // This would integrate with your payment processing system
      // For now, simulate processing
      await Future.delayed(const Duration(seconds: 1));
      
      // Simulate success/failure based on network quality
      final networkQuality = Connectivity.networkQuality;
      return networkQuality > 0.3;
    } catch (e) {
      _logger.e('Failed to process queue item: $e');
      return false;
    }
  }

  /// Handle failed queue item
  static Future<void> _handleFailedItem(QueueItem item) async {
    final newRetryCount = item.retryCount + 1;
    
    if (newRetryCount >= item.maxRetries) {
      // Max retries reached, mark as failed
      await updateStatus(
        queueId: item.id,
        status: Status.FAILED,
        errorMessage: 'Max retries exceeded',
      );
    } else {
      // Schedule retry with exponential backoff
      final retryDelay = Duration(minutes: (newRetryCount * newRetryCount * 5));
      final nextRetryAt = DateTime.now().add(retryDelay).millisecondsSinceEpoch;
      
      await _dbHelper.update(
        'offline_queue',
        {
          'status': Status.PENDING.name,
          'retry_count': newRetryCount,
          'next_retry_at': nextRetryAt,
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'id = ?',
        whereArgs: [item.id],
      );
    }
  }

  /// Trigger queue sync
  static void _triggerSync() {
    // This would trigger the sync process
    // In a real implementation, this might use a background service
    Future.delayed(const Duration(seconds: 2), () {
      processQueue();
    });
  }
}

/// Queue item model
class QueueItem {
  final String id;
  final ZambiaQRData transaction;
  final Status status;
  final Priority priority;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int retryCount;
  final int maxRetries;
  final DateTime nextRetryAt;
  final DateTime expiresAt;
  final String? errorMessage;
  final Map<String, dynamic> metadata;

  QueueItem({
    required this.id,
    required this.transaction,
    required this.status,
    required this.priority,
    required this.createdAt,
    required this.updatedAt,
    required this.retryCount,
    required this.maxRetries,
    required this.nextRetryAt,
    required this.expiresAt,
    this.errorMessage,
    this.metadata = const {},
  });
}

/// Queue sync result
class QueueSyncResult {
  final bool success;
  final String message;
  final int processedCount;
  final int failedCount;

  QueueSyncResult({
    required this.success,
    required this.message,
    required this.processedCount,
    required this.failedCount,
  });
}

/// Queue statistics
class QueueStats {
  int totalCount = 0;
  int pendingCount = 0;
  int processingCount = 0;
  int completedCount = 0;
  int failedCount = 0;

  double get successRate {
    final total = completedCount + failedCount;
    return total > 0 ? completedCount / total : 0.0;
  }
}
