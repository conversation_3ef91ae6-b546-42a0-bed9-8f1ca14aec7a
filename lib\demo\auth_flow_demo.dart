// AUTHENTICATION FLOW DEMO
// Demonstrates the expected flow: SIM verification → SMS OTP → Security PIN

import 'package:flutter/material.dart';
import '../core/auth/zambian_auth_flow.dart';
import '../core/auth/zambia_sim_service.dart';
import '../core/auth/sms_otp_service.dart';
import '../core/auth/security_pin_service.dart';

class AuthFlowDemo extends StatefulWidget {
  @override
  _AuthFlowDemoState createState() => _AuthFlowDemoState();
}

class _AuthFlowDemoState extends State<AuthFlowDemo> {
  AuthStep currentStep = AuthStep.simVerification;
  ZambianSIMInfo? simInfo;
  String? phoneNumber;
  bool isLoading = false;
  String? statusMessage;
  
  // OTP related
  String otpCode = '';
  int remainingTime = 0;
  Timer? otpTimer;
  
  // PIN related
  String pin = '';
  String confirmPin = '';
  bool pinVisible = false;

  @override
  void initState() {
    super.initState();
    _startAuthFlow();
  }

  @override
  void dispose() {
    otpTimer?.cancel();
    super.dispose();
  }

  /// Start the complete authentication flow
  Future<void> _startAuthFlow() async {
    setState(() {
      isLoading = true;
      statusMessage = 'Starting authentication...';
    });

    try {
      // EXPECTED FLOW IMPLEMENTATION
      
      // Step 1: Auto-detect MTN/Airtel/Zamtel
      await _performSIMVerification();
      
    } catch (e) {
      setState(() {
        isLoading = false;
        statusMessage = 'Authentication failed: $e';
      });
    }
  }

  /// Step 1: SIM Verification
  Future<void> _performSIMVerification() async {
    setState(() {
      currentStep = AuthStep.simVerification;
      statusMessage = 'Detecting Zambian SIM card...';
    });

    // await ZambiaSIM.verify(); // Auto-detects MTN/Airtel/Zamtel
    simInfo = await ZambiaSIM.verify();

    if (simInfo != null && simInfo!.isZambianSIM) {
      final networkName = ZambiaSIM.getNetworkDisplayName(simInfo!.network);
      setState(() {
        statusMessage = '✅ $networkName detected';
      });
      
      // Move to SMS OTP if phone number available
      if (simInfo!.phoneNumber != null) {
        phoneNumber = simInfo!.phoneNumber;
        await _performSMSOTP();
      } else {
        // Ask user to enter phone number
        _showPhoneNumberDialog();
      }
    } else {
      setState(() {
        isLoading = false;
        statusMessage = '❌ Please insert a Zambian SIM card (MTN, Airtel, or Zamtel)';
      });
    }
  }

  /// Step 2: SMS OTP
  Future<void> _performSMSOTP() async {
    if (phoneNumber == null) return;

    setState(() {
      currentStep = AuthStep.smsOTP;
      statusMessage = 'Sending SMS verification...';
    });

    // SMSOTP.request(); // Real SMS sent to your number
    final otpResult = await SMSOTP.request(
      phoneNumber: phoneNumber!,
      network: simInfo?.network,
    );

    if (otpResult.success) {
      setState(() {
        isLoading = false;
        statusMessage = '📱 SMS sent to $phoneNumber';
      });
      _startOTPTimer();
    } else {
      setState(() {
        isLoading = false;
        statusMessage = '❌ ${otpResult.message}';
      });
    }
  }

  /// Step 3: Security PIN Setup
  Future<void> _performSecurityPINSetup() async {
    setState(() {
      currentStep = AuthStep.securityPIN;
      isLoading = false;
      statusMessage = 'Setup your security PIN (minimum 6 digits)';
    });
  }

  /// Verify OTP code
  void _verifyOTP() {
    if (otpCode.length < 6) return;

    final verification = SMSOTP.verify(otpCode);
    
    if (verification.isValid) {
      setState(() {
        statusMessage = '✅ Phone number verified';
      });
      otpTimer?.cancel();
      _performSecurityPINSetup();
    } else {
      setState(() {
        statusMessage = '❌ ${verification.message}';
      });
      
      if (verification.isExpired) {
        _showResendOption();
      }
    }
  }

  /// Setup security PIN
  Future<void> _setupSecurityPIN() async {
    if (pin.length < 6 || pin != confirmPin) {
      setState(() {
        statusMessage = '❌ PIN must be at least 6 digits and match confirmation';
      });
      return;
    }

    setState(() {
      isLoading = true;
      statusMessage = 'Setting up security PIN...';
    });

    // setupSecurityPIN(minLength: 6);
    final result = await SecurityPINService.setupSecurityPIN(
      pin: pin,
      confirmPin: confirmPin,
      minLength: 6,
      enableBiometric: true,
    );

    if (result.success) {
      setState(() {
        currentStep = AuthStep.completed;
        isLoading = false;
        statusMessage = '🎉 Authentication completed successfully!';
      });
      
      if (result.requiresBiometric) {
        _showBiometricSetupDialog();
      }
    } else {
      setState(() {
        isLoading = false;
        statusMessage = '❌ ${result.message}';
      });
    }
  }

  /// Start OTP countdown timer
  void _startOTPTimer() {
    remainingTime = 300; // 5 minutes
    otpTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        remainingTime--;
      });
      
      if (remainingTime <= 0) {
        timer.cancel();
        setState(() {
          statusMessage = '⏰ OTP expired. Please request a new code.';
        });
      }
    });
  }

  /// Show phone number input dialog
  void _showPhoneNumberDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Enter Phone Number'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Please enter your Zambian phone number:'),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                labelText: 'Phone Number',
                hintText: '+260 96 XXX XXXX',
                prefixText: '+260 ',
              ),
              keyboardType: TextInputType.phone,
              onChanged: (value) {
                phoneNumber = '+260$value';
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              if (phoneNumber != null) {
                _performSMSOTP();
              }
            },
            child: Text('Continue'),
          ),
        ],
      ),
    );
  }

  /// Show resend OTP option
  void _showResendOption() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('OTP Expired'),
        content: Text('Your verification code has expired. Would you like to request a new one?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performSMSOTP();
            },
            child: Text('Resend'),
          ),
        ],
      ),
    );
  }

  /// Show biometric setup dialog
  void _showBiometricSetupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('🔒 Biometric Authentication'),
        content: Text('Biometric authentication has been enabled for faster access to Pay Mule.'),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Great!'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('🇿🇲 Pay Mule Authentication'),
        backgroundColor: Colors.blue[800],
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Progress indicator
            LinearProgressIndicator(
              value: ZambianAuthFlow.getProgress(),
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
            SizedBox(height: 24),
            
            // Current step indicator
            _buildStepIndicator(),
            SizedBox(height: 24),
            
            // Status message
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Text(
                statusMessage ?? 'Initializing...',
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 24),
            
            // Step-specific content
            Expanded(child: _buildStepContent()),
            
            // Loading indicator
            if (isLoading)
              Center(child: CircularProgressIndicator()),
          ],
        ),
      ),
    );
  }

  Widget _buildStepIndicator() {
    return Row(
      children: [
        _buildStepIcon(AuthStep.simVerification, '📱', 'SIM'),
        Expanded(child: Divider()),
        _buildStepIcon(AuthStep.smsOTP, '💬', 'SMS'),
        Expanded(child: Divider()),
        _buildStepIcon(AuthStep.securityPIN, '🔐', 'PIN'),
        Expanded(child: Divider()),
        _buildStepIcon(AuthStep.completed, '✅', 'Done'),
      ],
    );
  }

  Widget _buildStepIcon(AuthStep step, String icon, String label) {
    final isActive = currentStep == step;
    final isCompleted = currentStep.index > step.index;
    
    return Column(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isCompleted ? Colors.green : (isActive ? Colors.blue : Colors.grey[300]),
          ),
          child: Center(
            child: Text(
              isCompleted ? '✓' : icon,
              style: TextStyle(
                fontSize: 20,
                color: isCompleted || isActive ? Colors.white : Colors.grey[600],
              ),
            ),
          ),
        ),
        SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
            color: isActive ? Colors.blue : Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildStepContent() {
    switch (currentStep) {
      case AuthStep.simVerification:
        return _buildSIMVerificationContent();
      case AuthStep.smsOTP:
        return _buildSMSOTPContent();
      case AuthStep.securityPIN:
        return _buildSecurityPINContent();
      case AuthStep.completed:
        return _buildCompletedContent();
    }
  }

  Widget _buildSIMVerificationContent() {
    return Column(
      children: [
        Icon(Icons.sim_card, size: 80, color: Colors.blue),
        SizedBox(height: 16),
        Text(
          'Detecting Zambian SIM Card',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 8),
        Text(
          'Please ensure you have a Zambian SIM card (MTN, Airtel, or Zamtel) inserted.',
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.grey[600]),
        ),
        if (simInfo != null) ...[
          SizedBox(height: 24),
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                children: [
                  Text('Detected Network:', style: TextStyle(fontWeight: FontWeight.bold)),
                  SizedBox(height: 8),
                  Text(ZambiaSIM.getNetworkDisplayName(simInfo!.network)),
                  if (simInfo!.phoneNumber != null) ...[
                    SizedBox(height: 8),
                    Text('Phone: ${simInfo!.phoneNumber}'),
                  ],
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSMSOTPContent() {
    return Column(
      children: [
        Icon(Icons.sms, size: 80, color: Colors.green),
        SizedBox(height: 16),
        Text(
          'SMS Verification',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 8),
        Text(
          'Enter the 6-digit code sent to $phoneNumber',
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.grey[600]),
        ),
        SizedBox(height: 24),
        TextField(
          decoration: InputDecoration(
            labelText: 'Verification Code',
            hintText: '123456',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
          maxLength: 6,
          onChanged: (value) {
            setState(() {
              otpCode = value;
            });
            if (value.length == 6) {
              _verifyOTP();
            }
          },
        ),
        if (remainingTime > 0) ...[
          SizedBox(height: 16),
          Text(
            'Code expires in: ${(remainingTime ~/ 60).toString().padLeft(2, '0')}:${(remainingTime % 60).toString().padLeft(2, '0')}',
            style: TextStyle(color: Colors.orange),
          ),
        ],
        SizedBox(height: 16),
        ElevatedButton(
          onPressed: otpCode.length == 6 ? _verifyOTP : null,
          child: Text('Verify Code'),
        ),
      ],
    );
  }

  Widget _buildSecurityPINContent() {
    return Column(
      children: [
        Icon(Icons.security, size: 80, color: Colors.orange),
        SizedBox(height: 16),
        Text(
          'Setup Security PIN',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 8),
        Text(
          'Create a secure PIN (minimum 6 digits) to protect your account',
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.grey[600]),
        ),
        SizedBox(height: 24),
        TextField(
          decoration: InputDecoration(
            labelText: 'Security PIN',
            hintText: 'Enter 6+ digits',
            border: OutlineInputBorder(),
            suffixIcon: IconButton(
              icon: Icon(pinVisible ? Icons.visibility : Icons.visibility_off),
              onPressed: () => setState(() => pinVisible = !pinVisible),
            ),
          ),
          obscureText: !pinVisible,
          keyboardType: TextInputType.number,
          onChanged: (value) => setState(() => pin = value),
        ),
        SizedBox(height: 16),
        TextField(
          decoration: InputDecoration(
            labelText: 'Confirm PIN',
            hintText: 'Re-enter PIN',
            border: OutlineInputBorder(),
          ),
          obscureText: !pinVisible,
          keyboardType: TextInputType.number,
          onChanged: (value) => setState(() => confirmPin = value),
        ),
        SizedBox(height: 24),
        ElevatedButton(
          onPressed: pin.length >= 6 && pin == confirmPin ? _setupSecurityPIN : null,
          child: Text('Setup PIN'),
        ),
      ],
    );
  }

  Widget _buildCompletedContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.check_circle, size: 100, color: Colors.green),
        SizedBox(height: 24),
        Text(
          '🎉 Welcome to Pay Mule!',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 16),
        Text(
          ZambianAuthFlow.getWelcomeMessage(),
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 16, color: Colors.grey[600]),
        ),
        SizedBox(height: 32),
        ElevatedButton(
          onPressed: () {
            // Navigate to main app
            Navigator.pushReplacementNamed(context, '/home');
          },
          child: Text('Start Using Pay Mule'),
          style: ElevatedButton.styleFrom(
            padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          ),
        ),
      ],
    );
  }
}
