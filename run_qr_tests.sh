#!/bin/bash

# Comprehensive QR Payment Test Runner for Zambian Mobile Money
# Runs all QR-related tests including generation, scanning, and offline handling
# Optimized for Zambian market conditions and devices

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Default configuration
ENVIRONMENT="zm_prod"
DEVICES="Tecno Spark 7,Itel P40,Samsung Galaxy A12"
LIGHT_CONDITIONS="low,normal,bright"
AMOUNTS="10,25,50,100,250,500,1000,2500,5000"
OFFLINE_TRANSACTIONS=25
NETWORK_OUTAGE="48h"
OUTPUT_DIR="test_results/comprehensive"
PARALLEL_TESTS=false
VERBOSE=false

# Function to print colored output
print_header() {
    echo -e "\n${PURPLE}${'='*80}${NC}"
    echo -e "${PURPLE}$(printf '%*s' $(((80-${#1})/2)) '')$1${NC}"
    echo -e "${PURPLE}${'='*80}${NC}\n"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_test_result() {
    local test_name="$1"
    local status="$2"
    local details="$3"
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ $test_name${NC} $details"
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}❌ $test_name${NC} $details"
    elif [ "$status" = "SKIP" ]; then
        echo -e "${YELLOW}⏭️  $test_name${NC} $details"
    else
        echo -e "${BLUE}🔄 $test_name${NC} $details"
    fi
}

# Function to show usage
show_usage() {
    cat << EOF
🇿🇲 Comprehensive QR Payment Test Runner for Zambian Mobile Money

Usage: $0 [OPTIONS]

OPTIONS:
    --environment=ENV           Test environment (test, zm_prod)
                               Default: zm_prod
    
    --devices=DEVICE_LIST       Comma-separated list of devices for scanning tests
                               Default: "Tecno Spark 7,Itel P40,Samsung Galaxy A12"
    
    --light-conditions=CONDITIONS  Lighting conditions for scanning tests
                                  Default: "low,normal,bright"
    
    --amounts=AMOUNT_LIST       Amounts to test (comma-separated)
                               Default: "10,25,50,100,250,500,1000,2500,5000"
    
    --offline-transactions=NUM  Number of offline transactions to simulate
                               Default: 25
    
    --network-outage=DURATION   Network outage duration (e.g., 48h, 2d)
                               Default: 48h
    
    --output-dir=DIRECTORY      Output directory for all test results
                               Default: test_results/comprehensive
    
    --parallel                  Run tests in parallel where possible
    
    --verbose                   Enable verbose output
    
    --help                      Show this help message

TEST SUITES:
    1. QR Generation Tests      - Unit tests for QR code generation
    2. QR Scanning Tests        - Device-specific scanning tests
    3. Offline Handling Tests   - Network outage simulation
    4. Anti-Fraud Tests         - Security and fraud detection
    5. Integration Tests        - End-to-end payment flows

EXAMPLES:
    # Run all tests with default settings
    $0
    
    # Run tests for specific environment
    $0 --environment=test --verbose
    
    # Run extended offline testing
    $0 --offline-transactions=50 --network-outage=72h
    
    # Run scanning tests on specific devices
    $0 --devices="Tecno Spark 7" --light-conditions="low"

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --environment=*)
            ENVIRONMENT="${1#*=}"
            shift
            ;;
        --devices=*)
            DEVICES="${1#*=}"
            shift
            ;;
        --light-conditions=*)
            LIGHT_CONDITIONS="${1#*=}"
            shift
            ;;
        --amounts=*)
            AMOUNTS="${1#*=}"
            shift
            ;;
        --offline-transactions=*)
            OFFLINE_TRANSACTIONS="${1#*=}"
            shift
            ;;
        --network-outage=*)
            NETWORK_OUTAGE="${1#*=}"
            shift
            ;;
        --output-dir=*)
            OUTPUT_DIR="${1#*=}"
            shift
            ;;
        --parallel)
            PARALLEL_TESTS=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Initialize test results
TEST_RESULTS_FILE="$OUTPUT_DIR/test_results.json"
echo '{"test_suites": [], "summary": {}}' > "$TEST_RESULTS_FILE"

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    local missing_tools=()
    
    # Check Flutter
    if ! command -v flutter &> /dev/null; then
        missing_tools+=("flutter")
    fi
    
    # Check Python3 (for offline simulation)
    if ! command -v python3 &> /dev/null; then
        missing_tools+=("python3")
    fi
    
    # Check ADB (for device tests)
    if ! command -v adb &> /dev/null; then
        print_warning "ADB not found - device tests will be skipped"
    fi
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to run QR generation tests
run_generation_tests() {
    print_header "QR GENERATION TESTS"
    
    local start_time=$(date +%s)
    local test_output="$OUTPUT_DIR/generation_test_output.txt"
    
    print_status "Running QR generation tests..."
    
    if flutter test test/qr/generation_test.dart --dart-define=ENV="$ENVIRONMENT" > "$test_output" 2>&1; then
        print_test_result "QR Generation Tests" "PASS" "(Environment: $ENVIRONMENT)"
        
        # Extract test statistics
        local test_count=$(grep -c "✓" "$test_output" || echo "0")
        print_status "  Passed: $test_count tests"
        
        return 0
    else
        print_test_result "QR Generation Tests" "FAIL" "(Check $test_output for details)"
        
        if [ "$VERBOSE" = true ]; then
            echo "Error details:"
            tail -20 "$test_output"
        fi
        
        return 1
    fi
}

# Function to run QR scanning tests
run_scanning_tests() {
    print_header "QR SCANNING TESTS"
    
    # Check if ADB is available and devices are connected
    if ! command -v adb &> /dev/null; then
        print_test_result "QR Scanning Tests" "SKIP" "(ADB not available)"
        return 0
    fi
    
    local connected_devices=$(adb devices | grep -v "List of devices" | grep "device$" | wc -l)
    if [ "$connected_devices" -eq 0 ]; then
        print_test_result "QR Scanning Tests" "SKIP" "(No devices connected)"
        return 0
    fi
    
    print_status "Running QR scanning tests on $connected_devices device(s)..."
    
    local scanning_output="$OUTPUT_DIR/scanning_test_output.txt"
    
    if ./test_qr_scanning.sh \
        --devices="$DEVICES" \
        --light-conditions="$LIGHT_CONDITIONS" \
        --amounts="$AMOUNTS" \
        --output-dir="$OUTPUT_DIR/scanning" > "$scanning_output" 2>&1; then
        
        print_test_result "QR Scanning Tests" "PASS" "(Check $OUTPUT_DIR/scanning for detailed results)"
        
        # Extract success rate
        if [ -f "$OUTPUT_DIR/scanning/test_results.csv" ]; then
            local total_tests=$(wc -l < "$OUTPUT_DIR/scanning/test_results.csv")
            local successful_tests=$(grep -c "SUCCESS" "$OUTPUT_DIR/scanning/test_results.csv" || echo "0")
            local success_rate=$((successful_tests * 100 / total_tests))
            print_status "  Success Rate: $success_rate% ($successful_tests/$total_tests)"
        fi
        
        return 0
    else
        print_test_result "QR Scanning Tests" "FAIL" "(Check $scanning_output for details)"
        return 1
    fi
}

# Function to run offline handling tests
run_offline_tests() {
    print_header "OFFLINE HANDLING TESTS"
    
    print_status "Running offline QR simulation..."
    
    local offline_output="$OUTPUT_DIR/offline_test_output.txt"
    
    if python3 simulate_offline_qr \
        --transactions="$OFFLINE_TRANSACTIONS" \
        --network-outage="$NETWORK_OUTAGE" \
        --validate-receipts \
        --output-dir="$OUTPUT_DIR/offline" > "$offline_output" 2>&1; then
        
        print_test_result "Offline Handling Tests" "PASS" "(Simulated $OFFLINE_TRANSACTIONS transactions)"
        
        # Extract simulation results
        if [ -f "$OUTPUT_DIR/offline/simulation_results.json" ]; then
            local success_rate=$(python3 -c "
import json
with open('$OUTPUT_DIR/offline/simulation_results.json') as f:
    data = json.load(f)
    print(f\"{data['summary']['success_rate']:.1f}\")
" 2>/dev/null || echo "N/A")
            
            print_status "  Sync Success Rate: $success_rate%"
        fi
        
        return 0
    else
        print_test_result "Offline Handling Tests" "FAIL" "(Check $offline_output for details)"
        return 1
    fi
}

# Function to run anti-fraud tests
run_antifraud_tests() {
    print_header "ANTI-FRAUD TESTS"
    
    print_status "Running anti-fraud protection tests..."
    
    local antifraud_output="$OUTPUT_DIR/antifraud_test_output.txt"
    
    if flutter test test/qr/antifraud_test.dart --dart-define=ENV="$ENVIRONMENT" > "$antifraud_output" 2>&1; then
        print_test_result "Anti-Fraud Tests" "PASS" "(Security validation passed)"
        return 0
    else
        print_test_result "Anti-Fraud Tests" "FAIL" "(Check $antifraud_output for details)"
        return 1
    fi
}

# Function to run integration tests
run_integration_tests() {
    print_header "INTEGRATION TESTS"
    
    print_status "Running end-to-end integration tests..."
    
    local integration_output="$OUTPUT_DIR/integration_test_output.txt"
    
    if flutter test test/qr/integration_test.dart --dart-define=ENV="$ENVIRONMENT" > "$integration_output" 2>&1; then
        print_test_result "Integration Tests" "PASS" "(End-to-end flows validated)"
        return 0
    else
        print_test_result "Integration Tests" "FAIL" "(Check $integration_output for details)"
        return 1
    fi
}

# Function to generate comprehensive report
generate_report() {
    print_header "GENERATING COMPREHENSIVE REPORT"
    
    local report_file="$OUTPUT_DIR/comprehensive_test_report.md"
    local end_time=$(date +%s)
    local duration=$((end_time - START_TIME))
    
    cat > "$report_file" << EOF
# Comprehensive QR Payment Test Report

## Test Configuration
- **Environment**: $ENVIRONMENT
- **Test Date**: $(date)
- **Duration**: ${duration}s
- **Output Directory**: $OUTPUT_DIR

## Test Parameters
- **Devices**: $DEVICES
- **Light Conditions**: $LIGHT_CONDITIONS
- **Test Amounts**: $AMOUNTS
- **Offline Transactions**: $OFFLINE_TRANSACTIONS
- **Network Outage**: $NETWORK_OUTAGE

## Test Results Summary
EOF

    # Add individual test results
    local total_tests=0
    local passed_tests=0
    
    for test_suite in "generation" "scanning" "offline" "antifraud" "integration"; do
        if [ -f "$OUTPUT_DIR/${test_suite}_test_result" ]; then
            local result=$(cat "$OUTPUT_DIR/${test_suite}_test_result")
            echo "- **${test_suite^} Tests**: $result" >> "$report_file"
            total_tests=$((total_tests + 1))
            if [ "$result" = "PASS" ]; then
                passed_tests=$((passed_tests + 1))
            fi
        fi
    done
    
    local success_rate=$((passed_tests * 100 / total_tests))
    
    cat >> "$report_file" << EOF

## Overall Results
- **Total Test Suites**: $total_tests
- **Passed**: $passed_tests
- **Failed**: $((total_tests - passed_tests))
- **Success Rate**: $success_rate%

## Detailed Results
EOF

    # Add links to detailed results
    for result_dir in "$OUTPUT_DIR"/*/; do
        if [ -d "$result_dir" ]; then
            local dir_name=$(basename "$result_dir")
            echo "- [$dir_name Results](./$dir_name/)" >> "$report_file"
        fi
    done
    
    print_success "Comprehensive report generated: $report_file"
}

# Main execution
main() {
    local START_TIME=$(date +%s)
    
    print_header "🇿🇲 ZAMBIAN QR PAYMENT COMPREHENSIVE TEST SUITE"
    
    print_status "Test Configuration:"
    echo "  Environment: $ENVIRONMENT"
    echo "  Devices: $DEVICES"
    echo "  Light Conditions: $LIGHT_CONDITIONS"
    echo "  Amounts: $AMOUNTS"
    echo "  Offline Transactions: $OFFLINE_TRANSACTIONS"
    echo "  Network Outage: $NETWORK_OUTAGE"
    echo "  Output Directory: $OUTPUT_DIR"
    echo "  Parallel Tests: $PARALLEL_TESTS"
    echo "  Verbose: $VERBOSE"
    
    # Check prerequisites
    check_prerequisites
    
    # Make scripts executable
    chmod +x test_qr_scanning.sh
    chmod +x simulate_offline_qr
    
    # Run test suites
    local test_results=()
    
    # QR Generation Tests
    if run_generation_tests; then
        echo "PASS" > "$OUTPUT_DIR/generation_test_result"
        test_results+=("generation:PASS")
    else
        echo "FAIL" > "$OUTPUT_DIR/generation_test_result"
        test_results+=("generation:FAIL")
    fi
    
    # QR Scanning Tests
    if run_scanning_tests; then
        echo "PASS" > "$OUTPUT_DIR/scanning_test_result"
        test_results+=("scanning:PASS")
    else
        echo "FAIL" > "$OUTPUT_DIR/scanning_test_result"
        test_results+=("scanning:FAIL")
    fi
    
    # Offline Handling Tests
    if run_offline_tests; then
        echo "PASS" > "$OUTPUT_DIR/offline_test_result"
        test_results+=("offline:PASS")
    else
        echo "FAIL" > "$OUTPUT_DIR/offline_test_result"
        test_results+=("offline:FAIL")
    fi
    
    # Anti-Fraud Tests (if test file exists)
    if [ -f "test/qr/antifraud_test.dart" ]; then
        if run_antifraud_tests; then
            echo "PASS" > "$OUTPUT_DIR/antifraud_test_result"
            test_results+=("antifraud:PASS")
        else
            echo "FAIL" > "$OUTPUT_DIR/antifraud_test_result"
            test_results+=("antifraud:FAIL")
        fi
    fi
    
    # Integration Tests (if test file exists)
    if [ -f "test/qr/integration_test.dart" ]; then
        if run_integration_tests; then
            echo "PASS" > "$OUTPUT_DIR/integration_test_result"
            test_results+=("integration:PASS")
        else
            echo "FAIL" > "$OUTPUT_DIR/integration_test_result"
            test_results+=("integration:FAIL")
        fi
    fi
    
    # Generate comprehensive report
    generate_report
    
    # Display final summary
    print_header "TEST EXECUTION SUMMARY"
    
    local total_suites=${#test_results[@]}
    local passed_suites=0
    
    for result in "${test_results[@]}"; do
        local suite_name="${result%:*}"
        local suite_result="${result#*:}"
        
        if [ "$suite_result" = "PASS" ]; then
            print_test_result "${suite_name^} Tests" "PASS"
            passed_suites=$((passed_suites + 1))
        else
            print_test_result "${suite_name^} Tests" "FAIL"
        fi
    done
    
    local overall_success_rate=$((passed_suites * 100 / total_suites))
    
    echo ""
    print_status "📊 Overall Results:"
    echo "  Total Test Suites: $total_suites"
    echo "  Passed: $passed_suites"
    echo "  Failed: $((total_suites - passed_suites))"
    echo "  Success Rate: $overall_success_rate%"
    echo ""
    print_status "📁 All results saved to: $OUTPUT_DIR"
    
    if [ $overall_success_rate -eq 100 ]; then
        print_success "🎉 All tests passed! QR payment system is ready for production."
    elif [ $overall_success_rate -ge 80 ]; then
        print_warning "⚠️ Most tests passed. Review failed tests before production deployment."
    else
        print_error "❌ Multiple test failures detected. System requires fixes before deployment."
        exit 1
    fi
}

# Run main function
main "$@"
