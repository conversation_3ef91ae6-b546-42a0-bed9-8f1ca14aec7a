#!/bin/bash

# EXECUTE TEST TRANSACTION SCRIPT
# Sends ADB broadcast commands for testing mobile money transactions

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

echo -e "${CYAN}${BOLD}🧪 EXECUTE TEST TRANSACTION - PAY MULE ZAMBIA 🧪${NC}"
echo -e "${CYAN}======================================================${NC}"
echo ""

# Default parameters
RECEIVER="260971111111"
AMOUNT="1.0"
TYPE="mtn_to_airtel"
REFERENCE=""
PACKAGE_NAME="com.zm.paymule.real"
BROADCAST_ACTION="com.zm.paymule.TEST_TRANSACTION"

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_command() {
    echo -e "${CYAN}[COMMAND]${NC} $1"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --receiver=*)
            RECEIVER="${1#*=}"
            shift
            ;;
        --amount=*)
            AMOUNT="${1#*=}"
            shift
            ;;
        --type=*)
            TYPE="${1#*=}"
            shift
            ;;
        --reference=*)
            REFERENCE="${1#*=}"
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --receiver=PHONE     Receiver phone number (default: 260971111111)"
            echo "  --amount=AMOUNT      Transaction amount in ZMW (default: 1.0)"
            echo "  --type=TYPE          Transaction type (default: mtn_to_airtel)"
            echo "  --reference=REF      Optional transaction reference"
            echo "  --help, -h           Show this help message"
            echo ""
            echo "Transaction Types:"
            echo "  mtn_to_airtel       MTN Mobile Money → Airtel Money"
            echo "  airtel_to_mtn       Airtel Money → MTN Mobile Money"
            echo "  mtn_to_zamtel       MTN Mobile Money → Zamtel Kwacha"
            echo "  zamtel_to_mtn       Zamtel Kwacha → MTN Mobile Money"
            echo "  airtel_to_zamtel    Airtel Money → Zamtel Kwacha"
            echo "  zamtel_to_airtel    Zamtel Kwacha → Airtel Money"
            echo "  same_network        Same network transfer"
            echo ""
            echo "Examples:"
            echo "  $0 --receiver=260961234567 --amount=5.0 --type=mtn_to_airtel"
            echo "  $0 --receiver=260971111111 --amount=1.0 --type=airtel_to_mtn --reference=TEST001"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Validate parameters
validate_parameters() {
    print_info "Validating parameters..."
    
    # Validate receiver phone number
    if [[ ! "$RECEIVER" =~ ^260[0-9]{9}$ ]]; then
        print_error "Invalid receiver phone number: $RECEIVER"
        print_info "Expected format: 260XXXXXXXXX (Zambian number)"
        exit 1
    fi
    
    # Validate amount
    if ! [[ "$AMOUNT" =~ ^[0-9]+(\.[0-9]+)?$ ]]; then
        print_error "Invalid amount: $AMOUNT"
        print_info "Expected format: numeric value (e.g., 1.0, 5.50)"
        exit 1
    fi
    
    # Check minimum amount
    if (( $(echo "$AMOUNT < 1.0" | bc -l) )); then
        print_error "Amount too small: $AMOUNT"
        print_info "Minimum amount is K1.00"
        exit 1
    fi
    
    # Check maximum amount
    if (( $(echo "$AMOUNT > 50000.0" | bc -l) )); then
        print_error "Amount too large: $AMOUNT"
        print_info "Maximum amount is K50,000.00"
        exit 1
    fi
    
    # Validate transaction type
    case "$TYPE" in
        mtn_to_airtel|airtel_to_mtn|mtn_to_zamtel|zamtel_to_mtn|airtel_to_zamtel|zamtel_to_airtel|same_network)
            # Valid types
            ;;
        *)
            print_error "Invalid transaction type: $TYPE"
            print_info "Valid types: mtn_to_airtel, airtel_to_mtn, mtn_to_zamtel, zamtel_to_mtn, airtel_to_zamtel, zamtel_to_airtel, same_network"
            exit 1
            ;;
    esac
    
    print_success "Parameters validated successfully"
}

# Check ADB availability
check_adb() {
    print_info "Checking ADB availability..."
    
    if ! command -v adb &> /dev/null; then
        print_error "ADB not found in PATH"
        print_info "Please install Android SDK Platform Tools"
        exit 1
    fi
    
    print_success "ADB found: $(adb version | head -1)"
}

# Check device connection
check_device() {
    print_info "Checking device connection..."
    
    local devices=$(adb devices | grep -v "List of devices" | grep -v "^$" | wc -l)
    
    if [ "$devices" -eq 0 ]; then
        print_error "No devices connected"
        print_info "Please connect your Android device and enable USB debugging"
        exit 1
    fi
    
    print_success "Device(s) connected: $devices device(s)"
    adb devices
}

# Check if Pay Mule app is installed
check_app() {
    print_info "Checking if Pay Mule app is installed..."
    
    local app_installed=$(adb shell pm list packages | grep "$PACKAGE_NAME" | wc -l)
    
    if [ "$app_installed" -eq 0 ]; then
        print_error "Pay Mule app not installed: $PACKAGE_NAME"
        print_info "Please install the Pay Mule APK first"
        exit 1
    fi
    
    print_success "Pay Mule app is installed: $PACKAGE_NAME"
}

# Get transaction type display name
get_transaction_display_name() {
    case "$TYPE" in
        mtn_to_airtel)
            echo "MTN Mobile Money → Airtel Money"
            ;;
        airtel_to_mtn)
            echo "Airtel Money → MTN Mobile Money"
            ;;
        mtn_to_zamtel)
            echo "MTN Mobile Money → Zamtel Kwacha"
            ;;
        zamtel_to_mtn)
            echo "Zamtel Kwacha → MTN Mobile Money"
            ;;
        airtel_to_zamtel)
            echo "Airtel Money → Zamtel Kwacha"
            ;;
        zamtel_to_airtel)
            echo "Zamtel Kwacha → Airtel Money"
            ;;
        same_network)
            echo "Same Network Transfer"
            ;;
        *)
            echo "Unknown Transaction Type"
            ;;
    esac
}

# Execute test transaction
execute_test_transaction() {
    print_info "Executing test transaction..."
    
    local display_name=$(get_transaction_display_name)
    
    echo ""
    print_info "Transaction Details:"
    echo "  📱 Type: $display_name"
    echo "  📞 Receiver: +$RECEIVER"
    echo "  💰 Amount: K$AMOUNT"
    if [ -n "$REFERENCE" ]; then
        echo "  📋 Reference: $REFERENCE"
    fi
    echo ""
    
    # Build ADB command
    local adb_command="adb shell am broadcast -a $BROADCAST_ACTION"
    adb_command="$adb_command --es \"receiver\" \"$RECEIVER\""
    adb_command="$adb_command --es \"amount\" \"$AMOUNT\""
    adb_command="$adb_command --es \"type\" \"$TYPE\""
    
    if [ -n "$REFERENCE" ]; then
        adb_command="$adb_command --es \"reference\" \"$REFERENCE\""
    fi
    
    print_command "Executing ADB broadcast:"
    echo "  $adb_command"
    echo ""
    
    # Execute the command
    if [ -n "$REFERENCE" ]; then
        adb shell am broadcast -a "$BROADCAST_ACTION" \
            --es "receiver" "$RECEIVER" \
            --es "amount" "$AMOUNT" \
            --es "type" "$TYPE" \
            --es "reference" "$REFERENCE"
    else
        adb shell am broadcast -a "$BROADCAST_ACTION" \
            --es "receiver" "$RECEIVER" \
            --es "amount" "$AMOUNT" \
            --es "type" "$TYPE"
    fi
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        print_success "Test transaction broadcast sent successfully!"
        echo ""
        print_info "The Pay Mule app should now process this transaction."
        print_info "Check the app logs or UI for transaction status."
    else
        print_error "Failed to send test transaction broadcast (exit code: $exit_code)"
        exit 1
    fi
}

# Generate transaction summary
generate_summary() {
    local timestamp=$(date)
    local display_name=$(get_transaction_display_name)
    
    echo ""
    print_info "Transaction Summary:"
    echo "  🕒 Timestamp: $timestamp"
    echo "  📱 Transaction Type: $display_name"
    echo "  📞 Receiver: +$RECEIVER"
    echo "  💰 Amount: K$AMOUNT ZMW"
    if [ -n "$REFERENCE" ]; then
        echo "  📋 Reference: $REFERENCE"
    fi
    echo "  📦 Package: $PACKAGE_NAME"
    echo "  📡 Broadcast Action: $BROADCAST_ACTION"
    echo ""
    
    # Calculate estimated fee (simplified)
    local base_fee=2.0
    local percentage_fee=$(echo "$AMOUNT * 0.01" | bc -l)
    local cross_network_multiplier=1.5
    
    if [ "$TYPE" = "same_network" ]; then
        cross_network_multiplier=1.0
    fi
    
    local estimated_fee=$(echo "($base_fee + $percentage_fee) * $cross_network_multiplier" | bc -l)
    local total_amount=$(echo "$AMOUNT + $estimated_fee" | bc -l)
    
    print_info "Estimated Costs:"
    echo "  💰 Transaction Amount: K$(printf "%.2f" $AMOUNT)"
    echo "  💳 Estimated Fee: K$(printf "%.2f" $estimated_fee)"
    echo "  💵 Total Amount: K$(printf "%.2f" $total_amount)"
}

# Main execution function
main() {
    print_info "Starting test transaction execution..."
    echo ""
    
    validate_parameters
    echo ""
    
    check_adb
    echo ""
    
    check_device
    echo ""
    
    check_app
    echo ""
    
    execute_test_transaction
    
    generate_summary
    
    echo ""
    print_success "🎉 Test transaction execution completed! 🎉"
    echo ""
    print_info "Next steps:"
    echo "  1. Check the Pay Mule app for transaction processing"
    echo "  2. Monitor app logs for detailed transaction flow"
    echo "  3. Verify transaction completion in the app UI"
    echo ""
    print_info "To execute another transaction, run this script again with different parameters."
}

# Run main function
main
