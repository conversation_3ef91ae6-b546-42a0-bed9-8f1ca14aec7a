/// Zambian QR Payment System for Pay Mule
/// Optimized for Zambian mobile money networks (MTN, Airtel, Zamtel)
/// Supports offline payments and rural connectivity scenarios

import 'dart:convert';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

import '../core/constants/app_constants.dart';
import '../core/config/app_config.dart';
import '../core/security/encryption_service.dart';
import '../features/mobile_money/data/services/mobile_money_service.dart';
import '../features/offline_sync/data/offline_sync_manager.dart';
import '../features/auth/data/services/pin_service.dart';
import '../data/database/database_helper.dart';
import 'zambia_qr_format.dart';
import 'security/qr_security_service.dart';
import 'utils/connectivity_helper.dart';
import 'utils/offline_queue.dart';
import 'utils/receipt_generator.dart';

/// Main QR Payment System for Zambian Market
class PayMuleQR {
  static final PayMuleQR _instance = PayMuleQR._internal();
  factory PayMuleQR() => _instance;
  PayMuleQR._internal();

  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();
  final EncryptionService _encryption = EncryptionService();
  final MobileMoneyService _mobileMoneyService = MobileMoneyService();
  final OfflineSyncManager _syncManager = OfflineSyncManager();
  final PINService _pinService = PINService();
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final QRSecurityService _securityService = QRSecurityService();

  bool _isInitialized = false;

  /// Initialize QR payment system
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _encryption.initialize();
      await _mobileMoneyService.initialize(
        mtnCredentials: AppConfig.mtnCredentials,
        airtelCredentials: AppConfig.airtelCredentials,
        zamtelCredentials: AppConfig.zamtelCredentials,
      );
      await _syncManager.initialize();
      await _securityService.initialize();
      await Connectivity.initialize();
      await OfflineQueue.initialize();
      await ReceiptGenerator.initialize();

      _isInitialized = true;
      _logger.i('🇿🇲 PayMule QR system initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize QR system: $e');
      rethrow;
    }
  }

  /// Generate dynamic QR code for payments
  /// Optimized for Zambian mobile money networks
  static Future<Uint8List> generateQR({
    required String merchantId,
    required double amount,
    String? description,
    String? currency,
    Duration? expiryDuration,
  }) async {
    try {
      final instance = PayMuleQR();
      if (!instance._isInitialized) {
        await instance.initialize();
      }

      // Detect mobile money provider for optimization
      final provider = MobileMoneyService().detectProvider(merchantId);
      
      // Create Zambian QR format payload
      final payload = await ZambiaQRFormat.encode(
        merchantId: merchantId,
        amount: amount,
        currency: currency ?? "ZMW",
        provider: provider,
        description: description,
        expiryDuration: expiryDuration,
      );

      // Generate QR code with Zambian optimizations
      final qrValidationResult = QrValidator.validate(
        data: payload,
        version: QrVersions.auto,
        errorCorrectionLevel: QrErrorCorrectLevel.M, // Medium for 2G networks
      );

      if (qrValidationResult.status == QrValidationStatus.valid) {
        final qrCode = qrValidationResult.qrCode!;
        
        // Create QR painter with Zambian styling
        final painter = QrPainter(
          data: payload,
          version: qrCode.version,
          errorCorrectionLevel: qrCode.errorCorrectLevel,
          color: const Color(0xFF2E7D32), // Zambian green
          emptyColor: Colors.white,
          gapless: true,
        );

        // Convert to image
        final picData = await painter.toImageData(
          AppConstants.qrCodeSize.toDouble(),
          format: ui.ImageByteFormat.png,
        );
        
        return picData!.buffer.asUint8List();
      } else {
        throw Exception('Invalid QR data: ${qrValidationResult.error}');
      }
    } catch (e) {
      Logger().e('QR generation failed: $e');
      rethrow;
    }
  }

  /// Scan and process QR payment
  /// Handles offline scenarios and network optimization
  static Future<QRPaymentResult> scanAndPay({
    required String qrData,
    required String payerUserId,
    String? customPin,
  }) async {
    try {
      final instance = PayMuleQR();
      if (!instance._isInitialized) {
        await instance.initialize();
      }

      // Decode QR data
      final decodedData = await ZambiaQRFormat.decode(qrData);
      
      // Validate transaction
      final validation = await instance._validateTransaction(
        decodedData: decodedData,
        payerUserId: payerUserId,
      );
      
      if (!validation.isValid) {
        return QRPaymentResult(
          success: false,
          error: validation.error,
          message: validation.message,
        );
      }

      // Get transaction PIN
      final pin = customPin ?? await instance._getTransactionPin(payerUserId);
      if (pin == null) {
        return QRPaymentResult(
          success: false,
          error: 'PIN_REQUIRED',
          message: 'Transaction PIN is required for payment',
        );
      }

      // Process payment
      final paymentResult = await instance._processPayment(
        decodedData: decodedData,
        payerUserId: payerUserId,
        pin: pin,
      );

      return paymentResult;
    } catch (e) {
      Logger().e('QR scan and pay failed: $e');
      return QRPaymentResult(
        success: false,
        error: 'SCAN_FAILED',
        message: 'Failed to process QR payment: ${e.toString()}',
      );
    }
  }

  /// Validate transaction before processing
  Future<TransactionValidation> _validateTransaction({
    required ZambiaQRData decodedData,
    required String payerUserId,
  }) async {
    try {
      // Check if QR code has expired
      if (decodedData.expiryTime != null && 
          DateTime.now().isAfter(decodedData.expiryTime!)) {
        return TransactionValidation(
          isValid: false,
          error: 'QR_EXPIRED',
          message: 'QR code has expired. Please request a new one.',
        );
      }

      // Validate amount using Zambian QR format validation
      if (!ZambiaQRFormat.validateAmount(decodedData.amount)) {
        final detailedValidation = ZambiaQRFormat.validateAmountDetailed(decodedData.amount);
        return TransactionValidation(
          isValid: false,
          error: detailedValidation.error ?? 'INVALID_AMOUNT',
          message: detailedValidation.message,
        );
      }

      // Additional provider-specific validation
      if (!ZambiaQRFormat.validateAmountForProvider(decodedData.amount, decodedData.provider)) {
        return TransactionValidation(
          isValid: false,
          error: 'PROVIDER_LIMIT_EXCEEDED',
          message: 'Amount exceeds ${decodedData.provider} transaction limits.',
        );
      }

      // Check if user has sufficient balance (if online)
      if (await _syncManager.isConnected()) {
        final balance = await _mobileMoneyService.getBalance(payerUserId);
        if (balance != null && balance < decodedData.amount) {
          return TransactionValidation(
            isValid: false,
            error: 'INSUFFICIENT_BALANCE',
            message: 'Insufficient balance for this transaction.',
          );
        }
      }

      // Validate merchant exists
      final merchant = await _getMerchantInfo(decodedData.merchantId);
      if (merchant == null) {
        return TransactionValidation(
          isValid: false,
          error: 'MERCHANT_NOT_FOUND',
          message: 'Merchant not found or inactive.',
        );
      }

      return TransactionValidation(
        isValid: true,
        message: 'Transaction validation successful',
      );
    } catch (e) {
      _logger.e('Transaction validation failed: $e');
      return TransactionValidation(
        isValid: false,
        error: 'VALIDATION_ERROR',
        message: 'Failed to validate transaction',
      );
    }
  }

  /// Get transaction PIN from user
  Future<String?> _getTransactionPin(String userId) async {
    try {
      // In a real implementation, this would prompt the user for PIN
      // For now, we'll assume PIN verification is handled elsewhere
      return null; // PIN should be provided by the UI layer
    } catch (e) {
      _logger.e('Failed to get transaction PIN: $e');
      return null;
    }
  }

  /// Process the actual payment
  Future<QRPaymentResult> _processPayment({
    required ZambiaQRData decodedData,
    required String payerUserId,
    required String pin,
  }) async {
    try {
      // Verify PIN
      final pinVerification = await _pinService.verifyPIN(
        userId: payerUserId,
        pin: pin,
      );

      if (!pinVerification.success) {
        return QRPaymentResult(
          success: false,
          error: 'INVALID_PIN',
          message: 'Invalid transaction PIN',
        );
      }

      // Create transaction record
      final transactionId = _uuid.v4();
      final transaction = {
        'id': transactionId,
        'payer_user_id': payerUserId,
        'merchant_id': decodedData.merchantId,
        'amount': decodedData.amount,
        'currency': decodedData.currency,
        'description': decodedData.description ?? 'QR Payment',
        'provider': decodedData.provider,
        'qr_data': jsonEncode(decodedData.toJson()),
        'status': 'PENDING',
        'created_at': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('qr_transactions', transaction);

      // Check connectivity and process accordingly
      final isOnline = await _syncManager.isConnected();
      
      if (isOnline) {
        // Process online payment
        return await _processOnlinePayment(transactionId, decodedData, payerUserId);
      } else {
        // Queue for offline processing
        return await _processOfflinePayment(transactionId, decodedData);
      }
    } catch (e) {
      _logger.e('Payment processing failed: $e');
      return QRPaymentResult(
        success: false,
        error: 'PAYMENT_FAILED',
        message: 'Payment processing failed: ${e.toString()}',
      );
    }
  }

  /// Process online payment
  Future<QRPaymentResult> _processOnlinePayment(
    String transactionId,
    ZambiaQRData decodedData,
    String payerUserId,
  ) async {
    try {
      // Execute mobile money transfer
      final transferResult = await _mobileMoneyService.sendMoney(
        senderPhone: payerUserId, // Assuming userId is phone number
        receiverPhone: decodedData.merchantId,
        amount: decodedData.amount,
        externalId: transactionId,
        message: decodedData.description ?? 'QR Payment',
      );

      // Update transaction status
      await _dbHelper.update(
        'qr_transactions',
        {'status': transferResult.success ? 'COMPLETED' : 'FAILED'},
        where: 'id = ?',
        whereArgs: [transactionId],
      );

      return QRPaymentResult(
        success: transferResult.success,
        transactionId: transactionId,
        amount: decodedData.amount,
        merchantId: decodedData.merchantId,
        message: transferResult.success 
            ? 'Payment completed successfully'
            : transferResult.message ?? 'Payment failed',
        error: transferResult.success ? null : 'TRANSFER_FAILED',
      );
    } catch (e) {
      _logger.e('Online payment failed: $e');
      return QRPaymentResult(
        success: false,
        error: 'ONLINE_PAYMENT_FAILED',
        message: 'Online payment failed: ${e.toString()}',
      );
    }
  }

  /// Process offline payment (queue for later sync)
  Future<QRPaymentResult> _processOfflinePayment(
    String transactionId,
    ZambiaQRData decodedData,
  ) async {
    try {
      // Queue transaction for offline sync
      await _syncManager.queueTransaction(
        userId: decodedData.merchantId,
        transactionData: {
          'transaction_id': transactionId,
          'type': 'QR_PAYMENT',
          'amount': decodedData.amount,
          'merchant_id': decodedData.merchantId,
          'provider': decodedData.provider,
        },
        transactionType: 'QR_PAYMENT',
        priority: 1,
      );

      // Update transaction status
      await _dbHelper.update(
        'qr_transactions',
        {'status': 'QUEUED'},
        where: 'id = ?',
        whereArgs: [transactionId],
      );

      return QRPaymentResult(
        success: true,
        transactionId: transactionId,
        amount: decodedData.amount,
        merchantId: decodedData.merchantId,
        message: 'Payment queued for processing when online',
        isOffline: true,
      );
    } catch (e) {
      _logger.e('Offline payment queueing failed: $e');
      return QRPaymentResult(
        success: false,
        error: 'OFFLINE_QUEUE_FAILED',
        message: 'Failed to queue offline payment',
      );
    }
  }

  /// Get merchant information
  Future<Map<String, dynamic>?> _getMerchantInfo(String merchantId) async {
    try {
      final merchants = await _dbHelper.query(
        'merchant_profiles',
        where: 'id = ? OR id LIKE ?',
        whereArgs: [merchantId, '$merchantId%'],
        limit: 1,
      );

      return merchants.isNotEmpty ? merchants.first : null;
    } catch (e) {
      _logger.e('Failed to get merchant info: $e');
      return null;
    }
  }

  /// Handle offline result for QR payments
  /// Manages offline queue and receipt generation
  static void _handleOfflineResult(bool success, ZambiaQRData data) async {
    try {
      _logger.i('🔄 Handling offline result: ${success ? 'SUCCESS' : 'FAILED'}');

      if (!Connectivity.isOnline) {
        // Add to offline queue
        final status = success ? Status.PENDING : Status.FAILED;
        final queueId = await OfflineQueue.add(
          transaction: data,
          status: status,
          priority: Priority.NORMAL,
          metadata: {
            'offline_processed_at': DateTime.now().millisecondsSinceEpoch,
            'network_type': Connectivity.networkType,
            'network_quality': Connectivity.networkQuality,
          },
        );

        _logger.i('📦 Transaction added to offline queue: $queueId');

        // Generate and store visual receipt
        final receiptId = await ReceiptGenerator.saveOfflineReceipt(
          data,
          transactionId: queueId,
          isOffline: true,
        );

        _logger.i('🧾 Offline receipt generated: $receiptId');

        // Notify user about offline processing
        _notifyOfflineProcessing(data, receiptId, success);
      } else {
        // Online processing - still generate receipt for record keeping
        final receiptId = await ReceiptGenerator.saveOfflineReceipt(
          data,
          isOffline: false,
        );

        _logger.i('🧾 Online receipt generated: $receiptId');
      }
    } catch (e) {
      Logger().e('Failed to handle offline result: $e');
    }
  }

  /// Notify user about offline processing
  static void _notifyOfflineProcessing(
    ZambiaQRData data,
    String receiptId,
    bool success,
  ) {
    if (success) {
      _logger.i('✅ Offline payment processed successfully');
      _logger.i('   Amount: K${data.amount} ${data.currency}');
      _logger.i('   Merchant: ${data.merchantId.substring(0, 8)}...');
      _logger.i('   Receipt: $receiptId');
      _logger.i('   Will sync when connection is restored');
    } else {
      _logger.w('❌ Offline payment processing failed');
      _logger.w('   Amount: K${data.amount} ${data.currency}');
      _logger.w('   Merchant: ${data.merchantId.substring(0, 8)}...');
      _logger.w('   Receipt: $receiptId');
    }
  }

  /// Process QR payment with offline handling
  static Future<QRPaymentResult> processQRPaymentWithOfflineHandling({
    required String qrData,
    required String payerUserId,
    String? customPin,
  }) async {
    try {
      // First attempt normal processing
      final result = await scanAndPay(
        qrData: qrData,
        payerUserId: payerUserId,
        customPin: customPin,
      );

      // If successful or if we're online, return the result
      if (result.success || Connectivity.isOnline) {
        return result;
      }

      // If failed and we're offline, handle offline processing
      final decodedData = await ZambiaQRFormat.decode(qrData);
      _handleOfflineResult(false, decodedData);

      return QRPaymentResult(
        success: false,
        error: 'OFFLINE_PROCESSING',
        message: 'Payment queued for offline processing',
        isOffline: true,
      );
    } catch (e) {
      Logger().e('QR payment with offline handling failed: $e');
      return QRPaymentResult(
        success: false,
        error: 'PROCESSING_ERROR',
        message: 'Failed to process QR payment: ${e.toString()}',
      );
    }
  }

  /// Get offline queue statistics
  static Future<QueueStats> getOfflineQueueStats() async {
    return await OfflineQueue.getStats();
  }

  /// Process offline queue manually
  static Future<QueueSyncResult> syncOfflineTransactions() async {
    return await OfflineQueue.processQueue();
  }

  /// Get connectivity status
  static Map<String, dynamic> getConnectivityStatus() {
    return Connectivity.getConnectionStatus();
  }
}
