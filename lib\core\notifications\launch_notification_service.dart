// ZAMBIAN LAUNCH NOTIFICATION SERVICE
// Sends SMS launch alerts to Zambian mobile networks

import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;

enum ZambianNetwork {
  mtn,
  airtel,
  zamtel,
  auto // Auto-detect from phone number
}

class LaunchNotificationRequest {
  final String message;
  final List<String> recipients;
  final ZambianNetwork network;
  final String? campaignId;
  final Map<String, dynamic>? metadata;

  LaunchNotificationRequest({
    required this.message,
    required this.recipients,
    required this.network,
    this.campaignId,
    this.metadata,
  });

  Map<String, dynamic> toJson() => {
    'message': message,
    'recipients': recipients,
    'network': network.toString(),
    'campaignId': campaignId,
    'metadata': metadata,
  };
}

class LaunchNotificationResult {
  final bool success;
  final String? message;
  final int totalRecipients;
  final int successfulSends;
  final int failedSends;
  final List<String> successfulNumbers;
  final List<String> failedNumbers;
  final Map<String, dynamic>? details;

  LaunchNotificationResult({
    required this.success,
    this.message,
    required this.totalRecipients,
    required this.successfulSends,
    required this.failedSends,
    required this.successfulNumbers,
    required this.failedNumbers,
    this.details,
  });

  Map<String, dynamic> toJson() => {
    'success': success,
    'message': message,
    'totalRecipients': totalRecipients,
    'successfulSends': successfulSends,
    'failedSends': failedSends,
    'successfulNumbers': successfulNumbers,
    'failedNumbers': failedNumbers,
    'details': details,
  };
}

class LaunchNotificationService {
  static const Map<ZambianNetwork, Map<String, dynamic>> _networkConfigs = {
    ZambianNetwork.mtn: {
      'name': 'MTN Zambia',
      'smsGateway': 'https://api.mtn.com/v1/sms',
      'prefixes': ['96'],
      'maxLength': 160,
      'encoding': 'UTF-8',
      'priority': 'high',
    },
    ZambianNetwork.airtel: {
      'name': 'Airtel Zambia',
      'smsGateway': 'https://api.airtel.africa/v1/sms',
      'prefixes': ['97'],
      'maxLength': 160,
      'encoding': 'UTF-8',
      'priority': 'high',
    },
    ZambianNetwork.zamtel: {
      'name': 'Zamtel',
      'smsGateway': 'https://api.zamtel.zm/v1/sms',
      'prefixes': ['95'],
      'maxLength': 160,
      'encoding': 'UTF-8',
      'priority': 'high',
    },
  };

  /// Send launch notification to Zambian recipients
  static Future<LaunchNotificationResult> sendLaunchNotification(
    LaunchNotificationRequest request,
  ) async {
    try {
      print('📱 Sending Zambian launch notification...');
      print('📋 Message: ${request.message}');
      print('👥 Recipients: ${request.recipients.length}');
      print('📡 Network: ${request.network}');

      // Validate request
      final validation = _validateRequest(request);
      if (!validation.isValid) {
        return LaunchNotificationResult(
          success: false,
          message: validation.message,
          totalRecipients: request.recipients.length,
          successfulSends: 0,
          failedSends: request.recipients.length,
          successfulNumbers: [],
          failedNumbers: request.recipients,
        );
      }

      // Process recipients by network
      final networkGroups = _groupRecipientsByNetwork(request.recipients, request.network);
      
      final successfulNumbers = <String>[];
      final failedNumbers = <String>[];
      final sendResults = <String, dynamic>{};

      // Send to each network group
      for (final entry in networkGroups.entries) {
        final network = entry.key;
        final numbers = entry.value;
        
        if (numbers.isEmpty) continue;

        print('📱 Sending to ${_networkConfigs[network]!['name']}: ${numbers.length} recipients');

        final networkResult = await _sendToNetwork(
          network,
          request.message,
          numbers,
          request.campaignId,
        );

        successfulNumbers.addAll(networkResult.successfulNumbers);
        failedNumbers.addAll(networkResult.failedNumbers);
        sendResults[network.toString()] = networkResult.toJson();
      }

      final totalRecipients = request.recipients.length;
      final successfulSends = successfulNumbers.length;
      final failedSends = failedNumbers.length;

      print('✅ Launch notification complete:');
      print('   Total: $totalRecipients');
      print('   Successful: $successfulSends');
      print('   Failed: $failedSends');

      return LaunchNotificationResult(
        success: successfulSends > 0,
        message: successfulSends == totalRecipients 
            ? 'All launch notifications sent successfully'
            : 'Launch notifications sent with some failures',
        totalRecipients: totalRecipients,
        successfulSends: successfulSends,
        failedSends: failedSends,
        successfulNumbers: successfulNumbers,
        failedNumbers: failedNumbers,
        details: {
          'networkResults': sendResults,
          'campaignId': request.campaignId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

    } catch (e) {
      print('❌ Launch notification failed: $e');
      return LaunchNotificationResult(
        success: false,
        message: 'Launch notification failed: ${e.toString()}',
        totalRecipients: request.recipients.length,
        successfulSends: 0,
        failedSends: request.recipients.length,
        successfulNumbers: [],
        failedNumbers: request.recipients,
      );
    }
  }

  /// Validate launch notification request
  static RequestValidation _validateRequest(LaunchNotificationRequest request) {
    // Validate message
    if (request.message.isEmpty) {
      return RequestValidation(false, 'Message cannot be empty');
    }

    if (request.message.length > 160) {
      return RequestValidation(false, 'Message too long (max 160 characters)');
    }

    // Validate recipients
    if (request.recipients.isEmpty) {
      return RequestValidation(false, 'No recipients specified');
    }

    if (request.recipients.length > 1000) {
      return RequestValidation(false, 'Too many recipients (max 1000)');
    }

    // Validate phone numbers
    for (final number in request.recipients) {
      if (!_isValidZambianNumber(number)) {
        return RequestValidation(false, 'Invalid Zambian phone number: $number');
      }
    }

    return RequestValidation(true, null);
  }

  /// Group recipients by network
  static Map<ZambianNetwork, List<String>> _groupRecipientsByNetwork(
    List<String> recipients,
    ZambianNetwork requestedNetwork,
  ) {
    final groups = <ZambianNetwork, List<String>>{
      ZambianNetwork.mtn: [],
      ZambianNetwork.airtel: [],
      ZambianNetwork.zamtel: [],
    };

    for (final number in recipients) {
      final detectedNetwork = requestedNetwork == ZambianNetwork.auto
          ? _detectNetworkFromNumber(number)
          : requestedNetwork;

      if (groups.containsKey(detectedNetwork)) {
        groups[detectedNetwork]!.add(number);
      }
    }

    return groups;
  }

  /// Detect network from phone number
  static ZambianNetwork _detectNetworkFromNumber(String phoneNumber) {
    final cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    String prefix;
    if (cleaned.startsWith('260') && cleaned.length == 12) {
      prefix = cleaned.substring(3, 5);
    } else if (cleaned.startsWith('0') && cleaned.length == 10) {
      prefix = cleaned.substring(1, 3);
    } else if (cleaned.length == 9) {
      prefix = cleaned.substring(0, 2);
    } else {
      return ZambianNetwork.mtn; // Default fallback
    }

    switch (prefix) {
      case '96':
        return ZambianNetwork.mtn;
      case '97':
        return ZambianNetwork.airtel;
      case '95':
        return ZambianNetwork.zamtel;
      default:
        return ZambianNetwork.mtn; // Default fallback
    }
  }

  /// Send notification to specific network
  static Future<LaunchNotificationResult> _sendToNetwork(
    ZambianNetwork network,
    String message,
    List<String> recipients,
    String? campaignId,
  ) async {
    try {
      final config = _networkConfigs[network]!;
      final successfulNumbers = <String>[];
      final failedNumbers = <String>[];

      // Simulate network-specific sending
      switch (network) {
        case ZambianNetwork.mtn:
          final result = await _sendMTNSMS(message, recipients, campaignId);
          successfulNumbers.addAll(result.successfulNumbers);
          failedNumbers.addAll(result.failedNumbers);
          break;

        case ZambianNetwork.airtel:
          final result = await _sendAirtelSMS(message, recipients, campaignId);
          successfulNumbers.addAll(result.successfulNumbers);
          failedNumbers.addAll(result.failedNumbers);
          break;

        case ZambianNetwork.zamtel:
          final result = await _sendZamtelSMS(message, recipients, campaignId);
          successfulNumbers.addAll(result.successfulNumbers);
          failedNumbers.addAll(result.failedNumbers);
          break;

        default:
          failedNumbers.addAll(recipients);
      }

      return LaunchNotificationResult(
        success: successfulNumbers.isNotEmpty,
        message: '${config['name']} notifications sent',
        totalRecipients: recipients.length,
        successfulSends: successfulNumbers.length,
        failedSends: failedNumbers.length,
        successfulNumbers: successfulNumbers,
        failedNumbers: failedNumbers,
      );

    } catch (e) {
      print('❌ Network sending failed for $network: $e');
      return LaunchNotificationResult(
        success: false,
        message: 'Network sending failed: ${e.toString()}',
        totalRecipients: recipients.length,
        successfulSends: 0,
        failedSends: recipients.length,
        successfulNumbers: [],
        failedNumbers: recipients,
      );
    }
  }

  /// Send SMS via MTN network
  static Future<LaunchNotificationResult> _sendMTNSMS(
    String message,
    List<String> recipients,
    String? campaignId,
  ) async {
    print('📱 Sending MTN SMS to ${recipients.length} recipients...');
    
    // Simulate MTN SMS API call
    await Future.delayed(Duration(milliseconds: 1000 + Random().nextInt(2000)));
    
    // Simulate 95% success rate
    final successfulNumbers = <String>[];
    final failedNumbers = <String>[];
    
    for (final number in recipients) {
      if (Random().nextDouble() < 0.95) {
        successfulNumbers.add(number);
        print('   ✅ MTN SMS sent to $number');
      } else {
        failedNumbers.add(number);
        print('   ❌ MTN SMS failed to $number');
      }
    }

    return LaunchNotificationResult(
      success: successfulNumbers.isNotEmpty,
      message: 'MTN SMS batch completed',
      totalRecipients: recipients.length,
      successfulSends: successfulNumbers.length,
      failedSends: failedNumbers.length,
      successfulNumbers: successfulNumbers,
      failedNumbers: failedNumbers,
    );
  }

  /// Send SMS via Airtel network
  static Future<LaunchNotificationResult> _sendAirtelSMS(
    String message,
    List<String> recipients,
    String? campaignId,
  ) async {
    print('📱 Sending Airtel SMS to ${recipients.length} recipients...');
    
    // Simulate Airtel SMS API call
    await Future.delayed(Duration(milliseconds: 800 + Random().nextInt(1500)));
    
    // Simulate 93% success rate
    final successfulNumbers = <String>[];
    final failedNumbers = <String>[];
    
    for (final number in recipients) {
      if (Random().nextDouble() < 0.93) {
        successfulNumbers.add(number);
        print('   ✅ Airtel SMS sent to $number');
      } else {
        failedNumbers.add(number);
        print('   ❌ Airtel SMS failed to $number');
      }
    }

    return LaunchNotificationResult(
      success: successfulNumbers.isNotEmpty,
      message: 'Airtel SMS batch completed',
      totalRecipients: recipients.length,
      successfulSends: successfulNumbers.length,
      failedSends: failedNumbers.length,
      successfulNumbers: successfulNumbers,
      failedNumbers: failedNumbers,
    );
  }

  /// Send SMS via Zamtel network
  static Future<LaunchNotificationResult> _sendZamtelSMS(
    String message,
    List<String> recipients,
    String? campaignId,
  ) async {
    print('📱 Sending Zamtel SMS to ${recipients.length} recipients...');
    
    // Simulate Zamtel SMS API call
    await Future.delayed(Duration(milliseconds: 1200 + Random().nextInt(1800)));
    
    // Simulate 90% success rate
    final successfulNumbers = <String>[];
    final failedNumbers = <String>[];
    
    for (final number in recipients) {
      if (Random().nextDouble() < 0.90) {
        successfulNumbers.add(number);
        print('   ✅ Zamtel SMS sent to $number');
      } else {
        failedNumbers.add(number);
        print('   ❌ Zamtel SMS failed to $number');
      }
    }

    return LaunchNotificationResult(
      success: successfulNumbers.isNotEmpty,
      message: 'Zamtel SMS batch completed',
      totalRecipients: recipients.length,
      successfulSends: successfulNumbers.length,
      failedSends: failedNumbers.length,
      successfulNumbers: successfulNumbers,
      failedNumbers: failedNumbers,
    );
  }

  /// Validate Zambian phone number
  static bool _isValidZambianNumber(String phoneNumber) {
    final cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Check various Zambian formats
    if (cleaned.startsWith('260') && cleaned.length == 12) {
      final prefix = cleaned.substring(3, 5);
      return ['96', '97', '95'].contains(prefix);
    } else if (cleaned.startsWith('0') && cleaned.length == 10) {
      final prefix = cleaned.substring(1, 3);
      return ['96', '97', '95'].contains(prefix);
    } else if (cleaned.length == 9) {
      final prefix = cleaned.substring(0, 2);
      return ['96', '97', '95'].contains(prefix);
    }
    
    return false;
  }

  /// Format Zambian phone number
  static String formatZambianNumber(String phoneNumber) {
    final cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleaned.startsWith('260')) {
      return '+$cleaned';
    } else if (cleaned.startsWith('0') && cleaned.length == 10) {
      return '+260${cleaned.substring(1)}';
    } else if (cleaned.length == 9) {
      return '+260$cleaned';
    }
    
    return phoneNumber;
  }

  /// Get network display name
  static String getNetworkDisplayName(ZambianNetwork network) {
    return _networkConfigs[network]?['name'] ?? 'Unknown Network';
  }
}

class RequestValidation {
  final bool isValid;
  final String? message;

  RequestValidation(this.isValid, this.message);
}
