# 🇿🇲 QR Payment Testing Guide for Zambian Mobile Money

## Overview

This guide provides comprehensive testing commands and procedures for the Zambian QR payment system, optimized for local market conditions including low-end devices, poor connectivity, and rural environments.

## 🚀 Quick Start

### Run All Tests
```bash
# Complete test suite with default settings
./run_qr_tests.sh

# Production environment testing
./run_qr_tests.sh --environment=zm_prod --verbose
```

## 📋 Test Commands

### 1. QR Generation Tests
Tests QR code generation, validation, and format compliance for Zambian market.

```bash
# Basic generation tests
flutter test test/qr/generation_test.dart --dart-define=ENV=zm_prod

# Test with specific environment
flutter test test/qr/generation_test.dart --dart-define=ENV=test

# Verbose output
flutter test test/qr/generation_test.dart --dart-define=ENV=zm_prod --verbose
```

**What it tests:**
- ✅ Zambian QR format compliance
- ✅ Amount validation (K5 - K5,000)
- ✅ Provider detection (MTN/Airtel/Zamtel)
- ✅ 2G network optimization
- ✅ Merchant category recommendations
- ✅ Performance benchmarks

### 2. QR Scanning Tests
Tests QR scanning on various devices and lighting conditions common in Zambia.

```bash
# Default scanning tests
./test_qr_scanning.sh \
  --devices="Tecno Spark 7,Itel P40" \
  --light-conditions="low,normal" \
  --amounts="10,250,5000"

# Extended device testing
./test_qr_scanning.sh \
  --devices="Tecno Spark 7,Itel P40,Samsung Galaxy A12,Infinix Hot 10" \
  --light-conditions="low,normal,bright,outdoor" \
  --amounts="5,10,15,25,50,100,250,500,1000,2500,5000"

# Quick test for specific conditions
./test_qr_scanning.sh \
  --devices="Itel P40" \
  --light-conditions="low" \
  --amounts="10,50,100"
```

**What it tests:**
- 📱 Low-end device compatibility (Tecno, Itel, Infinix)
- 💡 Poor lighting conditions (rural environments)
- 📷 Camera quality variations
- ⚡ Performance on 1GB-4GB RAM devices
- 🔋 Battery optimization during scanning

### 3. Offline Handling Tests
Simulates network outages and validates offline transaction processing.

```bash
# Default offline simulation
simulate_offline_qr \
  --transactions=15 \
  --network-outage=48h \
  --validate-receipts

# Extended offline testing
simulate_offline_qr \
  --transactions=50 \
  --network-outage=72h \
  --validate-receipts \
  --output-dir=extended_offline_results

# Quick offline test
simulate_offline_qr \
  --transactions=10 \
  --network-outage=2h \
  --validate-receipts
```

**What it tests:**
- 🌐 Network outage simulation (2G/3G/4G)
- 📦 Offline transaction queuing
- 🧾 Receipt generation (SMS + Visual)
- 🔄 Automatic sync when online
- 💾 Local data persistence

## 🎯 Test Scenarios

### Zambian Market Conditions

#### Low-End Device Testing
```bash
# Test on budget Android devices (1-2GB RAM)
./test_qr_scanning.sh \
  --devices="Itel P40,Tecno Spark 7" \
  --light-conditions="low,normal" \
  --amounts="10,25,50,100"
```

#### Rural Connectivity Testing
```bash
# Simulate poor network conditions
simulate_offline_qr \
  --transactions=25 \
  --network-outage=48h \
  --validate-receipts
```

#### Mobile Money Provider Testing
```bash
# Test all Zambian providers
flutter test test/qr/generation_test.dart \
  --dart-define=ENV=zm_prod \
  --dart-define=PROVIDERS="MTN,AIRTEL,ZAMTEL"
```

### Performance Testing

#### High Volume Testing
```bash
# Test with many transactions
simulate_offline_qr \
  --transactions=100 \
  --network-outage=24h \
  --validate-receipts
```

#### Stress Testing
```bash
# Extended scanning test
./test_qr_scanning.sh \
  --devices="Tecno Spark 7,Itel P40,Samsung Galaxy A12" \
  --light-conditions="low,normal,bright" \
  --amounts="5,10,15,20,25,30,50,75,100,150,200,250,500,1000,2500,5000" \
  --duration=600
```

## 📊 Test Results

### Expected Success Rates

| Test Type | Minimum Success Rate | Target Success Rate |
|-----------|---------------------|-------------------|
| QR Generation | 100% | 100% |
| QR Scanning (Good Light) | 95% | 98% |
| QR Scanning (Low Light) | 80% | 90% |
| Offline Sync | 95% | 98% |
| Receipt Generation | 100% | 100% |

### Device Performance Targets

| Device Category | RAM | Expected Performance |
|----------------|-----|-------------------|
| Budget (Itel P40) | 1GB | QR scan < 3s |
| Entry (Tecno Spark 7) | 2GB | QR scan < 2s |
| Mid-range (Galaxy A12) | 4GB | QR scan < 1s |

## 🔧 Test Configuration

### Environment Variables
```bash
# Production environment
export ENV=zm_prod

# Test environment
export ENV=test

# Debug mode
export DEBUG=true
```

### Device Setup
```bash
# Enable developer options
adb shell settings put global development_settings_enabled 1

# Grant camera permissions
adb shell pm grant com.zambiapay.app android.permission.CAMERA

# Set screen brightness for testing
adb shell settings put system screen_brightness 128
```

## 📱 Supported Test Devices

### Budget Devices (Common in Zambia)
- **Itel P40**: 1GB RAM, Android 10, Basic camera
- **Tecno Spark 7**: 2GB RAM, Android 11, Average camera
- **Infinix Hot 10**: 3GB RAM, Android 10, Decent camera

### Mid-Range Devices
- **Samsung Galaxy A12**: 4GB RAM, Android 11, Good camera
- **Huawei Y7a**: 4GB RAM, Android 10, Good camera

### Test Requirements
- Android 8.0+ (API level 26+)
- Camera permission
- Internet connectivity (for online tests)
- ADB debugging enabled

## 🌍 Network Conditions

### Zambian Network Simulation
```bash
# 2G conditions (rural areas)
simulate_offline_qr --network-outage=72h

# 3G conditions (urban areas)
simulate_offline_qr --network-outage=24h

# 4G conditions (major cities)
simulate_offline_qr --network-outage=2h
```

### Provider-Specific Testing
- **MTN**: Most coverage, test with various amounts
- **Airtel**: Good urban coverage, test transaction limits
- **Zamtel**: Limited coverage, test offline capabilities

## 🚨 Troubleshooting

### Common Issues

#### QR Generation Fails
```bash
# Check environment setup
flutter doctor

# Verify dependencies
flutter pub get

# Run with verbose output
flutter test test/qr/generation_test.dart --verbose
```

#### Device Not Detected
```bash
# Check ADB connection
adb devices

# Restart ADB server
adb kill-server && adb start-server

# Check USB debugging
adb shell settings get global development_settings_enabled
```

#### Offline Tests Fail
```bash
# Check Python dependencies
python3 --version
pip3 install sqlite3

# Verify permissions
chmod +x simulate_offline_qr

# Run with debug output
python3 simulate_offline_qr --transactions=5 --network-outage=1h --validate-receipts
```

## 📈 Continuous Integration

### GitHub Actions Example
```yaml
name: QR Payment Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
      - run: flutter pub get
      - run: flutter test test/qr/generation_test.dart --dart-define=ENV=test
      - run: python3 simulate_offline_qr --transactions=10 --network-outage=1h --validate-receipts
```

### Local CI Script
```bash
#!/bin/bash
# ci_test.sh - Run essential tests for CI

./run_qr_tests.sh \
  --environment=test \
  --devices="Emulator" \
  --light-conditions="normal" \
  --amounts="10,50,100" \
  --offline-transactions=10 \
  --network-outage=1h
```

## 📋 Test Checklist

### Pre-Release Testing
- [ ] QR generation tests pass (100%)
- [ ] Scanning tests pass on budget devices (>80%)
- [ ] Offline handling works for 48h+ outages
- [ ] All Zambian providers supported
- [ ] Amount validation works correctly
- [ ] Receipt generation functional
- [ ] Anti-fraud protection active
- [ ] Performance meets targets

### Production Deployment
- [ ] All tests pass in zm_prod environment
- [ ] Device compatibility verified
- [ ] Network resilience tested
- [ ] Security validation complete
- [ ] Documentation updated
- [ ] Monitoring configured

## 🆘 Support

For testing issues:
- **Email**: <EMAIL>
- **Documentation**: [docs.paymule.zm/testing](https://docs.paymule.zm/testing)
- **Issues**: Create GitHub issue with test logs

---

**Built for Zambia 🇿🇲 | Tested on Local Devices | Optimized for Rural Conditions**
